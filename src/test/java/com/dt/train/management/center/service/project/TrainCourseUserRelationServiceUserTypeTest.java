package com.dt.train.management.center.service.project;

import com.dt.train.management.center.enums.UserTypeEnum;
import com.dt.train.management.center.mapper.TrainCourseUserRelationDOMapper;
import com.dt.train.management.center.mapper.TrainUserDOMapper;
import com.dt.train.management.center.model.dao.TrainCourseUserRelationDO;
import com.dt.train.management.center.model.dao.TrainUserDO;
import com.dt.train.management.center.model.dto.project.TrainCourseUserRelationDTO;
import com.dt.train.management.center.model.dto.project.TrainCourseUserRelationSaveOrUpdateDTO;
import com.dt.train.management.center.service.user.UserStatusManager;
import com.dt.train.management.center.utils.HalfDayTime;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Date;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class TrainCourseUserRelationServiceUserTypeTest {

    @Mock
    private TrainCourseUserRelationDOMapper trainCourseUserRelationDOMapper;

    @Mock
    private TrainTaskService trainTaskService;

    @Mock
    private UserStatusManager userStatusManager;

    @Mock
    private TrainUserDOMapper trainUserDOMapper;

    @InjectMocks
    private TrainCourseUserRelationService trainCourseUserRelationService;

    @BeforeEach
    void setUp() {
        when(trainCourseUserRelationDOMapper.insertSelective(any(TrainCourseUserRelationDO.class))).thenReturn(1);
        when(userStatusManager.getUserStatus(anyString())).thenReturn(0);
    }

    @Test
    void testSaveDTO2DO_WithPartTimeUser_SetsCorrectUserType() {
        // 准备测试数据
        TrainCourseUserRelationSaveOrUpdateDTO saveDTO = new TrainCourseUserRelationSaveOrUpdateDTO();
        saveDTO.setUserId(1001L);
        saveDTO.setUserName("兼职人员1");
        saveDTO.setStartDate(new HalfDayTime(new Date()));
        saveDTO.setEndDate(new HalfDayTime(new Date()));

        // Mock 兼职用户
        TrainUserDO partTimeUser = new TrainUserDO();
        partTimeUser.setUserId(1001L);
        partTimeUser.setUserType(UserTypeEnum.PART_TIME.getCode());
        when(trainUserDOMapper.selectByUserId(1001L)).thenReturn(partTimeUser);

        // 执行测试 - 通过反射调用私有方法
        try {
            java.lang.reflect.Method method = TrainCourseUserRelationService.class
                .getDeclaredMethod("saveDTO2DO", TrainCourseUserRelationSaveOrUpdateDTO.class);
            method.setAccessible(true);
            TrainCourseUserRelationDO result = (TrainCourseUserRelationDO) method.invoke(trainCourseUserRelationService, saveDTO);

            // 验证结果
            assertEquals(UserTypeEnum.PART_TIME.getCode(), result.getUserType());
            assertEquals(1001L, result.getUserId());
            assertEquals("兼职人员1", result.getUserName());
        } catch (Exception e) {
            fail("反射调用失败: " + e.getMessage());
        }
    }

    @Test
    void testSaveDTO2DO_WithFullTimeUser_SetsCorrectUserType() {
        // 准备测试数据
        TrainCourseUserRelationSaveOrUpdateDTO saveDTO = new TrainCourseUserRelationSaveOrUpdateDTO();
        saveDTO.setUserId(1002L);
        saveDTO.setUserName("正式员工");
        saveDTO.setStartDate(new HalfDayTime(new Date()));
        saveDTO.setEndDate(new HalfDayTime(new Date()));

        // Mock 正式员工
        TrainUserDO fullTimeUser = new TrainUserDO();
        fullTimeUser.setUserId(1002L);
        fullTimeUser.setUserType(UserTypeEnum.FULL_TIME.getCode());
        when(trainUserDOMapper.selectByUserId(1002L)).thenReturn(fullTimeUser);

        // 执行测试
        try {
            java.lang.reflect.Method method = TrainCourseUserRelationService.class
                .getDeclaredMethod("saveDTO2DO", TrainCourseUserRelationSaveOrUpdateDTO.class);
            method.setAccessible(true);
            TrainCourseUserRelationDO result = (TrainCourseUserRelationDO) method.invoke(trainCourseUserRelationService, saveDTO);

            // 验证结果
            assertEquals(UserTypeEnum.FULL_TIME.getCode(), result.getUserType());
            assertEquals(1002L, result.getUserId());
            assertEquals("正式员工", result.getUserName());
        } catch (Exception e) {
            fail("反射调用失败: " + e.getMessage());
        }
    }

    @Test
    void testConvertToDTO_SetsUserTypeName() {
        // 准备测试数据
        TrainCourseUserRelationDO relationDO = new TrainCourseUserRelationDO();
        relationDO.setUserId(1001L);
        relationDO.setUserName("兼职人员1");
        relationDO.setUserType(UserTypeEnum.PART_TIME.getCode());
        relationDO.setStartDate(new Date());
        relationDO.setEndDate(new Date());

        // 执行测试
        try {
            java.lang.reflect.Method method = TrainCourseUserRelationService.class
                .getDeclaredMethod("convertToDTO", TrainCourseUserRelationDO.class);
            method.setAccessible(true);
            TrainCourseUserRelationDTO result = (TrainCourseUserRelationDTO) method.invoke(trainCourseUserRelationService, relationDO);

            // 验证结果
            assertEquals(UserTypeEnum.PART_TIME.getCode(), result.getUserType());
            assertEquals("兼职人员", result.getUserTypeName());
            assertEquals(1001L, result.getUserId());
            assertEquals("兼职人员1", result.getUserName());
        } catch (Exception e) {
            fail("反射调用失败: " + e.getMessage());
        }
    }
}
