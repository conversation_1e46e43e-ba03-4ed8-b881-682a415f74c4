package com.dt.train.management.center.manager;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

import java.util.Collections;
import java.util.List;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import com.dt.train.management.center.config.UserRequestContextHolder;
import com.dt.train.management.center.enums.ManagementExceptionEnum;
import com.dt.train.management.center.exception.ManagementBusinessException;
import com.dt.train.management.center.mapper.TrainUserDOMapper;
import com.dt.train.management.center.model.dao.TrainUserDO;
import com.dt.train.management.center.model.dto.user.TrainUserSaveDTO;
import com.dt.train.management.center.service.project.TrainCourseUserRelationService;
import com.dt.train.management.center.service.user.TrainUserService;
import com.dt.train.management.center.service.user.UserAndDepartmentMaintenanceService;
import com.dt.train.management.center.utils.RedisUtil;

@ExtendWith(MockitoExtension.class)
class TrainUserManagerTest {

    @Mock
    private TrainUserService trainUserService;

    @Mock
    private TrainUserDOMapper trainUserDOMapper;

    @Mock
    private TrainCourseUserRelationService trainCourseUserRelationService;

    @Mock
    private RedisUtil redisUtil;

    @Mock
    private UserAndDepartmentMaintenanceService userAndDepartmentMaintenanceService;

    @InjectMocks
    private TrainUserManager trainUserManager;

    @BeforeEach
    void setUp() {
        // 设置当前用户上下文
        UserRequestContextHolder.setRequestUserId(1L);
    }

    @Test
    void testSaveOrUpdateUser_NewUser_Success() {
        // 准备测试数据
        TrainUserSaveDTO saveDTO = new TrainUserSaveDTO();
        saveDTO.setUserName("测试用户");
        saveDTO.setPhone("***********");
        saveDTO.setDeptId("1001");
        saveDTO.setRoleId(1);
        saveDTO.setUserPosition("测试职位");
        saveDTO.setWorkPlace("测试地点");

        // Mock 依赖方法
        when(trainUserDOMapper.selectByPhones(anyList())).thenReturn(Collections.emptyList());
        when(userAndDepartmentMaintenanceService.queryUserIdByMobile(anyString())).thenReturn(1001L);
        when(trainUserDOMapper.insertSelective(any(TrainUserDO.class))).thenReturn(1);

        // 执行测试
        assertDoesNotThrow(() -> trainUserManager.saveOrUpdateUser(saveDTO));

        // 验证调用
        verify(trainUserDOMapper).selectByPhones(Collections.singletonList("***********"));
        verify(userAndDepartmentMaintenanceService).queryUserIdByMobile("***********");
        verify(trainUserDOMapper).insertSelective(any(TrainUserDO.class));
    }

    @Test
    void testSaveOrUpdateUser_UpdateUser_Success() {
        // 准备测试数据
        TrainUserSaveDTO saveDTO = new TrainUserSaveDTO();
        saveDTO.setId(1);
        saveDTO.setUserName("更新用户");
        saveDTO.setPhone("***********");
        saveDTO.setDeptId("20000");
        saveDTO.setRoleId(2);

        TrainUserDO existUser = new TrainUserDO();
        existUser.setId(1);
        existUser.setUserId(1001L);
        existUser.setPhone("***********");

        // Mock 依赖方法
        when(trainUserDOMapper.selectByPrimaryKey(1)).thenReturn(existUser);
        when(trainUserDOMapper.selectByPhones(anyList())).thenReturn(Collections.singletonList(existUser));
        when(trainUserDOMapper.updateByPrimaryKeySelective(any(TrainUserDO.class))).thenReturn(1);

        // 执行测试
        assertDoesNotThrow(() -> trainUserManager.saveOrUpdateUser(saveDTO));

        // 验证调用
        verify(trainUserDOMapper).selectByPrimaryKey(1);
        verify(trainUserDOMapper).selectByPhones(Collections.singletonList("***********"));
        verify(trainUserDOMapper).updateByPrimaryKeySelective(any(TrainUserDO.class));
        verify(redisUtil).del(anyString());
    }

    @Test
    void testSaveOrUpdateUser_NullDTO_ThrowsException() {
        // 执行测试并验证异常
        ManagementBusinessException exception = assertThrows(
            ManagementBusinessException.class,
            () -> trainUserManager.saveOrUpdateUser(null)
        );
        
        assertEquals(ManagementExceptionEnum.BAD_PARAMETER.getCode(), exception.getCode());
    }

    @Test
    void testSaveOrUpdateUser_EmptyUserName_ThrowsException() {
        // 准备测试数据
        TrainUserSaveDTO saveDTO = new TrainUserSaveDTO();
        saveDTO.setUserName("");
        saveDTO.setPhone("***********");
        saveDTO.setDeptId("1001");
        saveDTO.setRoleId(1);

        // 执行测试并验证异常
        ManagementBusinessException exception = assertThrows(
            ManagementBusinessException.class,
            () -> trainUserManager.saveOrUpdateUser(saveDTO)
        );
        
        assertEquals(ManagementExceptionEnum.BAD_PARAMETER.getCode(), exception.getCode());
        assertTrue(exception.getMessage().contains("用户姓名不能为空"));
    }

    @Test
    void testSaveOrUpdateUser_PhoneAlreadyExists_ThrowsException() {
        // 准备测试数据
        TrainUserSaveDTO saveDTO = new TrainUserSaveDTO();
        saveDTO.setUserName("测试用户");
        saveDTO.setPhone("***********");
        saveDTO.setDeptId("1001");
        saveDTO.setRoleId(1);

        TrainUserDO existUser = new TrainUserDO();
        existUser.setId(999);
        existUser.setPhone("***********");

        // Mock 依赖方法
        when(trainUserDOMapper.selectByPhones(anyList())).thenReturn(Collections.singletonList(existUser));

        // 执行测试并验证异常
        ManagementBusinessException exception = assertThrows(
            ManagementBusinessException.class,
            () -> trainUserManager.saveOrUpdateUser(saveDTO)
        );
        
        assertEquals(ManagementExceptionEnum.BAD_PARAMETER.getCode(), exception.getCode());
        assertTrue(exception.getMessage().contains("手机号已存在"));
    }
}
