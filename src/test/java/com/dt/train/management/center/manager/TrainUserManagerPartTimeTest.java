package com.dt.train.management.center.manager;

import com.dt.train.management.center.enums.UserTypeEnum;
import com.dt.train.management.center.mapper.TrainUserDOMapper;
import com.dt.train.management.center.model.dao.TrainUserDO;
import com.dt.train.management.center.model.dto.user.TrainUserSaveDTO;
import com.dt.train.management.center.service.cache.DeptCacheService;
import com.dt.train.management.center.service.project.TrainCourseUserRelationService;
import com.dt.train.management.center.service.user.TrainUserService;
import com.dt.train.management.center.service.user.UserAndDepartmentMaintenanceService;
import com.dt.train.management.center.utils.RedisUtil;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Collections;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class TrainUserManagerPartTimeTest {

    @Mock
    private TrainUserService trainUserService;

    @Mock
    private TrainUserDOMapper trainUserDOMapper;

    @Mock
    private TrainCourseUserRelationService trainCourseUserRelationService;

    @Mock
    private RedisUtil redisUtil;

    @Mock
    private UserAndDepartmentMaintenanceService userAndDepartmentMaintenanceService;

    @Mock
    private DeptCacheService deptCacheService;

    @InjectMocks
    private TrainUserManager trainUserManager;

    @BeforeEach
    void setUp() {
        // 设置通用的mock行为
        when(trainUserDOMapper.selectAllByPhones(anyList())).thenReturn(Collections.emptyList());
        when(userAndDepartmentMaintenanceService.queryUserIdByMobile(anyString())).thenReturn(1001L);
        when(trainUserDOMapper.insertSelective(any(TrainUserDO.class))).thenReturn(1);
    }

    @Test
    void testCreatePartTimeUser_WithoutPhoneAndRole_Success() {
        // 准备测试数据 - 兼职人员，无手机号和角色
        TrainUserSaveDTO saveDTO = new TrainUserSaveDTO();
        saveDTO.setUserType(UserTypeEnum.PART_TIME.getCode()); // 兼职人员
        saveDTO.setDeptId("10000");
        saveDTO.setUserPosition("临时工作");
        saveDTO.setWorkPlace("测试地点");
        // 不设置userName、phone、roleId

        // Mock 部门下兼职人员数量
        when(trainUserDOMapper.countPartTimeUsersByDeptId("10000")).thenReturn(2);

        // 执行测试
        assertDoesNotThrow(() -> trainUserManager.saveOrUpdateUser(saveDTO));

        // 验证调用
        verify(trainUserDOMapper).countPartTimeUsersByDeptId("10000");
        verify(trainUserDOMapper).insertSelective(argThat(userDO -> {
            // 验证自动生成的名字
            assertEquals("兼职人员3", userDO.getUserName());
            // 验证用户类型
            assertEquals(UserTypeEnum.PART_TIME.getCode(), userDO.getUserType());
            // 验证手机号和角色可以为空
            assertNull(userDO.getPhone());
            assertNull(userDO.getRoleId());
            // 验证管理区域ID与所属区域ID相同
            assertEquals("10000", userDO.getManageDeptIds());
            assertEquals("10000", userDO.getDeptIds());
            return true;
        }));
    }

    @Test
    void testCreatePartTimeUser_WithCustomName_Success() {
        // 准备测试数据 - 兼职人员，有自定义名字
        TrainUserSaveDTO saveDTO = new TrainUserSaveDTO();
        saveDTO.setUserName("自定义兼职人员名字");
        saveDTO.setUserType(UserTypeEnum.PART_TIME.getCode());
        saveDTO.setDeptId("10000");

        // 执行测试
        assertDoesNotThrow(() -> trainUserManager.saveOrUpdateUser(saveDTO));

        // 验证不会自动生成名字
        verify(trainUserDOMapper, never()).countPartTimeUsersByDeptId(anyString());
        verify(trainUserDOMapper).insertSelective(argThat(userDO -> {
            assertEquals("自定义兼职人员名字", userDO.getUserName());
            // 验证管理区域ID与所属区域ID相同
            assertEquals("10000", userDO.getManageDeptIds());
            assertEquals("10000", userDO.getDeptIds());
            return true;
        }));
    }

    @Test
    void testCreatePartTimeUser_WithPhone_Success() {
        // 准备测试数据 - 兼职人员，有手机号
        TrainUserSaveDTO saveDTO = new TrainUserSaveDTO();
        saveDTO.setUserType(UserTypeEnum.PART_TIME.getCode());
        saveDTO.setDeptId("10000");
        saveDTO.setPhone("13800138000");

        // Mock 部门下兼职人员数量
        when(trainUserDOMapper.countPartTimeUsersByDeptId("10000")).thenReturn(0);

        // 执行测试
        assertDoesNotThrow(() -> trainUserManager.saveOrUpdateUser(saveDTO));

        // 验证
        verify(trainUserDOMapper).insertSelective(argThat(userDO -> {
            assertEquals("兼职人员1", userDO.getUserName());
            assertEquals("13800138000", userDO.getPhone());
            assertEquals(UserTypeEnum.PART_TIME.getCode(), userDO.getUserType());
            return true;
        }));
    }

    @Test
    void testCreateFullTimeUser_RequiresPhoneAndRole() {
        // 准备测试数据 - 正式员工，必须有手机号和角色
        TrainUserSaveDTO saveDTO = new TrainUserSaveDTO();
        saveDTO.setUserName("正式员工");
        saveDTO.setUserType(UserTypeEnum.FULL_TIME.getCode());
        saveDTO.setDeptId("10000");
        saveDTO.setPhone("13800138001");
        saveDTO.setRoleId(1);

        // 执行测试
        assertDoesNotThrow(() -> trainUserManager.saveOrUpdateUser(saveDTO));

        // 验证
        verify(trainUserDOMapper).insertSelective(argThat(userDO -> {
            assertEquals("正式员工", userDO.getUserName());
            assertEquals("13800138001", userDO.getPhone());
            assertEquals(Integer.valueOf(1), userDO.getRoleId());
            assertEquals(UserTypeEnum.FULL_TIME.getCode(), userDO.getUserType());
            return true;
        }));
    }
}
