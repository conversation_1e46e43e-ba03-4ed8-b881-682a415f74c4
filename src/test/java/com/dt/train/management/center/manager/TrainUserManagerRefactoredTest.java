package com.dt.train.management.center.manager;

import com.dt.train.management.center.enums.UserTypeEnum;
import com.dt.train.management.center.mapper.TrainUserDOMapper;
import com.dt.train.management.center.model.dao.TrainUserDO;
import com.dt.train.management.center.model.dto.user.TrainUserSaveDTO;
import com.dt.train.management.center.service.cache.DeptCacheService;
import com.dt.train.management.center.service.project.TrainCourseUserRelationService;
import com.dt.train.management.center.service.user.TrainUserService;
import com.dt.train.management.center.service.user.UserAndDepartmentMaintenanceService;
import com.dt.train.management.center.utils.RedisUtil;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Collections;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class TrainUserManagerRefactoredTest {

    @Mock
    private TrainUserService trainUserService;

    @Mock
    private TrainUserDOMapper trainUserDOMapper;

    @Mock
    private TrainCourseUserRelationService trainCourseUserRelationService;

    @Mock
    private RedisUtil redisUtil;

    @Mock
    private UserAndDepartmentMaintenanceService userAndDepartmentMaintenanceService;

    @Mock
    private DeptCacheService deptCacheService;

    @InjectMocks
    private TrainUserManager trainUserManager;

    @BeforeEach
    void setUp() {
        when(trainUserDOMapper.selectAllByPhones(anyList())).thenReturn(Collections.emptyList());
        when(userAndDepartmentMaintenanceService.queryUserIdByMobile(any())).thenReturn(1001L);
        when(trainUserDOMapper.insertSelective(any(TrainUserDO.class))).thenReturn(1);
        when(trainUserDOMapper.countPartTimeUsersByDeptId(anyString())).thenReturn(0);
    }

    @Test
    void testCreatePartTimeUser_RefactoredCode_Success() {
        // 准备测试数据
        TrainUserSaveDTO saveDTO = new TrainUserSaveDTO();
        saveDTO.setUserType(UserTypeEnum.PART_TIME.getCode());
        saveDTO.setDeptId("10000");

        // 执行测试
        assertDoesNotThrow(() -> trainUserManager.saveOrUpdateUser(saveDTO));

        // 验证调用
        verify(trainUserDOMapper).countPartTimeUsersByDeptId("10000");
        verify(trainUserDOMapper).insertSelective(argThat(userDO -> {
            assertEquals("兼职人员1", userDO.getUserName());
            assertEquals(UserTypeEnum.PART_TIME.getCode(), userDO.getUserType());
            assertEquals("", userDO.getManageDeptIds());
            return true;
        }));
    }

    @Test
    void testCreateFullTimeUser_RefactoredCode_Success() {
        // 准备测试数据
        TrainUserSaveDTO saveDTO = new TrainUserSaveDTO();
        saveDTO.setUserName("正式员工");
        saveDTO.setUserType(UserTypeEnum.FULL_TIME.getCode());
        saveDTO.setDeptId("10000");
        saveDTO.setManageDeptId("10000;10001");
        saveDTO.setPhone("13800138000");
        saveDTO.setRoleId(1);

        // 执行测试
        assertDoesNotThrow(() -> trainUserManager.saveOrUpdateUser(saveDTO));

        // 验证调用
        verify(trainUserDOMapper).insertSelective(argThat(userDO -> {
            assertEquals("正式员工", userDO.getUserName());
            assertEquals(UserTypeEnum.FULL_TIME.getCode(), userDO.getUserType());
            assertEquals("10000;10001", userDO.getManageDeptIds());
            assertEquals("13800138000", userDO.getPhone());
            assertEquals(Integer.valueOf(1), userDO.getRoleId());
            return true;
        }));
    }

    @Test
    void testUpdateUser_RefactoredCode_Success() {
        // 准备测试数据
        TrainUserSaveDTO saveDTO = new TrainUserSaveDTO();
        saveDTO.setId(1);
        saveDTO.setUserName("更新用户");
        saveDTO.setUserType(UserTypeEnum.FULL_TIME.getCode());
        saveDTO.setDeptId("10000");
        saveDTO.setPhone("13800138001");

        TrainUserDO existUser = new TrainUserDO();
        existUser.setId(1);
        existUser.setUserId(1001L);
        existUser.setDeptIds("10000");

        // Mock 依赖方法
        when(trainUserDOMapper.selectByPrimaryKey(1)).thenReturn(existUser);
        when(trainUserDOMapper.selectByPhones(anyList())).thenReturn(Collections.singletonList(existUser));
        when(trainUserDOMapper.updateByPrimaryKeySelective(any(TrainUserDO.class))).thenReturn(1);

        // 执行测试
        assertDoesNotThrow(() -> trainUserManager.saveOrUpdateUser(saveDTO));

        // 验证调用
        verify(trainUserDOMapper).updateByPrimaryKeySelective(argThat(userDO -> {
            assertEquals("更新用户", userDO.getUserName());
            assertEquals(Integer.valueOf(1), userDO.getId());
            return true;
        }));
    }
}
