# Apollo configuration is required, and general technical related content is placed here, such as system configuration, third-party plug-in configuration, etc.

# datasource - - -- - -- - -- - -- - -- - -- - -- - -- - -- - -- - -- - -- - -- - -- - -
spring.datasource.druid.url=**************************************
spring.datasource.druid.username=srt_dt
spring.datasource.druid.password=t81Qxr2EZ8ws
spring.datasource.druid.filters=stat
spring.datasource.druid.maxActive=20
spring.datasource.druid.initialSize=10
spring.datasource.druid.maxWait=60000
spring.datasource.druid.minIdle=1
spring.datasource.druid.driver-class-name=com.mysql.jdbc.Driver
spring.datasource.druid.timeBetweenEvictionRunsMillis=60000
spring.datasource.druid.minEvictableIdleTimeMillis=300000
spring.datasource.druid.testWhileIdle=true
spring.datasource.druid.testOnBorrow=false
spring.datasource.druid.testOnReturn=false
spring.datasource.druid.poolPreparedStatements=true
spring.datasource.druid.maxOpenPreparedStatements=20
spring.datasource.druid.validationQuery=SELECT 1

# redis - - -- - -- - -- - -- - -- - -- - -- - -- - -- - -- - -- - -- - -- - -- - - - -- - - - -- - - - -- - -
#spring.redis.host = ***********
#spring.redis.port = 6389
# spring.redis.sentinel.master=mymaster
# spring.redis.sentinel.nodes=***********:26389,***********:26389,***********:26389

spring.redis.lettuce.pool.min-idle = 8
spring.redis.lettuce.pool.max-idle = 100
spring.redis.lettuce.pool.max-active = 1000
spring.redis.lettuce.pool.max-wait = 1000ms

# consul - - -- - -- - -- - -- - -- - -- - -- - -- - -- - -- - -- - -- - -- - -- - - - -- - - - -- - - - -- - -
spring.cloud.consul.port=8500
spring.cloud.consul.host=*************
spring.cloud.consul.discovery.query-passing=true
spring.cloud.consul.discovery.tags[0]=profile=${env}
spring.cloud.consul.discovery.prefer-ip-address=true
spring.cloud.consul.discovery.catalogServicesWatchDelay=1000
spring.cloud.consul.discovery.catalog-services-watch-timeout=60

# rabbitmq - - -- - -- - -- - -- - -- - -- - -- - -- - -- - -- - -- - -- - -- - -- - - - -- - - - -- - - - -- - -
spring.rabbitmq.addresses=***********:5672
spring.rabbitmq.username=admin
spring.rabbitmq.password=7mjB0j3n9ZLu

# actuator - - -- - -- - -- - -- - -- - -- - -- - -- - -- - -- - -- - -- - -- - -- - - - -- - - - -- - - - -- - -
management.endpoints.web.exposure.include=*
management.endpoint.health.show-details=always

# tomcat access log - -- - -- - -- - -- - -- - -- - -- - -- - -- - -- - - - -- - - - -- - - - -- - -- -- - -- -
server.tomcat.basedir=logs/access_log
server.tomcat.accesslog.enabled=true
server.tomcat.accesslog.pattern=%t %a "%r" %s (%D ms)

# close zipkin - - -- - -- - -- - -- - -- - -- - -- - -- - -- - -- - -- - - - -- - - - -- - - - -- - -- -- -- -- -
spring.sleuth.function.enabled=false
spring.zipkin.enabled=false

# mybatis - - -- - -- - -- - -- - -- - -- - -- - -- - -- - -- - -- - - - -- - - - -- - - - -- - -- -- -- -- - ----
mybatis.mapper-locations=classpath:mapper/*.xml


