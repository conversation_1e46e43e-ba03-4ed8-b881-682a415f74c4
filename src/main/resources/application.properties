# Content that does not require Apollo configuration is placed here
# Do not modify this file easily

# server
server.port=31270
spring.application.name=train-management-center

# apollo
app.id=${spring.application.name}
apollo.bootstrap.enabled=true
apollo.bootstrap.namespaces=application, business, DT.COMMON, DT.ENV_VAR
apollo.cacheDir=./apollo-cache

# temporarily put the configuration here first, then put it in apollo, and then delete these configurations from here
spring.cloud.consul.discovery.query-passing = true
spring.cloud.loadbalancer.cache.ttl = 10s


