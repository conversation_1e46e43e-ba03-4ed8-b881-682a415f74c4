
#set environment value: spring.profiles.active=dev to make this file available

#apollo.meta=http://***********:8080

apollo.bootstrap.enabled=false

spring.cloud.consul.discovery.health-check-critical-timeout=5s

logging.level.com.ctrip=error
logging.level.org.apache.http.wire=warn
logging.level.org.apache.http.headers=warn
logging.level.reactor.netty.channel=warn
logging.level.com.dt.framework.core.mvc.AccessLogFilter=debug

#mybatis.configuration.log-impl=org.apache.ibatis.logging.stdout.StdOutImpl
#logging.level.org.springframework=WARN
#logging.level.com.spring.ibatis.UserMapper=DEBUG