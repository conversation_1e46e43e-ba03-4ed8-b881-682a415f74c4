alter table `train_project` add column `teach_days` float(10,1) NULL DEFAULT NULL COMMENT '面授总天数' after `sync_type`;
alter table `train_project` add column `teach_course_count` int(10) NULL DEFAULT NULL COMMENT '面授班次' after `teach_days` ;
alter table `train_project` add column `teach_remark` varchar(900) NULL DEFAULT NULL COMMENT '面授备注说明' after `teach_course_count` ;
alter table `train_project` add column `project_helper_id` varchar(100) NULL DEFAULT NULL COMMENT '项目协助人,多个人;分隔' after `teach_remark` ;
alter table `train_project` add column `project_helper_name` varchar(100) NULL DEFAULT NULL COMMENT '项目协助人名称,多个人;分隔' after `project_helper_id` ;

alter table `train_project` add column `live_editor_id` varchar(100) NULL DEFAULT NULL COMMENT '学支编辑,多个人;分隔' after `project_helper_name` ;
alter table `train_project` add column `live_editor_name` varchar(100) NULL DEFAULT NULL COMMENT '学支编辑名称,多个人;分隔' after `live_editor_id` ;
-- 最后同步crm时间 --
alter table `train_project` add column `sync_time` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) COMMENT '最后同步crm时间' after `live_editor_name` ;

update train_project set sync_time = ts where 1=1;