-- 为train_user表添加成员类型字段
-- 0: 正式员工（默认值）
-- 1: 兼职人员

ALTER TABLE train_management_center.train_user
ADD COLUMN `user_type` tinyint(1) NOT NULL DEFAULT 0 COMMENT '成员类型，0-正式员工，1-兼职人员'
AFTER `user_status`;

-- 为train_course_user_relation表添加成员类型字段
-- 0: 正式员工（默认值）
-- 1: 兼职人员

ALTER TABLE train_management_center.train_course_user_relation
ADD COLUMN `user_type` tinyint(1) NOT NULL DEFAULT 0 COMMENT '成员类型，0-正式员工，1-兼职人员'
AFTER `user_name`;

-- 更新现有数据的用户类型
-- 将train_course_user_relation表中的用户类型与train_user表同步

UPDATE train_management_center.train_course_user_relation tcur
INNER JOIN train_management_center.train_user tu ON tcur.user_id = tu.user_id
SET tcur.user_type = tu.user_type
WHERE tcur.invalid = 0 AND tu.invalid = 0;

ALTER TABLE train_management_center.train_project modify column `service_content`  varchar(500) NOT NULL DEFAULT '' COMMENT '服务内容';

ALTER TABLE train_management_center.train_course_user_relation add column `origin_user_name` varchar(30) NOT NULL DEFAULT '' COMMENT '用户原始名称' after `user_name`;

-- 为sys_permission表添加备注字段
ALTER TABLE train_management_center.sys_permission
ADD COLUMN `remark` varchar(255) DEFAULT NULL COMMENT '备注信息'
AFTER `can_select`;

INSERT INTO train_management_center.sys_permission (id, parent_id, permission_name, permission_type, permission_key, sort_order, can_select, remark) VALUES
(10606, 106, '排班全权限', 'FUNCTION', 'project:course-detail:all', 6, 1, "允许在任何状态下修改班次所有信息，支持补录历史班次及人员");