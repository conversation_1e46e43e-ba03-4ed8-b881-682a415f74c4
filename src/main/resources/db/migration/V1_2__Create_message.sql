CREATE TABLE `system_message` (
  `id` int(10) NOT NULL AUTO_INCREMENT COMMENT '主键ID，仅做主键，业务字段统一使用user_id',
  `user_id` bigint(20) NOT NULL COMMENT '用户中心-用户ID',
  `message_title` VARCHAR(100) NOT NULL default '' COMMENT '消息标题',
  `message_content` VARCHAR(1000) NOT NULL COMMENT '消息内容',
  `message_type` tinyint(2) NOT NULL COMMENT '消息类型',
  `is_read` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否已读，0-否，1-是',
  `business_id` bigint(20) NOT NULL COMMENT '业务ID',
  `business_type` tinyint(2) NOT NULL COMMENT '业务类型, 1-课程',
  `ext_info` varchar(1000) DEFAULT NULL COMMENT '扩展信息',
  `invalid` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否无效，0-否，1-是',
  `gmt_create` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) COMMENT '创建时间',
  PRIMARY KEY (`id`),
  INDEX `idx_user_id` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='系统消息表';

alter table `train_project` add column `total_user_count` int(10) NULL DEFAULT NULL COMMENT '执行总人数' after `total_hours`;