CREATE TABLE if not exists `train_user` (
    `id` int(10) NOT NULL AUTO_INCREMENT COMMENT '主键ID，仅做主键，业务字段统一使用user_id',
    `user_id` bigint(20) NOT NULL COMMENT '用户中心-用户ID',
    `ding_talk_user_id`  varchar(128) DEFAULT NULL COMMENT '钉钉用户id',
    `crm_user_id`  varchar(128) DEFAULT NULL COMMENT 'CRM用户id',
    `dept_ids`  varchar(300) DEFAULT NULL COMMENT '所属部门id，使用;分隔',
    `user_name`  varchar(32) DEFAULT NULL COMMENT '姓名',
    `email`  varchar(128) DEFAULT NULL COMMENT '邮箱',
    `phone`  varchar(128) DEFAULT NULL COMMENT '手机号',
    `user_position`  varchar(128) DEFAULT NULL COMMENT '职位',
    `user_role`  varchar(128) DEFAULT NULL COMMENT '排班系统角色',
    `work_place`  varchar(128) DEFAULT NULL COMMENT '工作地点',
    `user_status`  tinyint(1) DEFAULT NULL COMMENT '状态,0-在职，1-离职',
    `invalid` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否已删除 0 否 1 是',
    `created_by`  bigint(20) DEFAULT NULL COMMENT '创建人',
    `updated_by`  bigint(20) DEFAULT NULL COMMENT '更新人',
    `gmt_create` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) COMMENT '创建时间',
    `gmt_modified` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) COMMENT '修改时间',
    `ts` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6) COMMENT '最后修改时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_user_id` (`user_id`) COMMENT '用户ID唯一索引',
    KEY `idx_ding_talk_crm_user_id` (`ding_talk_user_id`, `crm_user_id`) USING BTREE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='培训管理用户表';

CREATE TABLE if not exists `train_dept` (
    `id` int(10) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `parent_id` int(10) NOT NULL COMMENT '父级部门ID,0为顶级部门',
    `dept_name`  varchar(30) DEFAULT NULL COMMENT '部门名称',
    `dept_name_short`  varchar(30) DEFAULT NULL COMMENT '部门名称简称',
    `dept_type`  tinyint(2) DEFAULT NULL COMMENT '部门类型,0-超管部门，1-普通部门',
    `dept_level`  int(10) DEFAULT NULL COMMENT '部门层级',
    `hidden`  tinyint(1) DEFAULT NULL COMMENT '是否隐藏，0-不隐藏，1-隐藏',
    `invalid` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否已删除 0 否 1 是',
    `created_by`  bigint(20) DEFAULT NULL COMMENT '创建人',
    `updated_by`  bigint(20) DEFAULT NULL COMMENT '更新人',
    `gmt_create` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) COMMENT '创建时间',
    `gmt_modified` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) COMMENT '修改时间',
    `ts` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6) COMMENT '最后修改时间',
    PRIMARY KEY (`id`),
    KEY `idx_parent_id` (`parent_id`) USING BTREE
    ) ENGINE=InnoDB AUTO_INCREMENT=1000 DEFAULT CHARSET=utf8mb4 COMMENT='培训管理部门表';

CREATE TABLE if not exists `train_project` (
    `id` int(10) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `crm_project_id` varchar(50) NOT NULL COMMENT 'CRM项目ID',
    `project_name`  varchar(100) DEFAULT NULL COMMENT '项目名称',
    `project_code`  varchar(100) DEFAULT NULL COMMENT '项目编码',
    `project_area`  varchar(30) DEFAULT NULL COMMENT '项目所属区域',
    `project_area_id`  int(10) DEFAULT NULL COMMENT '项目所属区域id',
    `province_id`  varchar(50) DEFAULT NULL COMMENT '省份id',
    `city_id`  varchar(50) DEFAULT NULL COMMENT '城市id',
    `area_id`  varchar(50) DEFAULT NULL COMMENT '地区id',
    `province`  varchar(50) DEFAULT NULL COMMENT '省份',
    `city`  varchar(50) DEFAULT NULL COMMENT '城市',
    `area`  varchar(50) DEFAULT NULL COMMENT '区县',
    `project_type`  tinyint(2) DEFAULT NULL COMMENT '立项类型,0-正式立项，1-预立项',
    `project_level`  tinyint(2) DEFAULT NULL COMMENT '项目级别,0-国家，1-省，2-市，3-区县，3-院校，4-学校，5-全国统招',
    `start_date`  datetime(6) DEFAULT NULL COMMENT '项目开始时间',
    `end_date`  datetime(6) DEFAULT NULL COMMENT '项目结束时间',
    `project_status`  tinyint(1) NOT NULL DEFAULT '0' COMMENT '项目状态,0-未开始，1-进行中，2-已完成',
    `schedule_status`  tinyint(1) NOT NULL DEFAULT '0' COMMENT '排期状态,0-未排期，1-已排期',
    `is_remote`  tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否异地，0-否，1-是',
    `action_type`  tinyint(1) NOT NULL DEFAULT '0' COMMENT '执行类型，0-一次集中，1-分段实施',
    `execute_contact_name`  varchar(30) DEFAULT NULL COMMENT '执行对接人姓名',
    `execute_contact_phone`  varchar(30) DEFAULT NULL COMMENT '执行对接人电话',
    `total_hours`  varchar(30) DEFAULT NULL COMMENT '项目总学时',
    `project_manager_id`  bigint(20) DEFAULT NULL COMMENT '项目经理id',
    `project_manager_name`  varchar(30) DEFAULT NULL COMMENT '项目经理名称',
    `project_editor_id`  bigint(20) DEFAULT NULL COMMENT '责任编辑',
    `project_editor_name`  varchar(30) DEFAULT NULL COMMENT '责任编辑名称',
    `service_object`  varchar(60) NOT NULL DEFAULT '' COMMENT '服务对象',
    `service_content`  varchar(60) NOT NULL DEFAULT '' COMMENT '服务内容',
    `sales_user_id`  bigint(20) DEFAULT NULL COMMENT '销售人员id',
    `sales_user_name`  varchar(30) DEFAULT NULL COMMENT '销售人员名称',
    `province_sales_manager_id`  bigint(20) DEFAULT NULL COMMENT '省区销售经理id',
    `province_sales_manager_name`  varchar(30) DEFAULT NULL COMMENT '省区销售经理名称',
    `region_sales_director_id`  bigint(20) DEFAULT NULL COMMENT '区域销售总监id',
    `region_sales_director_name`  varchar(30) DEFAULT NULL COMMENT '区域销售总监名称',
    `business_type`  varchar(20) DEFAULT NULL COMMENT '业务类型',
    `project_mode`  varchar(20) DEFAULT NULL COMMENT '项目模式',
    `project_sub_mode`  varchar(20) DEFAULT NULL COMMENT '项目子模式',
    `customer_name`  varchar(50) DEFAULT NULL COMMENT '客户单位名称',
    `sync_type`  tinyint(1) NOT NULL DEFAULT '0' COMMENT '同步方式,0-自动，1-手动',
    `invalid` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否已删除 0 否 1 是',
    `created_by`  bigint(20) DEFAULT NULL COMMENT '创建人',
    `updated_by`  bigint(20) DEFAULT NULL COMMENT '更新人',
    `gmt_create` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) COMMENT '创建时间',
    `gmt_modified` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) COMMENT '修改时间',
    `ts` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6) COMMENT '最后修改时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_crm_project_id` (`crm_project_id`) COMMENT 'CRM项目ID唯一索引',
    KEY `idx_project_code` (`project_code`) COMMENT '项目编码索引',
    KEY `idx_project_area_id` (`project_area_id`) COMMENT '项目区域id索引',
    KEY `idx_project_province_city_area` (`province_id`, `city_id`, `area_id`) COMMENT '项目区域索引',
    KEY `idx_start_end_date` (`start_date`, `end_date`) COMMENT '项目开始结束时间索引',
    KEY `idx_project_manager_id` (`project_manager_id`) COMMENT '项目经理id索引',
    KEY `idx_project_editor_id` (`project_editor_id`) COMMENT '责任编辑id索引'
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='培训管理项目表';

CREATE TABLE if not exists `train_course` (
    `id` int(10) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `project_id` int(10) NOT NULL COMMENT '项目ID',
    `project_name`  varchar(100) NOT NULL COMMENT '项目名称',
    `project_code`  varchar(100) NOT NULL COMMENT '项目编码',
    `course_name`  varchar(50) NOT NULL COMMENT '班次名称',
    `course_status`  tinyint(1) NOT NULL DEFAULT '0' COMMENT '班次状态,0-未开始，1-进行中，2-已完成',
    `course_form`  tinyint(1) NOT NULL DEFAULT '1' COMMENT '班次形式，1-面授，2-远程直播，3-现场直播',
    `course_type`  tinyint(1) NOT NULL COMMENT '班次类型',
    `course_people_num`  int(10) NOT NULL COMMENT '班次人数',
    `service_object`  varchar(60) NOT NULL DEFAULT '' COMMENT '服务对象',
    `is_remote`  tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否异地，0-否，1-是',
    `course_area`  varchar(30) DEFAULT NULL COMMENT '班次执行区域',
    `course_area_id`  int(10) DEFAULT NULL COMMENT '班次执行区域id',
    `has_plan_desc`  tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否填写计划说明，0-否，1-是',
    `has_execution_progress`  tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否填写执行进展说明，0-否，1-是',
    `plan_desc` longtext DEFAULT NULL COMMENT '计划说明',
    `execution_progress` longtext DEFAULT NULL COMMENT '执行进展说明',
    `plan_start_date`  datetime(6) DEFAULT NULL COMMENT '计划开始时间',
    `plan_end_date`  datetime(6) DEFAULT NULL COMMENT '计划结束时间',
    `plan_days`  float(10,1) NOT NULL DEFAULT '0' COMMENT '计划天数',
    `real_start_date`  datetime(6) DEFAULT NULL COMMENT '实际开始时间',
    `real_end_date`  datetime(6) DEFAULT NULL COMMENT '实际结束时间',
    `real_days`  float(10,1) NOT NULL DEFAULT '0' COMMENT '实际天数',
    `total_person_days`  float(10,1) NOT NULL DEFAULT '0' COMMENT '总人天',
    `invalid` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否已删除 0 否 1 是',
    `created_by`  bigint(20) DEFAULT NULL COMMENT '创建人',
    `updated_by`  bigint(20) DEFAULT NULL COMMENT '更新人',
    `gmt_create` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) COMMENT '创建时间',
    `gmt_modified` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) COMMENT '修改时间',
    `ts` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6) COMMENT '最后修改时间',
    PRIMARY KEY (`id`),
    KEY `idx_project_id` (`project_id`) USING BTREE,
    KEY `idx_plan_start_end_date` (`plan_start_date`, `plan_end_date`) USING BTREE,
    KEY `idx_real_start_end_date` (`real_start_date`, `real_end_date`) USING BTREE,
    KEY `idx_course_area_id` (`course_area_id`) USING BTREE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='培训班次';

CREATE TABLE if not exists `train_course_user_relation` (
    `id` int(10) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `project_id` int(10) NOT NULL COMMENT '项目ID',
    `course_id`  int(10) NOT NULL COMMENT '班次ID',
    `user_id`  bigint(20) NOT NULL COMMENT '用户ID',
    `user_name`  varchar(30) NOT NULL DEFAULT '' COMMENT '用户名称',
    `user_dept_id`  int(10) DEFAULT NULL COMMENT '用户部门ID',
    `user_dept_name`  varchar(30) DEFAULT NULL COMMENT '用户部门名称',
    `relation_type`  tinyint(2) DEFAULT NULL COMMENT '关系类型,1-执行人，2-支援人',
    `start_date`  datetime(6) DEFAULT NULL COMMENT '开始时间',
    `end_date`  datetime(6) DEFAULT NULL COMMENT '结束时间',
    `invalid` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否已删除 0 否 1 是',
    `created_by`  bigint(20) DEFAULT NULL COMMENT '创建人',
    `updated_by`  bigint(20) DEFAULT NULL COMMENT '更新人',
    `gmt_create` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) COMMENT '创建时间',
    `gmt_modified` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) COMMENT '修改时间',
    `ts` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6) COMMENT '最后修改时间',
    PRIMARY KEY (`id`),
    KEY `idx_project_id` (`project_id`) USING BTREE,
    KEY `idx_course_id` (`course_id`) USING BTREE,
    KEY `idx_user_id` (`user_id`) USING BTREE
  ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='培训班次人员关系表';

CREATE TABLE if not exists `train_course_area_relation` (
    `id` int(10) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `project_id` int(10) NOT NULL COMMENT '项目ID',
    `course_id`  int(10) NOT NULL COMMENT '班次ID',
    `province_id`  varchar(50) DEFAULT NULL COMMENT '省份id',
    `city_id`  varchar(50) DEFAULT NULL COMMENT '城市id',
    `area_id`  varchar(50) DEFAULT NULL COMMENT '地区id',
    `province`  varchar(50) DEFAULT NULL COMMENT '省份',
    `city`  varchar(50) DEFAULT NULL COMMENT '城市',
    `area`  varchar(50) DEFAULT NULL COMMENT '区县',
    `invalid` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否已删除 0 否 1 是',
    `created_by`  bigint(20) DEFAULT NULL COMMENT '创建人',
    `updated_by`  bigint(20) DEFAULT NULL COMMENT '更新人',
    `gmt_create` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) COMMENT '创建时间',
    `gmt_modified` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) COMMENT '修改时间',
    `ts` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6) COMMENT '最后修改时间',
    PRIMARY KEY (`id`),
    KEY `idx_project_id` (`project_id`) USING BTREE,
    KEY `idx_course_id` (`course_id`) USING BTREE,
    KEY `idx_province_city_area` (`province_id`, `city_id`, `area_id`) USING BTREE
  ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='培训班次区域关系表';
  
  
CREATE TABLE if not exists `train_task` (
    `id` int(10) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `project_id` int(10) NOT NULL COMMENT '项目ID',
    `course_id`  int(10) NOT NULL COMMENT '班次ID',
    `task_name`  varchar(50) NOT NULL COMMENT '任务名称',
    `task_type`  tinyint(2) DEFAULT NULL COMMENT '任务类型,1-带班考勤',
    `task_desc`  varchar(300) DEFAULT NULL COMMENT '任务描述',
    `invalid` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否已删除 0 否 1 是',
    `created_by`  bigint(20) DEFAULT NULL COMMENT '创建人',
    `updated_by`  bigint(20) DEFAULT NULL COMMENT '更新人',
    `gmt_create` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) COMMENT '创建时间',
    `gmt_modified` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) COMMENT '修改时间',
    `ts` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6) COMMENT '最后修改时间',
    PRIMARY KEY (`id`),
    KEY `idx_project_id` (`project_id`) USING BTREE,
    KEY `idx_course_id` (`course_id`) USING BTREE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='培训任务';

CREATE TABLE if not exists `train_check_task` (
    `id` int(10) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `project_id` int(10) NOT NULL COMMENT '项目ID',
    `course_id`  int(10) NOT NULL COMMENT '班次ID',
    `task_id`  int(10) NOT NULL COMMENT '任务ID',
    `user_id`  bigint(20) NOT NULL COMMENT '用户ID',
    `user_name`  varchar(30) NOT NULL DEFAULT '' COMMENT '用户名称',
    `check_dimension`  tinyint(2) NOT NULL DEFAULT '1' COMMENT '打卡维度，1-全天，2-上午，3-下午',
    `check_in_status`  tinyint(2) NOT NULL DEFAULT '0' COMMENT '上班打卡状态,0-未打卡，1-正常打卡，2-迟到打卡,3-早退打卡',
    `check_out_status`  tinyint(2) NOT NULL DEFAULT '0' COMMENT '下班打卡状态,0-未打卡，1-正常打卡，2-迟到打卡,3-早退打卡',
    `check_on_site_status`  tinyint(2) NOT NULL DEFAULT '0' COMMENT '现场签到状态,0-未签到，1-已签到',
    `check_status`  tinyint(2) NOT NULL DEFAULT '0' COMMENT '打卡状态,0-正常，1-异常',
    `check_date` datetime(6) DEFAULT NULL COMMENT '打卡日期',
    `invalid` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否已删除 0 否 1 是',
    `created_by`  bigint(20) DEFAULT NULL COMMENT '创建人',
    `updated_by`  bigint(20) DEFAULT NULL COMMENT '更新人',
    `gmt_create` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) COMMENT '创建时间',
    `gmt_modified` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) COMMENT '修改时间',
    `ts` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6) COMMENT '最后修改时间',
    PRIMARY KEY (`id`),
    KEY `idx_project_id` (`project_id`) USING BTREE,
    KEY `idx_course_id` (`course_id`) USING BTREE,
    KEY `idx_task_id` (`task_id`) USING BTREE,
    KEY `idx_user_id` (`user_id`) USING BTREE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='带班考勤任务记录--按天总结';

CREATE TABLE if not exists `train_check_record` (
    `id` int(10) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `project_id` int(10) NOT NULL COMMENT '项目ID',
    `course_id`  int(10) NOT NULL COMMENT '班次ID',
    `task_id`  int(10) NOT NULL COMMENT '任务ID',
    `check_task_id`  int(10) NOT NULL COMMENT '考勤任务ID',
    `user_id`  bigint(20) NOT NULL COMMENT '用户ID',
    `check_type`  tinyint(2) DEFAULT NULL COMMENT '打卡类型,1-上班打卡，2-下班打卡，3-现场签到',
    `check_time`  datetime(6) DEFAULT NULL COMMENT '打卡时间',
    `check_status`  tinyint(2) DEFAULT NULL COMMENT '打卡状态,1-正常打卡，2-迟到打卡,3-早退打卡',
    `check_latitude`  double NOT NULL default 0 COMMENT '打卡纬度',
    `check_longitude`  double NOT NULL default 0 COMMENT '打卡经度',
    `check_address`  varchar(100) NOT NULL default '' COMMENT '打卡地址',
    `check_image`  varchar(300) NOT NULL default '' COMMENT '打卡图片',
    `invalid` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否已删除 0 否 1 是',
    `created_by`  bigint(20) DEFAULT NULL COMMENT '创建人',
    `updated_by`  bigint(20) DEFAULT NULL COMMENT '更新人',
    `gmt_create` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) COMMENT '创建时间',
    `gmt_modified` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) COMMENT '修改时间',
    `ts` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6) COMMENT '最后修改时间',
    PRIMARY KEY (`id`),
    KEY `idx_project_id` (`project_id`) USING BTREE,
    KEY `idx_course_task_check_task_id` (`course_id`, `task_id`, `check_task_id`) USING BTREE,
    KEY `idx_check_time` (`check_time`) USING BTREE,
    KEY `idx_user_id` (`user_id`) USING BTREE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='签到详细记录';

CREATE TABLE if not exists `dict_mapping` (
    `id` int(10) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `dict_code`  varchar(50) NOT NULL COMMENT '字典编码',
    `third_dict_code`  varchar(50) NOT NULL COMMENT '第三方字典编码',
    `third_system`  varchar(50) NOT NULL COMMENT '第三方系统',
    `dict_type`  varchar(50) NOT NULL COMMENT '字典类型',
    `dict_value`  varchar(50) NOT NULL COMMENT '字典值',
    `dict_desc`  varchar(300) NOT NULL COMMENT '字典描述',
    `dict_order`  int(10) NOT NULL DEFAULT '0' COMMENT '字典排序',
    `invalid` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否已删除 0 否 1 是',
    `gmt_create` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) COMMENT '创建时间',
    `gmt_modified` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) COMMENT '修改时间',
    `ts` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6) COMMENT '最后修改时间',
    PRIMARY KEY (`id`),
    KEY `idx_dict_type_code` (`dict_type`, `dict_code`) USING BTREE,
    KEY `idx_third_dict_code` (`third_system`, `dict_type`, `third_dict_code`) USING BTREE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='字典映射表';

CREATE TABLE if not exists `smart_config`  (
                              `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键',
                              `config_key` varchar(64) NOT NULL COMMENT '配置键',
                              `second_key` varchar(64) NOT NULL DEFAULT '' COMMENT '配置键',
                              `config_value` text NOT NULL COMMENT '配置值',
                              `config_order` int(2) NOT NULL default 0 COMMENT '排序',
                              `ext_info` varchar(255) NULL COMMENT '扩展信息',
                              `config_type` tinyint(1) NOT NULL DEFAULT '0' COMMENT '配置类型 0 single, 1 chunk',
                              `invalid` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否已删除 0 否 1 是',
                              `created_by` bigint(20) NOT NULL DEFAULT '0' COMMENT '创建人',
                              `updated_by` bigint(20) NOT NULL DEFAULT '0' COMMENT '修改人',
                              `gmt_create` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) COMMENT '创建时间',
                              `gmt_modified` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) COMMENT '修改时间',
                              `ts` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6) COMMENT '修改时间',
                              PRIMARY KEY (`id`) USING BTREE,
                              INDEX `idx_key_order`(`config_key`, `second_key`, `config_order`) USING BTREE COMMENT '配置键索引'
  )ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='灵活配置表';


CREATE TABLE if not exists `common_log`  (
                              `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键',
                              `log_type` varchar(64) NOT NULL COMMENT '日志类型',
                              `log_second_type` varchar(64) NOT NULL COMMENT '日志二级类型',
                              `detail` text NULL COMMENT '详细信息',
                              `ip` varchar(64) NULL DEFAULT '' COMMENT 'ip地址',
                              `user_id` varchar(64) NULL DEFAULT '' COMMENT '用户id',
                              `user_name` varchar(64) NULL DEFAULT '' COMMENT '用户名称',
                              `business_id` varchar(64) NULL DEFAULT '' COMMENT '业务id',
                              `business_type` varchar(64) NULL DEFAULT '' COMMENT '业务类型',
                              `ext_info` varchar(1024) NULL DEFAULT '' COMMENT '扩展信息',
                              `gmt_create` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) COMMENT '创建时间',
                              `ts` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6)  COMMENT '修改时间',
                              PRIMARY KEY (`id`) USING BTREE,
                              INDEX `idx_type`(`log_type`, `log_second_type`) USING BTREE COMMENT '日志类型索引',
                              INDEX `idx_business_type_id`(`business_type`, `business_id`) USING BTREE COMMENT '业务类型索引',
                              INDEX `idx_user_time`(`user_id`, `gmt_create`) USING BTREE COMMENT '用户id时间索引'
  )ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='通用日志记录';


INSERT INTO `dict_mapping` (`dict_code`, `third_dict_code`, `third_system`, `dict_type`, `dict_value`, `dict_desc`, `dict_order`) 
  VALUES ('0', 'v2KK9k36M', 'FXK_CRM', 'project_type', '正式立项', '正式立项', 0),
  ('1', 'option1', 'FXK_CRM', 'project_type', '预立项', '预立项', 1);

INSERT INTO `dict_mapping` (`dict_code`, `third_dict_code`, `third_system`, `dict_type`, `dict_value`, `dict_desc`, `dict_order`) 
  VALUES ('0', '', '', 'course_type', '集中研修', '集中研修', 0),
  ('1', '', '', 'course_type', '骨干面授', '骨干面授', 1),
  ('2', '', '', 'course_type', '入校指导', '入校指导', 2),
  ('3', '', '', 'course_type', '跟岗实践', '跟岗实践', 3),
  ('4', '', '', 'course_type', '送教下乡', '送教下乡', 4),
  ('99', '', '', 'course_type', '其他线下活动', '其他线下活动', 99);

INSERT INTO `dict_mapping` (`dict_code`, `third_dict_code`, `third_system`, `dict_type`, `dict_value`, `dict_desc`, `dict_order`) 
  VALUES ('0', 'IaXZ1A7pH', 'FXK_CRM', 'project_level', '国家', '国家', 0),
  ('1', 'hnwqO14t4', 'FXK_CRM', 'project_level', '省', '省', 1),
  ('2', 'g5q7GewS1', 'FXK_CRM', 'project_level', '市', '市', 2),
  ('3', 'doq3X8daX', 'FXK_CRM', 'project_level', '区县', '区县', 3),
  ('4', 'z1cp89TGc', 'FXK_CRM', 'project_level', '院校', '院校', 4),
  ('5', 'Br4E1z5vz', 'FXK_CRM', 'project_level', '学校', '学校', 5),
  ('6', 'nzplWE6Ua', 'FXK_CRM', 'project_level', '全国统招', '全国统招', 6);

INSERT INTO `dict_mapping` (`dict_code`, `third_dict_code`, `third_system`, `dict_type`, `dict_value`, `dict_desc`, `dict_order`) 
  VALUES ('default__c', 'default__c', 'FXK_CRM', 'business_type', '研训类项目立项', 'A.研训类：含A1常规培训、A2联合教研、A3课程资源、A4中高考备考', 0),
  ('record_9ti4I__c', 'record_9ti4I__c', 'FXK_CRM', 'business_type', '课程类项目立项', 'B.课程类：含B1课程建设、B2拔尖创新人才培养-高中培优/强基/竞赛', 1),
  ('record_FOcnr__c', 'record_FOcnr__c', 'FXK_CRM', 'business_type', '评价类项目立项', 'C.评价类：含C1课程监测、C2学业测评、C3其他', 2),
  ('record_1z4D6__c', 'record_1z4D6__c', 'FXK_CRM', 'business_type', '数智平台类项目立项', 'D.数智平台类：含D1赛事平台、D2培训平台、D3教研平台、D4科研平台、D5名师工作室平台', 3),
  ('record_Eex0p__c', 'record_Eex0p__c', 'FXK_CRM', 'business_type', '人工智能类项目立项', 'E.人工智能类：含E1课堂分析、E2智能门户、E3智能体、E4 AI通识课程、E5人机共育', 4),
  ('record_Rn2P3__c', 'record_Rn2P3__c', 'FXK_CRM', 'business_type', '直播类项目立项', 'F.直播类：含F1研直播、F2研播云、F3课程录制', 5),
  ('record_g2lvn__c', 'record_g2lvn__c', 'FXK_CRM', 'business_type', '其他类项目立项', 'G.其他类：如调研、年会、论坛、研讨会等', 6),
  ('record_L5m1x__c', 'record_L5m1x__c', 'FXK_CRM', 'business_type', '教学教研事业部项目立项单（手拉手项目、学科联研、集团化办学、课堂分析、个性化服务、其他）', '教学教研事业部项目立项单（手拉手项目、学科联研、集团化办学、课堂分析、个性化服务、其他）', 7),
  ('record_gM148__c', 'record_gM148__c', 'FXK_CRM', 'business_type', '原考试评价类项目立项', '原考试评价类项目立项', 8);

INSERT INTO train_management_center.`train_dept` (`id`, `parent_id`, `dept_name`, `dept_name_short`, `dept_type`, `dept_level`, `hidden`, `invalid`, `created_by`, `updated_by`)
VALUES
    (1, -1, '项目管理部', '项目管理部', 0, 1, 0, 0, -1, -1),
    (2, -1, '超管部门', '超管部门', 0, 1, 1, 0, -1, -1),
    (3, -1, '西南区域服务中心', '西南区域', 1, 1, 0, 0, -1, -1),
    (4, -1, '西北区域服务中心', '西北区域', 1, 1, 0, 0, -1, -1),
    (5, -1, '华北/东北区域服务中心', '华北/东北区域', 1, 1, 0, 0, -1, -1),
    (6, -1, '华东区域服务中心', '华东区域', 1, 1, 0, 0, -1, -1),
    (7, -1, '华中区域服务中心', '华中区域', 1, 1, 0, 0, -1, -1),
    (8, -1, '华南区域服务中心', '华南区域', 1, 1, 0, 0, -1, -1),
    (9, -1, '总部区域', '总部区域', 1, 1, 0, 0, -1, -1),
    (10, 4, '青藏服务中心', '青藏', 1, 2, 0, 0, -1, -1),
    (11, 4, '河南服务中心', '河南', 1, 2, 0, 0, -1, -1),
    (12, 4, '陕西服务中心', '陕西', 1, 2, 0, 0, -1, -1),
    (13, 4, '甘宁服务中心', '甘宁', 1, 2, 0, 0, -1, -1),
    (14, 6, '江西服务中心', '江西', 1, 2, 0, 0, -1, -1),
    (15, 6, '山东服务中心', '山东', 1, 2, 0, 0, -1, -1),
    (16, 6, '上海服务中心', '上海', 1, 2, 0, 0, -1, -1),
    (17, 6, '苏皖闽服务中心', '苏皖闽', 1, 2, 0, 0, -1, -1),
    (18, 6, '浙江服务中心', '浙江', 1, 2, 0, 0, -1, -1),
    (19, 7, '湖南服务中心', '湖南', 1, 2, 0, 0, -1, -1),
    (20, 7, '湖北服务中心', '湖北', 1, 2, 0, 0, -1, -1),
    (21, 8, '海南服务中心', '海南', 1, 2, 0, 0, -1, -1),
    (22, 8, '广西服务中心', '广西', 1, 2, 0, 0, -1, -1),
    (23, 8, '广东服务中心', '广东', 1, 2, 0, 0, -1, -1);
