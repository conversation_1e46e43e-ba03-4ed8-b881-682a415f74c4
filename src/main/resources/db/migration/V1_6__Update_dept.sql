update train_management_center.train_dept set dept_level = dept_level + 1 where id not in (1001,1002);
update train_management_center.train_dept set parent_id = 1001 where id in (1003,1004,1005,1006,1007,1008,1009);
update train_management_center.train_dept set dept_type = 1 where id = 1001;

CREATE TABLE `sys_role` (
  `id` int(10) NOT NULL AUTO_INCREMENT COMMENT '角色ID',
  `role_name` varchar(30) NOT NULL COMMENT '角色名称',
  `role_status` tinyint(1) NOT NULL DEFAULT '0' COMMENT '角色状态（0正常 1停用 -1删除）',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `gmt_create` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) COMMENT '创建时间',
  `gmt_modified` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) COMMENT '修改时间', 
  `ts` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6) COMMENT '最后修改时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_name` (`role_name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='角色信息表';

CREATE TABLE `sys_permission` (
  `id` int(10) NOT NULL AUTO_INCREMENT COMMENT '权限ID',
  `parent_id` int(10) DEFAULT '0' COMMENT '父权限ID (0代表顶级)',
  `permission_name` varchar(50) NOT NULL COMMENT '权限名称',
  `permission_type` varchar(20) NOT NULL COMMENT '菜单类型（MENU:菜单, PAGE:页面, FUNCTION:功能）',
  `permission_key` varchar(100) DEFAULT NULL COMMENT '权限标识 (例如: sys:user:list)',
  `p_path` varchar(255) DEFAULT NULL COMMENT '路由地址',
  `icon` varchar(100) DEFAULT NULL COMMENT '菜单图标',
  `sort_order` int DEFAULT '0' COMMENT '显示排序',
  `can_select` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否可选（0不可选 1可选）',
  `gmt_create` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) COMMENT '创建时间',
  `gmt_modified` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) COMMENT '修改时间',
  `ts` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6) COMMENT '最后修改时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='权限菜单表';

CREATE TABLE `sys_role_permission` (
  `id` int(10) NOT NULL AUTO_INCREMENT COMMENT '关联ID',
  `role_id` int(10) NOT NULL COMMENT '角色ID',
  `permission_key` varchar(100) NOT NULL COMMENT '权限标识',
  `invalid` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否失效（0正常 1失效）',
   `gmt_create` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) COMMENT '创建时间',
   `gmt_modified` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) COMMENT '修改时间',
  `ts` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6) COMMENT '最后修改时间',
  PRIMARY KEY (`id`),
  KEY `idx_role_id` (`role_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='角色和权限关联表';

-- SQL Data Initialization for sys_permission Table

-- ----------------------------
--  Top-Level Menus
-- ----------------------------
INSERT INTO `sys_permission` (id, parent_id, permission_name, permission_type, permission_key, p_path, sort_order, can_select) VALUES
(1, 0, '项目执行管理', 'MENU', 'project', '', 1, 0),
(2, 0, '系统管理', 'MENU', 'system', '', 2, 0);

-- ----------------------------
--  Sub-Menus under "项目执行管理"
-- ----------------------------
INSERT INTO `sys_permission` (id, parent_id, permission_name, permission_type, permission_key, p_path, sort_order, can_select) VALUES
(101, 1, '项目管理', 'MENU', 'project:manage', '/digital/projectManage', 1, 0),
(102, 1, '我的项目', 'MENU', 'project:my', '/digital/projectManage', 2, 0),
(103, 1, '项目详情页', 'PAGE', 'project:detail', '/digital/projectDetail', 3, 1),
(104, 1, '班次管理', 'MENU', 'project:course', '/digital/shiftManage', 4, 0),
(105, 1, '我的班次', 'MENU', 'project:my-course', '/digital/shiftManage', 5, 0),
(106, 1, '班次详情页', 'PAGE', 'project:course:detail', '/digital/courseDetail', 6, 1),
(107, 1, '执行人员管理', 'MENU', 'project:executor', '/digital/personnelManage', 7, 0);

-- ----------------------------
--  1. Project Management Permissions
-- ----------------------------
-- Pages
INSERT INTO `sys_permission` (id, parent_id, permission_name, permission_type, permission_key, p_path, sort_order, can_select) VALUES
(10101, 101, '项目列表', 'PAGE', 'project:manage:list', '', 1, 0),
(10201, 102, '项目列表', 'PAGE', 'project:my:list', '', 2, 0);

INSERT INTO `sys_permission` (id, parent_id, permission_name, permission_type, permission_key, p_path, sort_order, can_select) VALUES
(1010101, 10101, '当前组织项目列表', 'PAGE', 'project:manage:list:current', '', 1, 1),
(1010102, 10101, '跨组织支持项目列表', 'PAGE', 'project:manage:list:cross', '', 2, 1);

INSERT INTO `sys_permission` (id, parent_id, permission_name, permission_type, permission_key, p_path, sort_order, can_select) VALUES
(1020101, 10201, '我执行的当前组织项目列表', 'PAGE', 'project:my:list:current', '', 1, 1),
(1020102, 10201, '我执行的跨组织支持项目列表', 'PAGE', 'project:my:list:cross', '', 2, 1);

-- Functions for "项目列表"
INSERT INTO `sys_permission` (id, parent_id, permission_name, permission_type, permission_key, sort_order, can_select) VALUES
(101010101, 1010101, '查看项目列表', 'FUNCTION', 'project:manage:list:current:view', 1, 1),
(101010102, 1010101, '导入项目(手动同步)', 'FUNCTION', 'project:manage:list:current:import-manual', 2, 1),
(101010103, 1010101, '导入项目(搜索导入)', 'FUNCTION', 'project:manage:list:current:import-version', 3, 1),
(101010104, 1010101, '项目详情', 'FUNCTION', 'project:manage:list:current:detail', 4, 1),
(101010105, 1010101, '项目下新建班次', 'FUNCTION', 'project:manage:list:current:add-course', 5, 1),
(101010106, 1010101, '导出', 'FUNCTION', 'project:manage:list:current:export', 6, 1),
(101010107, 1010101, '删除项目', 'FUNCTION', 'project:manage:list:current:delete', 7, 1);

INSERT INTO `sys_permission` (id, parent_id, permission_name, permission_type, permission_key, sort_order, can_select) VALUES
(101010201, 1010102, '查看项目列表', 'FUNCTION', 'project:manage:list:cross:view', 1, 1),
(101010202, 1010102, '项目详情', 'FUNCTION', 'project:manage:list:cross:detail', 2, 1);


-- Functions for "我的项目"
INSERT INTO `sys_permission` (id, parent_id, permission_name, permission_type, permission_key, sort_order, can_select) VALUES
(102010101, 1020101, '查看项目列表', 'FUNCTION', 'project:my:list:current:view', 1, 1),
(102010102, 1020101, '项目详情', 'FUNCTION', 'project:my:list:current:detail', 2, 1),
(102010103, 1020101, '项目下新建班次', 'FUNCTION', 'project:my:list:current:add-course', 3, 1),
(102010104, 1020101, '导出', 'FUNCTION', 'project:my:list:current:export', 4, 1);

INSERT INTO `sys_permission` (id, parent_id, permission_name, permission_type, permission_key, sort_order, can_select) VALUES
(102010201, 1020102, '查看项目列表', 'FUNCTION', 'project:my:list:cross:view', 1, 1),
(102010202, 1020102, '项目详情', 'FUNCTION', 'project:my:list:cross:detail', 2, 1);

-- Functions for "项目详情页"
INSERT INTO `sys_permission` (id, parent_id, permission_name, permission_type, permission_key, sort_order, can_select) VALUES
(10301, 103, '查看项目详情', 'FUNCTION', 'project:detail:view', 1, 1),
(10302, 103, '新建班次', 'FUNCTION', 'project:detail:create-schedule', 2, 1),
(10303, 103, '复制班次', 'FUNCTION', 'project:detail:copy-schedule', 3, 1),
(10304, 103, '删除班次', 'FUNCTION', 'project:detail:delete-schedule', 4, 1),
(10305, 103, '日历视图', 'FUNCTION', 'project:detail:calendar-view', 5, 1);

-- ----------------------------
--  2. Schedule Management Permissions
-- ----------------------------
-- Pages
INSERT INTO `sys_permission` (id, parent_id, permission_name, permission_type, permission_key, p_path, sort_order, can_select) VALUES
(10401, 104, '班次列表', 'PAGE', 'project:course:list', '', 1, 0),
(10402, 104, '班次日历', 'PAGE', 'project:course:calendar', '', 2, 0);

INSERT INTO `sys_permission` (id, parent_id, permission_name, permission_type, permission_key, p_path, sort_order, can_select) VALUES
(1040101, 10401, '当前组织班次列表', 'PAGE', 'project:course:list:current', '', 1, 1),
(1040102, 10401, '跨组织支持班次列表', 'PAGE', 'project:course:list:cross', '', 2, 1),
(1040201, 10402, '当前组织班次日历', 'PAGE', 'project:course:calendar:current', '', 1, 1),
(1040202, 10402, '跨组织支持班次日历', 'PAGE', 'project:course:calendar:cross', '', 2, 1);

-- Functions for "班次列表"
INSERT INTO `sys_permission` (id, parent_id, permission_name, permission_type, permission_key, sort_order, can_select) VALUES
(104010101, 1040101, '查看班次列表', 'FUNCTION', 'project:course:list:current:view', 1, 1),
(104010102, 1040101, '新建班次', 'FUNCTION', 'project:course:list:current:create', 2, 1),
(104010103, 1040101, '班次详情', 'FUNCTION', 'project:course:list:current:detail', 3, 1),
(104010104, 1040101, '编辑班次', 'FUNCTION', 'project:course:list:current:edit', 4, 1),
(104010105, 1040101, '复制班次', 'FUNCTION', 'project:course:list:current:copy', 5, 1),
(104010106, 1040101, '删除班次', 'FUNCTION', 'project:course:list:current:delete', 6, 1),
(104010107, 1040101, '导出', 'FUNCTION', 'project:course:list:current:export', 7, 1);

INSERT INTO `sys_permission` (id, parent_id, permission_name, permission_type, permission_key, sort_order, can_select) VALUES
(104010201, 1040102, '查看班次列表', 'FUNCTION', 'project:course:list:cross:view', 1, 1),
(104010202, 1040102, '班次详情', 'FUNCTION', 'project:course:list:cross:detail', 2, 1);

-- Functions for "班次日历"
INSERT INTO `sys_permission` (id, parent_id, permission_name, permission_type, permission_key, sort_order, can_select) VALUES
(104020101, 1040201, '查看班次日历', 'FUNCTION', 'project:course:calendar:current:view', 1, 1),
(104020102, 1040201, '班次详情', 'FUNCTION', 'project:course:calendar:current:detail', 2, 1);

INSERT INTO `sys_permission` (id, parent_id, permission_name, permission_type, permission_key, sort_order, can_select) VALUES
(104020201, 1040202, '查看班次日历', 'FUNCTION', 'project:course:calendar:cross:view', 1, 1),
(104020202, 1040202, '班次详情', 'FUNCTION', 'project:course:calendar:cross:detail', 2, 1);

-- ----------------------------
--  3. My Schedule Permissions
-- ----------------------------
-- Pages
INSERT INTO `sys_permission` (id, parent_id, permission_name, permission_type, permission_key, p_path, sort_order, can_select) VALUES
(10501, 105, '班次列表', 'PAGE', 'project:my-course:list', '', 1, 0),
(10502, 105, '班次日历', 'PAGE', 'project:my-course:calendar', '', 2, 0);

INSERT INTO `sys_permission` (id, parent_id, permission_name, permission_type, permission_key, p_path, sort_order, can_select) VALUES
(1050101, 10501, '我执行的当前组织班次列表', 'PAGE', 'project:my-course:list:current', '', 1, 1),
(1050102, 10501, '我执行的跨组织支持班次列表', 'PAGE', 'project:my-course:list:cross', '', 2, 1),
(1050201, 10502, '我执行的当前组织班次日历', 'PAGE', 'project:my-course:calendar:current', '', 1, 1),
(1050202, 10502, '我执行的跨组织支持班次日历', 'PAGE', 'project:my-course:calendar:cross', '', 2, 1);

-- Functions for "我的班次" > "班次列表"
INSERT INTO `sys_permission` (id, parent_id, permission_name, permission_type, permission_key, sort_order, can_select) VALUES
(105010101, 1050101, '查看班次列表', 'FUNCTION', 'project:my-course:list:current:view', 1, 1),
(105010102, 1050101, '新建班次', 'FUNCTION', 'project:my-course:list:current:create', 2, 1),
(105010103, 1050101, '班次详情', 'FUNCTION', 'project:my-course:list:current:detail', 3, 1),
(105010104, 1050101, '编辑班次', 'FUNCTION', 'project:my-course:list:current:edit', 4, 1),
(105010105, 1050101, '复制班次', 'FUNCTION', 'project:my-course:list:current:copy', 5, 1),
(105010106, 1050101, '删除班次', 'FUNCTION', 'project:my-course:list:current:delete', 6, 1),
(105010107, 1050101, '导出', 'FUNCTION', 'project:my-course:list:current:export', 7, 1);

INSERT INTO `sys_permission` (id, parent_id, permission_name, permission_type, permission_key, sort_order, can_select) VALUES
(105010201, 1050102, '查看班次列表', 'FUNCTION', 'project:my-course:list:cross:view', 1, 1),
(105010202, 1050102, '班次详情', 'FUNCTION', 'project:my-course:list:cross:detail', 2, 1);

-- Functions for "我的班次" > "班次日历"
INSERT INTO `sys_permission` (id, parent_id, permission_name, permission_type, permission_key, sort_order, can_select) VALUES
(105020101, 1050201, '查看班次日历', 'FUNCTION', 'project:my-course:calendar:current:view', 1, 1),
(105020102, 1050201, '班次详情', 'FUNCTION', 'project:my-course:calendar:current:detail', 2, 1);

INSERT INTO `sys_permission` (id, parent_id, permission_name, permission_type, permission_key, sort_order, can_select) VALUES
(105020201, 1050202, '查看班次日历', 'FUNCTION', 'project:my-course:calendar:cross:view', 1, 1),
(105020202, 1050202, '班次详情', 'FUNCTION', 'project:my-course:calendar:cross:detail', 2, 1);

-- ----------------------------
--  4. Schedule Detail Page Permissions
-- ----------------------------
INSERT INTO `sys_permission` (id, parent_id, permission_name, permission_type, permission_key, sort_order, can_select) VALUES
(10601, 106, '查看班次详情', 'FUNCTION', 'project:course:detail:view', 1, 1),
(10602, 106, '编辑班次', 'FUNCTION', 'project:course:detail:edit', 2, 1),
(10603, 106, '复制班次', 'FUNCTION', 'project:course:detail:copy', 3, 1),
(10604, 106, '删除班次', 'FUNCTION', 'project:course:detail:delete', 4, 1),
(10605, 106, '班次人员日历', 'FUNCTION', 'project:course:detail:calendar', 5, 1);

-- ----------------------------
--  5. Executor Management Permissions
-- ----------------------------
-- Pages
INSERT INTO `sys_permission` (id, parent_id, permission_name, permission_type, permission_key, p_path, sort_order, can_select) VALUES
(10701, 107, '人员列表', 'PAGE', 'project:executor:list', '', 1, 0);

INSERT INTO `sys_permission` (id, parent_id, permission_name, permission_type, permission_key, p_path, sort_order, can_select) VALUES
(1070101, 10701, '人员列表', 'PAGE', 'project:executor:list:list', '', 1, 1),
(1070102, 10701, '人员详情页', 'PAGE', 'project:executor:list:detail', '', 2, 1),
(1070103, 10701, '闲忙日历', 'PAGE', 'project:executor:list:calendar', '', 3, 1);

-- Functions for "人员列表"
INSERT INTO `sys_permission` (id, parent_id, permission_name, permission_type, permission_key, sort_order, can_select) VALUES
(107010101, 1070101, '查看人员列表', 'FUNCTION', 'project:executor:list:list:view', 1, 1),
(107010102, 1070101, '查看人员详情', 'FUNCTION', 'project:executor:list:detail:view', 2, 1);
-- Functions for "人员详情页"
INSERT INTO `sys_permission` (id, parent_id, permission_name, permission_type, permission_key, sort_order, can_select) VALUES
(107010201, 1070102, '查看人员详情', 'FUNCTION', 'project:executor:list:detail:view', 1, 1);
-- Functions for "闲忙日历"
INSERT INTO `sys_permission` (id, parent_id, permission_name, permission_type, permission_key, sort_order, can_select) VALUES
(107010301, 1070103, '查看闲忙日历', 'FUNCTION', 'project:executor:list:calendar:view', 1, 1),
(107010302, 1070103, '人员详情', 'FUNCTION', 'project:executor:list:calendar:detail', 2, 1);

-- ----------------------------
--  6. System Management Permissions
-- ----------------------------
-- Pages
INSERT INTO `sys_permission` (id, parent_id, permission_name, permission_type, permission_key, p_path, sort_order, can_select) VALUES
(201, 2, '成员管理', 'MENU', 'system:member', '', 1, 0),
(202, 2, '角色权限管理', 'MENU', 'system:role', '', 2, 0);
-- Sub-Page for "成员管理"
INSERT INTO `sys_permission` (id, parent_id, permission_name, permission_type, permission_key, p_path, sort_order, can_select) VALUES
(20101, 201, '成员列表', 'PAGE', 'system:member:list', '', 1, 1);

-- Functions for "成员管理"
INSERT INTO `sys_permission` (id, parent_id, permission_name, permission_type, permission_key, sort_order, can_select) VALUES
(2010101, 20101, '查看成员列表', 'FUNCTION', 'system:member:list:view', 1, 1),
(2010102, 20101, '新增成员', 'FUNCTION', 'system:member:list:add', 2, 1),
(2010103, 20101, '编辑成员', 'FUNCTION', 'system:member:list:edit', 3, 1),
(2010104, 20101, '启用/停用成员', 'FUNCTION', 'system:member:list:status', 4, 1),
(2010105, 20101, '删除成员', 'FUNCTION', 'system:member:list:delete', 5, 1);
-- Sub-Page for "角色权限管理"
INSERT INTO `sys_permission` (id, parent_id, permission_name, permission_type, permission_key, p_path, sort_order, can_select) VALUES
(20202, 202, '系统角色', 'PAGE', 'system:role:list', '', 1, 1);
-- Functions for "系统角色"
INSERT INTO `sys_permission` (id, parent_id, permission_name, permission_type, permission_key, sort_order, can_select) VALUES
(2020201, 20202, '查看角色列表', 'FUNCTION', 'system:role:list:view', 1, 1),
(2020202, 20202, '新增角色', 'FUNCTION', 'system:role:list:add', 2, 1),
(2020203, 20202, '编辑角色', 'FUNCTION', 'system:role:list:edit', 3, 1),
(2020204, 20202, '删除角色', 'FUNCTION', 'system:role:list:delete', 4, 1);

INSERT INTO `sys_role` (id, role_name, role_status, remark) VALUES
(1, '超级管理员', 0, '系统最高管理权限'),
(2, '区域总监', 0, '负责本区域内的整体项目排班管理与调度'),
(3, '项目经理', 0, '负责本区域内项目的执行管理，包括班次安排、人员调度'),
(4, '教务编辑', 0, '查看自己所负责的项目及班次');

-- 超级管理员
INSERT INTO `sys_role_permission` (role_id, permission_key) VALUES
(1, "project:detail"),
(1, "project:detail:view"),
(1, "project:detail:create-schedule"),
(1, "project:detail:copy-schedule"),
(1, "project:detail:delete-schedule"),
(1, "project:detail:calendar-view"),
(1, "project:course:detail"),
(1, "project:course:detail:view"),
(1, "project:course:detail:edit"),
(1, "project:course:detail:copy"),
(1, "project:course:detail:delete"),
(1, "project:course:detail:calendar"),
(1, "system:member:list"),
(1, "system:role:list"),
(1, "project:manage:list:current"),
(1, "project:manage:list:cross"),
(1, "project:course:list:current"),
(1, "project:course:list:cross"),
(1, "project:course:calendar:current"),
(1, "project:course:calendar:cross"),
(1, "project:executor:list:list"),
(1, "project:executor:list:detail"),
(1, "project:executor:list:calendar"),
(1, "system:member:list:view"),
(1, "system:member:list:add"),
(1, "system:member:list:edit"),
(1, "system:member:list:status"),
(1, "system:member:list:delete"),
(1, "system:role:list:view"),
(1, "system:role:list:add"),
(1, "system:role:list:edit"),
(1, "system:role:list:delete"),
(1, "project:manage:list:current:view"),
(1, "project:manage:list:current:import-manual"),
(1, "project:manage:list:current:import-version"),
(1, "project:manage:list:current:detail"),
(1, "project:manage:list:current:add-course"),
(1, "project:manage:list:current:export"),
(1, "project:manage:list:current:delete"),
(1, "project:manage:list:cross:view"),
(1, "project:manage:list:cross:detail"),
(1, "project:course:list:current:view"),
(1, "project:course:list:current:create"),
(1, "project:course:list:current:detail"),
(1, "project:course:list:current:edit"),
(1, "project:course:list:current:copy"),
(1, "project:course:list:current:delete"),
(1, "project:course:list:current:export"),
(1, "project:course:list:cross:view"),
(1, "project:course:list:cross:detail"),
(1, "project:course:calendar:current:view"),
(1, "project:course:calendar:current:detail"),
(1, "project:course:calendar:cross:view"),
(1, "project:course:calendar:cross:detail"),
(1, "project:executor:list:list:view"),
(1, "project:executor:list:detail:view"),
(1, "project:executor:list:detail:view"),
(1, "project:executor:list:calendar:view"),
(1, "project:executor:list:calendar:detail");

-- 区域总监
INSERT INTO `sys_role_permission` (role_id, permission_key) VALUES
(2, "project:detail"),
(2, "project:detail:view"),
(2, "project:detail:create-schedule"),
(2, "project:detail:copy-schedule"),
(2, "project:detail:delete-schedule"),
(2, "project:detail:calendar-view"),
(2, "project:course:detail"),
(2, "project:course:detail:view"),
(2, "project:course:detail:edit"),
(2, "project:course:detail:copy"),
(2, "project:course:detail:delete"),
(2, "project:course:detail:calendar"),
(2, "project:manage:list:current"),
(2, "project:manage:list:cross"),
(2, "project:course:list:current"),
(2, "project:course:list:cross"),
(2, "project:course:calendar:current"),
(2, "project:course:calendar:cross"),
(2, "project:executor:list:list"),
(2, "project:executor:list:detail"),
(2, "project:executor:list:calendar"),
(2, "project:manage:list:current:view"),
(2, "project:manage:list:current:import-manual"),
(2, "project:manage:list:current:import-version"),
(2, "project:manage:list:current:detail"),
(2, "project:manage:list:current:add-course"),
(2, "project:manage:list:current:export"),
(2, "project:manage:list:current:delete"),
(2, "project:manage:list:cross:view"),
(2, "project:manage:list:cross:detail"),
(2, "project:course:list:current:view"),
(2, "project:course:list:current:create"),
(2, "project:course:list:current:detail"),
(2, "project:course:list:current:edit"),
(2, "project:course:list:current:copy"),
(2, "project:course:list:current:delete"),
(2, "project:course:list:current:export"),
(2, "project:course:list:cross:view"),
(2, "project:course:list:cross:detail"),
(2, "project:course:calendar:current:view"),
(2, "project:course:calendar:current:detail"),
(2, "project:course:calendar:cross:view"),
(2, "project:course:calendar:cross:detail"),
(2, "project:executor:list:list:view"),
(2, "project:executor:list:detail:view"),
(2, "project:executor:list:detail:view"),
(2, "project:executor:list:calendar:view"),
(2, "project:executor:list:calendar:detail");

-- 项目经理
INSERT INTO `sys_role_permission` (role_id, permission_key) VALUES
(3, "project:detail"),
(3, "project:detail:view"),
(3, "project:detail:create-schedule"),
(3, "project:detail:copy-schedule"),
(3, "project:detail:delete-schedule"),
(3, "project:detail:calendar-view"),
(3, "project:course:detail"),
(3, "project:course:detail:view"),
(3, "project:course:detail:edit"),
(3, "project:course:detail:copy"),
(3, "project:course:detail:delete"),
(3, "project:course:detail:calendar"),
(3, "project:manage:list:current"),
(3, "project:manage:list:cross"),
(3, "project:course:list:current"),
(3, "project:course:list:cross"),
(3, "project:course:calendar:current"),
(3, "project:course:calendar:cross"),
(3, "project:executor:list:list"),
(3, "project:executor:list:detail"),
(3, "project:executor:list:calendar"),
(3, "project:manage:list:current:view"),
(3, "project:manage:list:current:import-manual"),
(3, "project:manage:list:current:import-version"),
(3, "project:manage:list:current:detail"),
(3, "project:manage:list:current:add-course"),
(3, "project:manage:list:current:export"),
(3, "project:manage:list:current:delete"),
(3, "project:manage:list:cross:view"),
(3, "project:manage:list:cross:detail"),
(3, "project:course:list:current:view"),
(3, "project:course:list:current:create"),
(3, "project:course:list:current:detail"),
(3, "project:course:list:current:edit"),
(3, "project:course:list:current:copy"),
(3, "project:course:list:current:delete"),
(3, "project:course:list:current:export"),
(3, "project:course:list:cross:view"),
(3, "project:course:list:cross:detail"),
(3, "project:course:calendar:current:view"),
(3, "project:course:calendar:current:detail"),
(3, "project:course:calendar:cross:view"),
(3, "project:course:calendar:cross:detail"),
(3, "project:executor:list:list:view"),
(3, "project:executor:list:detail:view"),
(3, "project:executor:list:detail:view"),
(3, "project:executor:list:calendar:view"),
(3, "project:executor:list:calendar:detail");

-- 教务编辑
INSERT INTO `sys_role_permission` (role_id, permission_key) VALUES
(4, "project:my:list:current"),
(4, "project:my:list:cross"),
(4, "project:my:list:current:view"),
(4, "project:my:list:current:detail"),
(4, "project:my:list:cross:view"),
(4, "project:my:list:cross:detail"),
(4, "project:detail"),
(4, "project:detail:view"),
(4, "project:detail:calendar-view"),
(4, "project:my-course:list:current"),
(4, "project:my-course:list:cross"),
(4, "project:my-course:calendar:current"),
(4, "project:my-course:calendar:cross"),
(4, "project:my-course:list:current:view"),
(4, "project:my-course:list:current:detail"),
(4, "project:my-course:list:cross:view"),
(4, "project:my-course:list:cross:detail"),
(4, "project:my-course:calendar:current:view"),
(4, "project:my-course:calendar:current:detail"),
(4, "project:my-course:calendar:cross:view"),
(4, "project:my-course:calendar:cross:detail"),
(4, "project:course:detail"),
(4, "project:course:detail:view"),
(4, "project:course:detail:calendar");

alter table train_management_center.train_user add column manage_dept_ids varchar(300) not null default '' comment '管理部门ID' after dept_ids;
alter table train_management_center.train_user add column role_id int(10) not null default 0 comment '角色ID' after user_role;

update train_management_center.train_user set role_id = 1 where user_role = '主管';
update train_management_center.train_user set role_id = 2 where user_role like '业务总监%';
update train_management_center.train_user set role_id = 3 where user_role like '项目经理%';
update train_management_center.train_user set role_id = 4 where user_role = '教务编辑';

alter table train_management_center.train_course_user_relation modify column `user_dept_id` varchar(100) DEFAULT NULL COMMENT '用户部门ID';
alter table train_management_center.train_course_user_relation modify column `user_dept_name` varchar(300) DEFAULT NULL COMMENT '用户部门名称';

alter table train_management_center.train_course_user_relation add column `course_area_id` int(10) DEFAULT NULL COMMENT '班次执行区域id' after `course_id`;
alter table train_management_center.train_course_user_relation add column `project_area_id` int(10) DEFAULT NULL COMMENT '项目执行区域id' after `project_id`;

update train_management_center.train_dept set id = 10000 where id = 1001;
update train_management_center.train_dept set parent_id = 10000 where parent_id = 1001;
update train_management_center.train_project set project_area_id = 10000 where project_area_id = 1001;
update train_management_center.train_course set course_area_id = 10000 where course_area_id = 1001;
update train_management_center.train_course_user_relation set user_dept_id = 10000 where user_dept_id = '1001';
update train_management_center.train_user set dept_ids = '10000' where dept_ids = '1001';
update train_management_center.train_dept set id = 20000 where id = 1002;
update train_management_center.train_dept set parent_id = 20000 where parent_id = 1002;
update train_management_center.train_project set project_area_id = 20000 where project_area_id = 1002;
update train_management_center.train_course set course_area_id = 20000 where course_area_id = 1002;
update train_management_center.train_course_user_relation set user_dept_id = 20000 where user_dept_id = '1002';
update train_management_center.train_user set dept_ids = '20000' where dept_ids = '1002';
update train_management_center.train_dept set id = 10100 where id = 1003;
update train_management_center.train_dept set parent_id = 10100 where parent_id = 1003;
update train_management_center.train_project set project_area_id = 10100 where project_area_id = 1003;
update train_management_center.train_course set course_area_id = 10100 where course_area_id = 1003;
update train_management_center.train_course_user_relation set user_dept_id = 10100 where user_dept_id = '1003';
update train_management_center.train_user set dept_ids = '10100' where dept_ids = '1003';
update train_management_center.train_dept set id = 10200 where id = 1004;
update train_management_center.train_dept set parent_id = 10200 where parent_id = 1004;
update train_management_center.train_project set project_area_id = 10200 where project_area_id = 1004;
update train_management_center.train_course set course_area_id = 10200 where course_area_id = 1004;
update train_management_center.train_course_user_relation set user_dept_id = 10200 where user_dept_id = '1004';
update train_management_center.train_user set dept_ids = '10200' where dept_ids = '1004';
update train_management_center.train_dept set id = 10300 where id = 1005;
update train_management_center.train_dept set parent_id = 10300 where parent_id = 1005;
update train_management_center.train_project set project_area_id = 10300 where project_area_id = 1005;
update train_management_center.train_course set course_area_id = 10300 where course_area_id = 1005;
update train_management_center.train_course_user_relation set user_dept_id = 10300 where user_dept_id = '1005';
update train_management_center.train_user set dept_ids = '10300' where dept_ids = '1005';
update train_management_center.train_dept set id = 10400 where id = 1006;
update train_management_center.train_dept set parent_id = 10400 where parent_id = 1006;
update train_management_center.train_project set project_area_id = 10400 where project_area_id = 1006;
update train_management_center.train_course set course_area_id = 10400 where course_area_id = 1006;
update train_management_center.train_course_user_relation set user_dept_id = 10400 where user_dept_id = '1006';
update train_management_center.train_user set dept_ids = '10400' where dept_ids = '1006';
update train_management_center.train_dept set id = 10500 where id = 1007;
update train_management_center.train_dept set parent_id = 10500 where parent_id = 1007;
update train_management_center.train_project set project_area_id = 10500 where project_area_id = 1007;
update train_management_center.train_course set course_area_id = 10500 where course_area_id = 1007;
update train_management_center.train_course_user_relation set user_dept_id = 10500 where user_dept_id = '1007';
update train_management_center.train_user set dept_ids = '10500' where dept_ids = '1007';
update train_management_center.train_dept set id = 10600 where id = 1008;
update train_management_center.train_dept set parent_id = 10600 where parent_id = 1008;
update train_management_center.train_project set project_area_id = 10600 where project_area_id = 1008;
update train_management_center.train_course set course_area_id = 10600 where course_area_id = 1008;
update train_management_center.train_course_user_relation set user_dept_id = 10600 where user_dept_id = '1008';
update train_management_center.train_user set dept_ids = '10600' where dept_ids = '1008';
update train_management_center.train_dept set id = 10700 where id = 1009;
update train_management_center.train_dept set parent_id = 10700 where parent_id = 1009;
update train_management_center.train_project set project_area_id = 10700 where project_area_id = 1009;
update train_management_center.train_course set course_area_id = 10700 where course_area_id = 1009;
update train_management_center.train_course_user_relation set user_dept_id = 10700 where user_dept_id = '1009';
update train_management_center.train_user set dept_ids = '10700' where dept_ids = '1009';
update train_management_center.train_dept set id = 10201 where id = 1010; 
update train_management_center.train_project set project_area_id = 10201 where project_area_id = 1010;
update train_management_center.train_course set course_area_id = 10201 where course_area_id = 1010;
update train_management_center.train_course_user_relation set user_dept_id = 10201 where user_dept_id = '1010';
update train_management_center.train_user set dept_ids = '10201' where dept_ids = '1010';
update train_management_center.train_dept set id = 10202 where id = 1011;
update train_management_center.train_project set project_area_id = 10202 where project_area_id = 1011;
update train_management_center.train_course set course_area_id = 10202 where course_area_id = 1011;
update train_management_center.train_course_user_relation set user_dept_id = 10202 where user_dept_id = '1011';
update train_management_center.train_user set dept_ids = '10202' where dept_ids = '1011';
update train_management_center.train_dept set id = 10203 where id = 1012;
update train_management_center.train_project set project_area_id = 10203 where project_area_id = 1012;
update train_management_center.train_course set course_area_id = 10203 where course_area_id = 1012;
update train_management_center.train_course_user_relation set user_dept_id = 10203 where user_dept_id = '1012';
update train_management_center.train_user set dept_ids = '10203' where dept_ids = '1012';
update train_management_center.train_dept set id = 10204 where id = 1013;
update train_management_center.train_project set project_area_id = 10204 where project_area_id = 1013;
update train_management_center.train_course set course_area_id = 10204 where course_area_id = 1013;
update train_management_center.train_course_user_relation set user_dept_id = 10204 where user_dept_id = '1013';
update train_management_center.train_user set dept_ids = '10204' where dept_ids = '1013';
update train_management_center.train_dept set id = 10401 where id = 1014;
update train_management_center.train_project set project_area_id = 10401 where project_area_id = 1014;
update train_management_center.train_course set course_area_id = 10401 where course_area_id = 1014;
update train_management_center.train_course_user_relation set user_dept_id = 10401 where user_dept_id = '1014';
update train_management_center.train_user set dept_ids = '10401' where dept_ids = '1014';
update train_management_center.train_dept set id = 10402 where id = 1015;
update train_management_center.train_project set project_area_id = 10402 where project_area_id = 1015;
update train_management_center.train_course set course_area_id = 10402 where course_area_id = 1015;
update train_management_center.train_course_user_relation set user_dept_id = 10402 where user_dept_id = '1015';
update train_management_center.train_user set dept_ids = '10402' where dept_ids = '1015';
update train_management_center.train_dept set id = 10403 where id = 1016;
update train_management_center.train_project set project_area_id = 10403 where project_area_id = 1016;
update train_management_center.train_course set course_area_id = 10403 where course_area_id = 1016;
update train_management_center.train_course_user_relation set user_dept_id = 10403 where user_dept_id = '1016';
update train_management_center.train_user set dept_ids = '10403' where dept_ids = '1016';
update train_management_center.train_dept set id = 10404 where id = 1017;
update train_management_center.train_project set project_area_id = 10404 where project_area_id = 1017;
update train_management_center.train_course set course_area_id = 10404 where course_area_id = 1017;
update train_management_center.train_course_user_relation set user_dept_id = 10404 where user_dept_id = '1017';
update train_management_center.train_user set dept_ids = '10404' where dept_ids = '1017';
update train_management_center.train_dept set id = 10405 where id = 1018;
update train_management_center.train_project set project_area_id = 10405 where project_area_id = 1018;
update train_management_center.train_course set course_area_id = 10405 where course_area_id = 1018;
update train_management_center.train_course_user_relation set user_dept_id = 10405 where user_dept_id = '1018';
update train_management_center.train_user set dept_ids = '10405' where dept_ids = '1018';
update train_management_center.train_dept set id = 10501 where id = 1019;
update train_management_center.train_project set project_area_id = 10501 where project_area_id = 1019;
update train_management_center.train_course set course_area_id = 10501 where course_area_id = 1019;
update train_management_center.train_course_user_relation set user_dept_id = 10501 where user_dept_id = '1019';
update train_management_center.train_user set dept_ids = '10501' where dept_ids = '1019';
update train_management_center.train_dept set id = 10502 where id = 1020;
update train_management_center.train_project set project_area_id = 10502 where project_area_id = 1020;
update train_management_center.train_course set course_area_id = 10502 where course_area_id = 1020;
update train_management_center.train_course_user_relation set user_dept_id = 10502 where user_dept_id = '1020';
update train_management_center.train_user set dept_ids = '10502' where dept_ids = '1020';
update train_management_center.train_dept set id = 10601 where id = 1021;
update train_management_center.train_project set project_area_id = 10601 where project_area_id = 1021;
update train_management_center.train_course set course_area_id = 10601 where course_area_id = 1021;
update train_management_center.train_course_user_relation set user_dept_id = 10601 where user_dept_id = '1021';
update train_management_center.train_user set dept_ids = '10601' where dept_ids = '1021';
update train_management_center.train_dept set id = 10602 where id = 1022;
update train_management_center.train_project set project_area_id = 10602 where project_area_id = 1022;
update train_management_center.train_course set course_area_id = 10602 where course_area_id = 1022;
update train_management_center.train_course_user_relation set user_dept_id = 10602 where user_dept_id = '1022';
update train_management_center.train_user set dept_ids = '10602' where dept_ids = '1022';
update train_management_center.train_dept set id = 10603 where id = 1023;
update train_management_center.train_project set project_area_id = 10603 where project_area_id = 1023;
update train_management_center.train_course set course_area_id = 10603 where course_area_id = 1023;
update train_management_center.train_course_user_relation set user_dept_id = 10603 where user_dept_id = '1023';
update train_management_center.train_user set dept_ids = '10603' where dept_ids = '1023';

UPDATE train_management_center.train_course_user_relation r
JOIN train_management_center.train_course c ON r.course_id = c.id
SET r.course_area_id = c.course_area_id where 1=1;

UPDATE train_management_center.train_course_user_relation r
JOIN train_management_center.train_project p ON r.project_id = p.id
SET r.project_area_id = p.project_area_id where 1=1;

update train_management_center.train_user set manage_dept_ids = dept_ids where dept_ids is not null and dept_ids != '';

UPDATE train_management_center.dict_mapping
	SET dict_value='研训类'
	WHERE dict_code='default__c';
UPDATE train_management_center.dict_mapping
	SET dict_value='课程类'
	WHERE dict_code='record_9ti4I__c';
UPDATE train_management_center.dict_mapping
	SET dict_value='评价类'
	WHERE dict_code='record_FOcnr__c';
UPDATE train_management_center.dict_mapping
	SET dict_value='数智平台类'
	WHERE dict_code='record_1z4D6__c';
UPDATE train_management_center.dict_mapping
	SET dict_value='人工智能类'
	WHERE dict_code='record_Eex0p__c';
UPDATE train_management_center.dict_mapping
	SET dict_value='直播类'
	WHERE dict_code='record_Rn2P3__c';
UPDATE train_management_center.dict_mapping
	SET dict_value='其他类'
	WHERE dict_code='record_g2lvn__c';
UPDATE train_management_center.dict_mapping
	SET dict_value='原考试评价类'
	WHERE dict_code='record_gM148__c';
