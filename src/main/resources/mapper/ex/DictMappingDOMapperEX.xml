<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dt.train.management.center.mapper.ex.DictMappingDOMapperEx">

    <resultMap id="ResultMap" type="com.dt.train.management.center.model.dao.DictMappingDO"
        extends="com.dt.train.management.center.mapper.DictMappingDOMapper.BaseResultMap" />

    <sql id="Column_List">
        <include refid="com.dt.train.management.center.mapper.DictMappingDOMapper.Base_Column_List" />
    </sql>


    <!-- selectByDictType -->
    <select id="selectByDictType" resultMap="ResultMap">
        select
        <include refid="Column_List" />
        from
        dict_mapping where dict_type = #{dictType,jdbcType=VARCHAR} and invalid = 0 order by dict_order
    </select>
    <select id="selectByThirdDictCode"  resultMap="ResultMap">
        select
        <include refid="Column_List" />
        from
        dict_mapping where dict_type = #{dictType,jdbcType=VARCHAR} 
            and third_dict_code = #{thirdDictCode,jdbcType=VARCHAR} 
            and invalid = 0 order by dict_order
    </select>

</mapper>