<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dt.train.management.center.mapper.ex.TrainProjectDOMapperEx">

  <resultMap id="BaseResultMap" type="com.dt.train.management.center.model.dao.TrainProjectDO"
    extends="com.dt.train.management.center.mapper.TrainProjectDOMapper.BaseResultMap" />

  <sql id="Base_Column_List">
    <include refid="com.dt.train.management.center.mapper.TrainProjectDOMapper.Base_Column_List" />
  </sql>


  <!-- selectByCrmId -->

  <select id="selectByCrmId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from train_project
    where crm_project_id = #{crmProjectId,jdbcType=VARCHAR}
  </select>

  <sql id="Base_Where_List">
    <where>
      <if test="projectNameOrCode != null and projectNameOrCode != ''">
        project_name like concat('%', #{projectNameOrCode,jdbcType=VARCHAR}, '%')
        or project_code like concat('%', #{projectNameOrCode,jdbcType=VARCHAR}, '%')
      </if>
      <if test="projectAreaId != null">
        and project_area_id = #{projectAreaId,jdbcType=INTEGER}
      </if>
      <if test="projectAreaIds != null and projectAreaIds.size() > 0">
        and project_area_id in
        <foreach collection="projectAreaIds" item="item" open="(" separator="," close=")">
          #{item,jdbcType=INTEGER}
        </foreach>
      </if>
      <if test="projectStatus != null and projectStatus.size() > 0">
        and project_status in
        <foreach collection="projectStatus" item="item" open="(" separator="," close=")">
          #{item,jdbcType=TINYINT}
        </foreach>
      </if>
      <if test="scheduleStatus != null">
        and schedule_status = #{scheduleStatus,jdbcType=TINYINT}
      </if>
      <if test="isRemote != null">
        and is_remote = #{isRemote,jdbcType=TINYINT}
      </if>
      <if test="startDate != null">
        and end_date &gt;= #{startDate,jdbcType=TIMESTAMP}
      </if>
      <if test="endDate != null">
        and start_date &lt;= #{endDate,jdbcType=TIMESTAMP}
      </if>
      <if test="startTime != null">
        and start_date <![CDATA[ >= ]]> #{startTime,jdbcType=TIMESTAMP}
      </if>
      <if test="endTime != null">
        and start_date <![CDATA[ <= ]]> #{endTime,jdbcType=TIMESTAMP}
      </if>
      <if test="projectLevel != null">
        and project_level = #{projectLevel,jdbcType=TINYINT}
      </if>
      <if test="provinceId != null and provinceId != ''">
        and province_id = #{provinceId,jdbcType=VARCHAR}
      </if>
      <if test="cityId != null and cityId != ''">
        and city_id = #{cityId,jdbcType=VARCHAR}
      </if>
      <if test="areaId != null and areaId != ''">
        and area_id = #{areaId,jdbcType=VARCHAR}
      </if>
      <if test="projectType != null">
        and project_type = #{projectType,jdbcType=TINYINT}
      </if>
      <if test="actionType != null">
        and action_type = #{actionType,jdbcType=TINYINT}
      </if>
      <if test="projectModeDesc != null">
        and project_mode = #{projectModeDesc,jdbcType=VARCHAR}
      </if>
      <if test="syncType != null">
        and sync_type = #{syncType,jdbcType=TINYINT}
      </if>
      <if test="projectEditor != null and projectEditor != ''">
        and project_editor_name like concat('%', #{projectEditor,jdbcType=VARCHAR}, '%')
      </if>
      and invalid = 0
      <if test="courseRelationUserId != null or (excludeProjectAreaIds != null and excludeProjectAreaIds.size() > 0)">
        and id in (select project_id from train_course_user_relation where invalid = 0
          <if test="courseRelationUserId != null">
            and user_id = #{courseRelationUserId,jdbcType=BIGINT}
          </if>
          <if test="excludeProjectAreaIds != null and excludeProjectAreaIds.size() > 0">
            <if test=" projectAreaIds == null or projectAreaIds.size() == 0">
              and
            project_area_id not in
            <foreach collection="excludeProjectAreaIds" item="item" open="(" separator="," close=")">
              #{item,jdbcType=INTEGER}
            </foreach>
            </if>
            <if test=" projectAreaIds != null and projectAreaIds.size() > 0">
              and
            project_area_id in
            <foreach collection="projectAreaIds" item="item" open="(" separator="," close=")">
              #{item,jdbcType=INTEGER}
            </foreach>
            </if>
            and 
            (<foreach collection="excludeProjectAreaIds" item="deptId" separator=" OR ">
              user_dept_id like CONCAT('%', #{deptId,jdbcType=VARCHAR}, '%')
            </foreach>)
          </if>
        )
      </if>
      <if test="serviceObject != null and serviceObject != ''">
        and service_object like concat('%', #{serviceObject}, '%')
      </if>
      <if test="serviceContent != null and serviceContent != ''">
        and service_content like concat('%', #{serviceContent}, '%')
      </if>
    </where>
  </sql>

  <select id="countByQuery" resultType="java.lang.Long" parameterType="com.dt.train.management.center.model.dto.project.TrainProjectQueryDTO">
    select count(*) from train_project
    <include refid="Base_Where_List" />
  </select>

  <select id="selectByQuery" resultMap="BaseResultMap" parameterType="com.dt.train.management.center.model.dto.project.TrainProjectQueryDTO">
    select
    <include refid="Base_Column_List" />
    from train_project
    <include refid="Base_Where_List" />
    order by start_date desc, id desc
    <if test="rowStart != null and pageSize != null">
      limit #{rowStart,jdbcType=INTEGER}, #{pageSize,jdbcType=INTEGER}
    </if>
  </select>

  <!-- processCompleteStatus -->

  <update id="processCompleteStatus">
    update train_project set project_status = 2 where project_status in (0,1) 
      and end_date &lt; DATE_FORMAT(NOW(), '%Y-%m-%d 00:00:00.000')
  </update>

  <!-- processStartStatus -->

  <update id="processStartStatus">
    update train_project set project_status = 1 where project_status = 0
      and start_date &lt;= DATE_FORMAT(NOW(), '%Y-%m-%d 00:00:00.000')
      and end_date &gt;= DATE_FORMAT(NOW(), '%Y-%m-%d 00:00:00.000')
  </update>

  <!-- getProjectStatusSummary -->

  <select id="getProjectStatusSummary"
    resultType="com.dt.train.management.center.model.vo.project.ProjectStatusSummaryVO"
    parameterType="com.dt.train.management.center.model.dto.project.TrainProjectQueryDTO">
    select
      count(*) as total,
      sum(case when project_status = 2 then 1 else 0 end) as completed,
      sum(case when project_status = 1 then 1 else 0 end) as inProgress,
      sum(case when project_status = 0 then 1 else 0 end) as notStarted,
      sum(case when schedule_status = 0 then 1 else 0 end) as totalNotScheduled,
      sum(case when project_status = 2 and schedule_status = 0 then 1 else 0 end) as completedNotScheduled,
      sum(case when project_status = 1 and schedule_status = 0 then 1 else 0 end) as inProgressNotScheduled,
      sum(case when project_status = 0 and schedule_status = 0 then 1 else 0 end) as notStartedNotScheduled
    from train_project tp
    <include refid="Base_Where_List" />
  </select>

  <select id="selectByIds" resultMap="BaseResultMap" resultType="com.dt.train.management.center.model.dao.TrainProjectDO">
    select
    <include refid="Base_Column_List" />
    from train_project
    where id in
    <foreach collection="ids" item="item" open="(" separator="," close=")">
      #{item,jdbcType=VARCHAR}
    </foreach>
  </select>

  <!-- updateIsRemote --> 

  <update id="updateIsRemote">
    update train_project tp 
    set tp.is_remote = (
      case when not exists (select 1 from train_course tc where tc.project_id = tp.id and tc.invalid = 0) 
           then -1
           else (select if(count(*) > 0, 1, 0) 
                 from train_course tc 
                 where tc.project_id = tp.id and tc.is_remote = 1 and tc.invalid = 0)
      end
    )
    where tp.id = #{projectId,jdbcType=INTEGER};
  </update>
  <select id="selectCrossAreaIds" resultType="java.lang.String">
    SELECT DISTINCT
      tcur.project_area_id
    FROM
      train_course_user_relation tcur
    WHERE
      tcur.invalid = 0
      AND tcur.project_area_id IS NOT NULL
      AND tcur.project_area_id NOT IN
      <foreach collection="excludeProjectAreaIds" item="item" open="(" separator="," close=")">
        #{item,jdbcType=INTEGER}
      </foreach>
      AND (
        <foreach collection="excludeProjectAreaIds" item="deptId" separator=" OR ">
          tcur.user_dept_id like CONCAT('%', #{deptId,jdbcType=VARCHAR}, '%')
        </foreach>
      )
      <if test="userId != null">
        AND tcur.user_id = #{userId,jdbcType=VARCHAR}
      </if>
  </select>
</mapper>
