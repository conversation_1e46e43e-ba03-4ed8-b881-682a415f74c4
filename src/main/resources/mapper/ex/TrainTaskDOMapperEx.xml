<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dt.train.management.center.mapper.ex.TrainTaskDOMapperEx">

    <resultMap id="BaseResultMap" type="com.dt.train.management.center.model.dao.TrainTaskDO"
        extends="com.dt.train.management.center.mapper.TrainTaskDOMapper.BaseResultMap" />

    <sql id="Base_Column_List">
        <include refid="com.dt.train.management.center.mapper.TrainTaskDOMapper.Base_Column_List" />
    </sql>

    <sql id="Where_Clause">
        <where>
            <if test="courseId != null">
                and course_id = #{courseId,jdbcType=INTEGER}
            </if>
            <if test="projectId != null">
                and project_id = #{projectId,jdbcType=INTEGER}
            </if>
            <if test="taskType != null">
                and task_type = #{taskType,jdbcType=TINYINT}
            </if>
            and invalid = 0
        </where>
    </sql>


    <!-- selectByQuery -->

    <select id="selectByQuery" parameterType="com.dt.train.management.center.model.dto.task.TrainTaskQueryDTO" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from train_task
        <include refid="Where_Clause" />
        order by gmt_create desc
    </select>

    <!-- deleteByCourseId --> 

    <update id="deleteByCourseId">
        update train_task set invalid = 1 where course_id = #{courseId,jdbcType=INTEGER}
    </update>
    <!-- selectByCourseIds --> 

    <select id="selectByCourseIds" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from train_task
        <where>
            <if test="courseIds != null and courseIds.size() > 0">
                and course_id in
                <foreach collection="courseIds" item="courseId" separator="," open="(" close=")">
                    #{courseId,jdbcType=INTEGER}
                </foreach>
            </if>
            and invalid = 0
        </where>
    </select>
</mapper>