<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dt.train.management.center.mapper.ex.TrainCheckTaskDOMapperEx">

    <resultMap id="ResultMap" type="com.dt.train.management.center.model.dto.task.TrainCheckTaskDTOWithCourse">
        <id column="id" jdbcType="INTEGER" property="id" />
        <result column="project_id" jdbcType="INTEGER" property="projectId" />
        <result column="course_id" jdbcType="INTEGER" property="courseId" />
        <result column="task_id" jdbcType="INTEGER" property="taskId" />
        <result column="user_id" jdbcType="BIGINT" property="userId" />
        <result column="user_name" jdbcType="VARCHAR" property="userName" />
        <result column="check_dimension" jdbcType="TINYINT" property="checkDimension" />
        <result column="check_in_status" jdbcType="TINYINT" property="checkInStatus" />
        <result column="check_out_status" jdbcType="TINYINT" property="checkOutStatus" />
        <result column="check_on_site_status" jdbcType="TINYINT" property="checkOnSiteStatus" />
        <result column="check_status" jdbcType="TINYINT" property="checkStatus" />
        <result column="check_date" jdbcType="TIMESTAMP" property="checkDate" />
        <result column="project_name" jdbcType="VARCHAR" property="projectName" />
        <result column="project_code" jdbcType="VARCHAR" property="projectCode" />
        <result column="course_name" jdbcType="VARCHAR" property="courseName" />
        <result column="course_form" jdbcType="INTEGER" property="courseForm" />
      </resultMap>
      <sql id="Column_List">
        tct.id, tct.project_id, tct.course_id, tct.task_id, tct.user_id, tct.user_name, tct.check_dimension, tct.check_in_status, tct.check_out_status, 
        tct.check_on_site_status, tct.check_status, tct.check_date, tc.project_name, tc.project_code, tc.course_name, tc.course_form
      </sql>

    <sql id="Base_Where_Clause">
        <where>
            <if test="projectId != null">
                AND tct.project_id = #{projectId}
            </if>
            <if test="courseId != null">
                AND tct.course_id = #{courseId}
            </if>
            <if test="courseIds != null and courseIds.size() > 0">
                AND tct.course_id in
                <foreach collection="courseIds" item="courseId" separator="," open="(" close=")">
                    #{courseId}
                </foreach>
            </if>
            <if test="taskId != null">
                AND tct.task_id = #{taskId}
            </if>
            <if test="userId != null">
                AND tct.user_id = #{userId}
            </if>
            <if test="userIds != null and userIds.size() > 0">
                AND tct.user_id in
                <foreach collection="userIds" item="userId" separator="," open="(" close=")">
                    #{userId}
                </foreach>
            </if>
            <if test="checkStatus != null">
                AND tct.check_status = #{checkStatus}
            </if>
            <if test="startCheckDate != null">
                AND tct.check_date &gt;= #{startCheckDate}
            </if>
            <if test="endCheckDate != null">
                AND tct.check_date &lt;= #{endCheckDate} 
            </if>
            <if test="projectName != null and projectName != ''">
                AND tc.project_name like concat('%', #{projectName}, '%')
            </if>
            <if test="courseName != null and courseName != ''">
                AND tc.course_name like concat('%', #{courseName}, '%')
            </if>
            <if test="userName != null and userName != ''">
                AND tct.user_name = #{userName}
            </if>
            and tct.invalid = 0 and tc.invalid = 0
        </where>
    </sql>

    <!-- listByQuery --> 

    <select id="listByQuery" resultMap="ResultMap">
        SELECT
            <include refid="Column_List" />
        FROM
            train_check_task tct
            left join train_course tc on tct.course_id = tc.id
        <include refid="Base_Where_Clause" />
        order by check_date desc, id desc
        <if test="rowStart != null and pageSize != null">
            LIMIT #{rowStart}, #{pageSize}
        </if>
    </select>

    <!-- countByQuery --> 

    <select id="countByQuery" resultType="java.lang.Integer">
        SELECT
            COUNT(*)
        FROM
            train_check_task tct
            left join train_course tc on tct.course_id = tc.id
        <include refid="Base_Where_Clause" />
    </select>

    <!-- insertBatch --> 

    <insert id="insertBatch">
        insert into train_check_task (project_id, course_id, task_id, user_id, user_name, check_dimension, check_in_status, check_out_status, check_on_site_status, check_status, check_date)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.projectId}, #{item.courseId}, #{item.taskId}, #{item.userId}, #{item.userName}, #{item.checkDimension}, #{item.checkInStatus}, #{item.checkOutStatus}, #{item.checkOnSiteStatus}, #{item.checkStatus}, #{item.checkDate})
        </foreach>
    </insert>
</mapper>