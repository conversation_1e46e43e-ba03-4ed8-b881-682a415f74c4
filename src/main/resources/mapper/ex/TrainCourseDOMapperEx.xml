<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dt.train.management.center.mapper.ex.TrainCourseDOMapperEx">

    <resultMap id="BaseResultMap" type="com.dt.train.management.center.model.dao.TrainCourseDO"
        extends="com.dt.train.management.center.mapper.TrainCourseDOMapper.BaseResultMap" />

    <resultMap id="ComplexResultMap" type="com.dt.train.management.center.model.dao.TrainCourseDO"
        extends="com.dt.train.management.center.mapper.TrainCourseDOMapper.BaseResultMap">
        <result column="project_area" jdbcType="VARCHAR" property="projectArea" />
    </resultMap>

    <resultMap id="ResultMapWithBLOBs" type="com.dt.train.management.center.model.dao.TrainCourseDOWithBLOBs"
        extends="com.dt.train.management.center.mapper.TrainCourseDOMapper.ResultMapWithBLOBs">
    </resultMap>

    <sql id="Base_Column_List">
        <include refid="com.dt.train.management.center.mapper.TrainCourseDOMapper.Base_Column_List" />
    </sql>

    <sql id="Blob_Column_List">
        <include refid="com.dt.train.management.center.mapper.TrainCourseDOMapper.Blob_Column_List" />
    </sql>

    <sql id="JOIN_Base_Column_List">
        distinct tc.id, tc.project_id, tc.project_name, tc.project_code, tc.course_name, tc.course_status, tc.course_form, tc.course_type, tc.course_people_num, 
    tc.service_object, tc.is_remote, tc.course_area, tc.course_area_id, tc.has_plan_desc, tc.has_execution_progress, 
    tc.plan_start_date, tc.plan_end_date, tc.plan_days, tc.real_start_date, tc.real_end_date, tc.real_days, 
    tc.invalid, tc.created_by, tc.updated_by, tc.gmt_create, tc.gmt_modified, tp.project_area
    </sql>

    <!-- selectByProjectIds -->

    <select id="selectByProjectIds" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from train_course
        where project_id in
        <foreach item="projectId" index="index" collection="projectIds"
            separator="," open="(" close=")">
          #{projectId}
        </foreach>
        and invalid = 0
    </select>


    <sql id="Base_Where_List">
        <where>
            <if test="projectId != null">
            and project_id = #{projectId}
          </if>
            <if test="projectName != null and projectName != ''">
            and project_name like concat('%', #{projectName}, '%')
          </if>
            <if test="projectCode != null and projectCode != ''">
            and project_code like concat('%', #{projectCode}, '%')
          </if>
            <if test="courseName != null and courseName != ''">
            and course_name like concat('%', #{courseName}, '%')
          </if>
            <if test="courseStatus != null">
            and course_status = #{courseStatus}
          </if>
            <if test="courseType != null">
            and course_type = #{courseType}
          </if>
          <if test="courseForm != null">
            and course_form = #{courseForm}
          </if>
            <if test="serviceObject != null and serviceObject != ''">
            and tc.service_object like concat('%', #{serviceObject}, '%')
          </if>
            <if test="isRemote != null">
            and tc.is_remote = #{isRemote}
          </if>
            <if test="courseArea != null and courseArea != ''">
            and course_area like concat('%', #{courseArea}, '%')
          </if>
            <if test="courseAreaId != null">
            and course_area_id = #{courseAreaId}
          </if>
          <if test="courseAreaIds != null and courseAreaIds.size() > 0">
                and course_area_id in
                <foreach collection="courseAreaIds" item="item" open="(" separator="," close=")">
                    #{item,jdbcType=INTEGER}
                </foreach>
          </if>
          <if test="hasPlanDesc != null">
            and has_plan_desc = #{hasPlanDesc}
          </if>
          <if test="hasExecutionProgress != null">
            and has_execution_progress = #{hasExecutionProgress}
          </if>
          <if test="planStartDate != null">
            and plan_end_date &gt;= #{planStartDate}
          </if>
          <if test="planEndDate != null">
            and plan_start_date &lt;= #{planEndDate}
          </if>
          <if test="realStartDate != null">
            and real_end_date &gt;= #{realStartDate}
          </if>
          <if test="realEndDate != null">
            and real_start_date &lt;= #{realEndDate}
          </if>
          <if test="startTime != null">
             and real_start_date <![CDATA[ >= ]]> #{startTime,jdbcType=TIMESTAMP}
          </if>
          <if test="endTime != null">
             and real_start_date <![CDATA[ <= ]]> #{endTime,jdbcType=TIMESTAMP}
          </if>
          <if test="(provinceId != null and provinceId != '') or (cityId != null and cityId != '') or (areaId != null and areaId != '')">
            and tc.id in (select course_id from train_course_area_relation 
            <where>
                <if test="provinceId != null and provinceId != ''">
                    province_id = #{provinceId}
                </if>
                <if test="cityId != null and cityId != ''">
                    and city_id = #{cityId}
                </if>
                <if test="areaId != null and areaId != ''">
                    and area_id = #{areaId}
                </if>
                and invalid = 0
            </where>
        )
          </if>
          <if test="(courseRelationUserIds != null and courseRelationUserIds.size() > 0)
          or (courseRelationUserName != null and courseRelationUserName != '')">
            and tc.id in (select course_id from train_course_user_relation
            <where>
              <if test="courseRelationUserIds != null">
                and user_id in
                <foreach item="courseRelationUserId" index="index" collection="courseRelationUserIds"
                    separator="," open="(" close=")">
                    #{courseRelationUserId}
                </foreach>
              </if>
              <if test="courseRelationUserName != null and courseRelationUserName != ''">
                and user_name like concat('%', #{courseRelationUserName}, '%')
              </if>
              and invalid = 0
            </where>
            )
          </if>
          <if test="(executeUserId != null) or (executeUserName != null and executeUserName != '')">
            and tc.id in (select course_id from train_course_user_relation
            <where>
              relation_type = 2
              <if test="executeUserId != null">
                and user_id = #{executeUserId}
              </if>
              <if test="executeUserName != null and executeUserName != ''">
                and user_name like concat('%', #{executeUserName}, '%')
              </if>
              and invalid = 0
            </where>
            )
          </if>
          <if test="(supportUserId != null) or (supportUserName != null and supportUserName != '')">
            and tc.id in (select course_id from train_course_user_relation
            <where>
              relation_type = 1
              <if test="supportUserId != null">
                and user_id = #{supportUserId}
              </if>
              <if test="supportUserName != null and supportUserName != ''">
                and user_name like concat('%', #{supportUserName}, '%')
              </if>
              and invalid = 0
            </where>
            )
          </if>
          <if test="excludeCourseAreaIds != null and excludeCourseAreaIds.size() > 0">
            and tc.id in (select course_id from train_course_user_relation where invalid = 0
            <if test="courseAreaIds == null or courseAreaIds.size() == 0">
              and course_area_id not in
            <foreach collection="excludeCourseAreaIds" item="item" open="(" separator="," close=")">
              #{item,jdbcType=INTEGER}
            </foreach>
            </if>
            <if test="courseAreaIds != null and courseAreaIds.size() > 0">
              and course_area_id in
            <foreach collection="courseAreaIds" item="item" open="(" separator="," close=")">
              #{item,jdbcType=INTEGER}
            </foreach>
            </if>
            and 
            (<foreach collection="excludeCourseAreaIds" item="deptId" separator=" OR ">
              user_dept_id like CONCAT('%', #{deptId,jdbcType=VARCHAR}, '%')
            </foreach>)
          )
          </if>
          and tc.invalid = 0
        </where>
    </sql>

    <sql id="Base_Join_List">
        <if test="(provinceId != null and provinceId != '') or (cityId != null and cityId != '') or (areaId != null and areaId != '')">
            left join train_course_area_relation tcar on tcar.course_id = tc.id 
        </if>
        <if test="(courseRelationUserIds != null and courseRelationUserIds.size() > 0)
          or (executeUserId != null) or (executeUserName != null and executeUserName != '')
          or (supportUserId != null) or (supportUserName != null and supportUserName != '')
          or (courseRelationUserName != null and courseRelationUserName != '')">
            left join train_course_user_relation tcur on tcur.course_id = tc.id
        </if>
    </sql>
    <!-- countByQuery -->

    <select id="countByQuery" resultType="java.lang.Long" parameterType="com.dt.train.management.center.model.dto.project.TrainCourseQueryDTO">
        select count(1) from train_course tc
        <include refid="Base_Where_List" />
    </select>

    <!-- selectByQuery --> 

    <select id="selectByQuery" resultMap="ComplexResultMap">
        select
        <include refid="JOIN_Base_Column_List" />
        from train_course tc left join train_project tp on tc.project_id = tp.id
        <include refid="Base_Where_List" />
          order by tc.${orderBy} ${sortBy.code}, tc.real_start_date desc, tc.id desc
        <if test="rowStart != null and pageSize != null">
            limit #{rowStart,jdbcType=INTEGER}, #{pageSize,jdbcType=INTEGER}
        </if>
    </select>

  <!-- selectByPrimaryKeys --> 

  <select id="selectByPrimaryKeys" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from train_course tc
    where id in
    <foreach item="id" index="index" collection="ids"
        separator="," open="(" close=")">
        #{id}
    </foreach>
  </select>

  <select id="selectBlobByPrimaryKeys" parameterType="java.lang.Integer" resultMap="ResultMapWithBLOBs">
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from train_course
    where id in
    <foreach item="id" index="index" collection="ids"
        separator="," open="(" close=")">
        #{id}
    </foreach>
  </select>

  <!-- processCompleteStatus --> 

  <update id="processCompleteStatus">
    update train_course set course_status = 2 where course_status in (0,1) 
      and real_end_date &lt; NOW() - INTERVAL 12 HOUR;
  </update>

  <!-- processStartStatus --> 

  <update id="processStartStatus">
    update train_course set course_status = 1 where course_status = 0
      and real_start_date &lt;= NOW()
      and real_end_date &gt;= NOW() - INTERVAL 12 HOUR;
  </update>

  <!-- getCourseStatusSummary --> 

  <select id="getCourseStatusSummary" resultType="com.dt.train.management.center.model.vo.project.ProjectStatusSummaryVO">
    select
      count(distinct tc.id) as total,
      sum(case when tc.course_status = 2 then 1 else 0 end) as completed,
      sum(case when tc.course_status = 1 then 1 else 0 end) as inProgress,
      sum(case when tc.course_status = 0 then 1 else 0 end) as notStarted
    from train_course tc
    <include refid="Base_Where_List" />
  </select>  

  <select id="selectCrossAreaIds" resultType="java.lang.String">
    SELECT DISTINCT
      tcur.course_area_id
    FROM
      train_course_user_relation tcur
    WHERE
      tcur.invalid = 0
      AND tcur.course_area_id IS NOT NULL
      AND tcur.course_area_id NOT IN
      <foreach collection="excludeCourseAreaIds" item="item" open="(" separator="," close=")">
        #{item,jdbcType=INTEGER}
      </foreach>
      AND (
        <foreach collection="excludeCourseAreaIds" item="deptId" separator=" OR ">
          tcur.user_dept_id like CONCAT('%', #{deptId,jdbcType=VARCHAR}, '%')
        </foreach>
      )
      <if test="userId != null">
        AND tcur.user_id = #{userId,jdbcType=VARCHAR}
      </if>
  </select>

</mapper>