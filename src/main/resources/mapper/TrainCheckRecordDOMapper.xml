<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dt.train.management.center.mapper.TrainCheckRecordDOMapper">
  <resultMap id="BaseResultMap" type="com.dt.train.management.center.model.dao.TrainCheckRecordDO">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="project_id" jdbcType="INTEGER" property="projectId" />
    <result column="course_id" jdbcType="INTEGER" property="courseId" />
    <result column="task_id" jdbcType="INTEGER" property="taskId" />
    <result column="check_task_id" jdbcType="INTEGER" property="checkTaskId" />
    <result column="user_id" jdbcType="BIGINT" property="userId" />
    <result column="check_type" jdbcType="TINYINT" property="checkType" />
    <result column="check_time" jdbcType="TIMESTAMP" property="checkTime" />
    <result column="check_status" jdbcType="TINYINT" property="checkStatus" />
    <result column="check_latitude" jdbcType="DOUBLE" property="checkLatitude" />
    <result column="check_longitude" jdbcType="DOUBLE" property="checkLongitude" />
    <result column="check_address" jdbcType="VARCHAR" property="checkAddress" />
    <result column="check_image" jdbcType="VARCHAR" property="checkImage" />
    <result column="invalid" jdbcType="TINYINT" property="invalid" />
    <result column="created_by" jdbcType="BIGINT" property="createdBy" />
    <result column="updated_by" jdbcType="BIGINT" property="updatedBy" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
  </resultMap>
  <sql id="Base_Column_List">
    id, project_id, course_id, task_id, check_task_id, user_id, check_type, check_time, 
    check_status, check_latitude, check_longitude, check_address, check_image, invalid, 
    created_by, updated_by, gmt_create, gmt_modified
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from train_check_record
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from train_check_record
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.dt.train.management.center.model.dao.TrainCheckRecordDO">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into train_check_record (project_id, course_id, task_id, 
      check_task_id, user_id, check_type, 
      check_time, check_status, check_latitude, 
      check_longitude, check_address, check_image, 
      invalid, created_by, updated_by, 
      gmt_create, gmt_modified)
    values (#{projectId,jdbcType=INTEGER}, #{courseId,jdbcType=INTEGER}, #{taskId,jdbcType=INTEGER}, 
      #{checkTaskId,jdbcType=INTEGER}, #{userId,jdbcType=BIGINT}, #{checkType,jdbcType=TINYINT}, 
      #{checkTime,jdbcType=TIMESTAMP}, #{checkStatus,jdbcType=TINYINT}, #{checkLatitude,jdbcType=DOUBLE}, 
      #{checkLongitude,jdbcType=DOUBLE}, #{checkAddress,jdbcType=VARCHAR}, #{checkImage,jdbcType=VARCHAR}, 
      #{invalid,jdbcType=TINYINT}, #{createdBy,jdbcType=BIGINT}, #{updatedBy,jdbcType=BIGINT}, 
      #{gmtCreate,jdbcType=TIMESTAMP}, #{gmtModified,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.dt.train.management.center.model.dao.TrainCheckRecordDO">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into train_check_record
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="projectId != null">
        project_id,
      </if>
      <if test="courseId != null">
        course_id,
      </if>
      <if test="taskId != null">
        task_id,
      </if>
      <if test="checkTaskId != null">
        check_task_id,
      </if>
      <if test="userId != null">
        user_id,
      </if>
      <if test="checkType != null">
        check_type,
      </if>
      <if test="checkTime != null">
        check_time,
      </if>
      <if test="checkStatus != null">
        check_status,
      </if>
      <if test="checkLatitude != null">
        check_latitude,
      </if>
      <if test="checkLongitude != null">
        check_longitude,
      </if>
      <if test="checkAddress != null">
        check_address,
      </if>
      <if test="checkImage != null">
        check_image,
      </if>
      <if test="invalid != null">
        invalid,
      </if>
      <if test="createdBy != null">
        created_by,
      </if>
      <if test="updatedBy != null">
        updated_by,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtModified != null">
        gmt_modified,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="projectId != null">
        #{projectId,jdbcType=INTEGER},
      </if>
      <if test="courseId != null">
        #{courseId,jdbcType=INTEGER},
      </if>
      <if test="taskId != null">
        #{taskId,jdbcType=INTEGER},
      </if>
      <if test="checkTaskId != null">
        #{checkTaskId,jdbcType=INTEGER},
      </if>
      <if test="userId != null">
        #{userId,jdbcType=BIGINT},
      </if>
      <if test="checkType != null">
        #{checkType,jdbcType=TINYINT},
      </if>
      <if test="checkTime != null">
        #{checkTime,jdbcType=TIMESTAMP},
      </if>
      <if test="checkStatus != null">
        #{checkStatus,jdbcType=TINYINT},
      </if>
      <if test="checkLatitude != null">
        #{checkLatitude,jdbcType=DOUBLE},
      </if>
      <if test="checkLongitude != null">
        #{checkLongitude,jdbcType=DOUBLE},
      </if>
      <if test="checkAddress != null">
        #{checkAddress,jdbcType=VARCHAR},
      </if>
      <if test="checkImage != null">
        #{checkImage,jdbcType=VARCHAR},
      </if>
      <if test="invalid != null">
        #{invalid,jdbcType=TINYINT},
      </if>
      <if test="createdBy != null">
        #{createdBy,jdbcType=BIGINT},
      </if>
      <if test="updatedBy != null">
        #{updatedBy,jdbcType=BIGINT},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModified != null">
        #{gmtModified,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.dt.train.management.center.model.dao.TrainCheckRecordDO">
    update train_check_record
    <set>
      <if test="projectId != null">
        project_id = #{projectId,jdbcType=INTEGER},
      </if>
      <if test="courseId != null">
        course_id = #{courseId,jdbcType=INTEGER},
      </if>
      <if test="taskId != null">
        task_id = #{taskId,jdbcType=INTEGER},
      </if>
      <if test="checkTaskId != null">
        check_task_id = #{checkTaskId,jdbcType=INTEGER},
      </if>
      <if test="userId != null">
        user_id = #{userId,jdbcType=BIGINT},
      </if>
      <if test="checkType != null">
        check_type = #{checkType,jdbcType=TINYINT},
      </if>
      <if test="checkTime != null">
        check_time = #{checkTime,jdbcType=TIMESTAMP},
      </if>
      <if test="checkStatus != null">
        check_status = #{checkStatus,jdbcType=TINYINT},
      </if>
      <if test="checkLatitude != null">
        check_latitude = #{checkLatitude,jdbcType=DOUBLE},
      </if>
      <if test="checkLongitude != null">
        check_longitude = #{checkLongitude,jdbcType=DOUBLE},
      </if>
      <if test="checkAddress != null">
        check_address = #{checkAddress,jdbcType=VARCHAR},
      </if>
      <if test="checkImage != null">
        check_image = #{checkImage,jdbcType=VARCHAR},
      </if>
      <if test="invalid != null">
        invalid = #{invalid,jdbcType=TINYINT},
      </if>
      <if test="createdBy != null">
        created_by = #{createdBy,jdbcType=BIGINT},
      </if>
      <if test="updatedBy != null">
        updated_by = #{updatedBy,jdbcType=BIGINT},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModified != null">
        gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.dt.train.management.center.model.dao.TrainCheckRecordDO">
    update train_check_record
    set project_id = #{projectId,jdbcType=INTEGER},
      course_id = #{courseId,jdbcType=INTEGER},
      task_id = #{taskId,jdbcType=INTEGER},
      check_task_id = #{checkTaskId,jdbcType=INTEGER},
      user_id = #{userId,jdbcType=BIGINT},
      check_type = #{checkType,jdbcType=TINYINT},
      check_time = #{checkTime,jdbcType=TIMESTAMP},
      check_status = #{checkStatus,jdbcType=TINYINT},
      check_latitude = #{checkLatitude,jdbcType=DOUBLE},
      check_longitude = #{checkLongitude,jdbcType=DOUBLE},
      check_address = #{checkAddress,jdbcType=VARCHAR},
      check_image = #{checkImage,jdbcType=VARCHAR},
      invalid = #{invalid,jdbcType=TINYINT},
      created_by = #{createdBy,jdbcType=BIGINT},
      updated_by = #{updatedBy,jdbcType=BIGINT},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_modified = #{gmtModified,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>

  <!-- selectByQuery --> 

  <select id="selectByQuery" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from train_check_record
    <where>
      <if test="projectId != null">
        and project_id = #{projectId,jdbcType=INTEGER}
      </if>
      <if test="taskId != null">
        and task_id = #{taskId,jdbcType=INTEGER}
      </if>
      <if test="checkTaskId != null">
        and check_task_id = #{checkTaskId,jdbcType=INTEGER}
      </if>
      <if test="userId != null">
        and user_id = #{userId,jdbcType=BIGINT}
      </if>
      <if test="courseIds != null and courseIds.size() > 0">
        and course_id in
        <foreach collection="courseIds" item="courseId" separator="," open="(" close=")">
          #{courseId,jdbcType=INTEGER}
        </foreach>
      </if>
      <if test="startCheckTime != null">
        and check_time &gt;= #{startCheckTime,jdbcType=TIMESTAMP}
      </if>
      <if test="endCheckTime != null">
        and check_time &lt;= #{endCheckTime,jdbcType=TIMESTAMP}
      </if>
      <if test="checkType != null">
        and check_type = #{checkType,jdbcType=TINYINT}
      </if>
      <if test="checkStatus != null">
        and check_status = #{checkStatus,jdbcType=TINYINT}
      </if>
    </where>
    order by check_time asc
  </select>
</mapper>