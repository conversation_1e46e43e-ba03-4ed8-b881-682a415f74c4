<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dt.train.management.center.mapper.TrainTaskDOMapper">
  <resultMap id="BaseResultMap" type="com.dt.train.management.center.model.dao.TrainTaskDO">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="project_id" jdbcType="INTEGER" property="projectId" />
    <result column="course_id" jdbcType="INTEGER" property="courseId" />
    <result column="task_name" jdbcType="VARCHAR" property="taskName" />
    <result column="task_type" jdbcType="TINYINT" property="taskType" />
    <result column="task_desc" jdbcType="VARCHAR" property="taskDesc" />
    <result column="invalid" jdbcType="TINYINT" property="invalid" />
    <result column="created_by" jdbcType="BIGINT" property="createdBy" />
    <result column="updated_by" jdbcType="BIGINT" property="updatedBy" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
  </resultMap>
  <sql id="Base_Column_List">
    id, project_id, course_id, task_name, task_type, task_desc, invalid, created_by, 
    updated_by, gmt_create, gmt_modified
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from train_task
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from train_task
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.dt.train.management.center.model.dao.TrainTaskDO">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into train_task (project_id, course_id, task_name, 
      task_type, task_desc, invalid, 
      created_by, updated_by, gmt_create, 
      gmt_modified)
    values (#{projectId,jdbcType=INTEGER}, #{courseId,jdbcType=INTEGER}, #{taskName,jdbcType=VARCHAR}, 
      #{taskType,jdbcType=TINYINT}, #{taskDesc,jdbcType=VARCHAR}, #{invalid,jdbcType=TINYINT}, 
      #{createdBy,jdbcType=BIGINT}, #{updatedBy,jdbcType=BIGINT}, #{gmtCreate,jdbcType=TIMESTAMP}, 
      #{gmtModified,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.dt.train.management.center.model.dao.TrainTaskDO">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into train_task
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="projectId != null">
        project_id,
      </if>
      <if test="courseId != null">
        course_id,
      </if>
      <if test="taskName != null">
        task_name,
      </if>
      <if test="taskType != null">
        task_type,
      </if>
      <if test="taskDesc != null">
        task_desc,
      </if>
      <if test="invalid != null">
        invalid,
      </if>
      <if test="createdBy != null">
        created_by,
      </if>
      <if test="updatedBy != null">
        updated_by,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtModified != null">
        gmt_modified,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="projectId != null">
        #{projectId,jdbcType=INTEGER},
      </if>
      <if test="courseId != null">
        #{courseId,jdbcType=INTEGER},
      </if>
      <if test="taskName != null">
        #{taskName,jdbcType=VARCHAR},
      </if>
      <if test="taskType != null">
        #{taskType,jdbcType=TINYINT},
      </if>
      <if test="taskDesc != null">
        #{taskDesc,jdbcType=VARCHAR},
      </if>
      <if test="invalid != null">
        #{invalid,jdbcType=TINYINT},
      </if>
      <if test="createdBy != null">
        #{createdBy,jdbcType=BIGINT},
      </if>
      <if test="updatedBy != null">
        #{updatedBy,jdbcType=BIGINT},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModified != null">
        #{gmtModified,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.dt.train.management.center.model.dao.TrainTaskDO">
    update train_task
    <set>
      <if test="projectId != null">
        project_id = #{projectId,jdbcType=INTEGER},
      </if>
      <if test="courseId != null">
        course_id = #{courseId,jdbcType=INTEGER},
      </if>
      <if test="taskName != null">
        task_name = #{taskName,jdbcType=VARCHAR},
      </if>
      <if test="taskType != null">
        task_type = #{taskType,jdbcType=TINYINT},
      </if>
      <if test="taskDesc != null">
        task_desc = #{taskDesc,jdbcType=VARCHAR},
      </if>
      <if test="invalid != null">
        invalid = #{invalid,jdbcType=TINYINT},
      </if>
      <if test="createdBy != null">
        created_by = #{createdBy,jdbcType=BIGINT},
      </if>
      <if test="updatedBy != null">
        updated_by = #{updatedBy,jdbcType=BIGINT},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModified != null">
        gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.dt.train.management.center.model.dao.TrainTaskDO">
    update train_task
    set project_id = #{projectId,jdbcType=INTEGER},
      course_id = #{courseId,jdbcType=INTEGER},
      task_name = #{taskName,jdbcType=VARCHAR},
      task_type = #{taskType,jdbcType=TINYINT},
      task_desc = #{taskDesc,jdbcType=VARCHAR},
      invalid = #{invalid,jdbcType=TINYINT},
      created_by = #{createdBy,jdbcType=BIGINT},
      updated_by = #{updatedBy,jdbcType=BIGINT},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_modified = #{gmtModified,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>