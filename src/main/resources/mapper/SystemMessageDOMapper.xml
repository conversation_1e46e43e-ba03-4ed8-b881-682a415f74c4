<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dt.train.management.center.mapper.SystemMessageDOMapper">
  <resultMap id="BaseResultMap" type="com.dt.train.management.center.model.dao.SystemMessageDO">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="user_id" jdbcType="BIGINT" property="userId" />
    <result column="message_title" jdbcType="VARCHAR" property="messageTitle" />
    <result column="message_content" jdbcType="VARCHAR" property="messageContent" />
    <result column="message_type" jdbcType="TINYINT" property="messageType" />
    <result column="is_read" jdbcType="TINYINT" property="isRead" />
    <result column="business_id" jdbcType="BIGINT" property="businessId" />
    <result column="business_type" jdbcType="TINYINT" property="businessType" />
    <result column="ext_info" jdbcType="VARCHAR" property="extInfo" />
    <result column="invalid" jdbcType="TINYINT" property="invalid" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
  </resultMap>
  <sql id="Base_Column_List">
    id, user_id, message_title, message_content, message_type, is_read, business_id, 
    business_type, ext_info, invalid, gmt_create
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from system_message
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from system_message
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.dt.train.management.center.model.dao.SystemMessageDO">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into system_message (user_id, message_title, message_content, 
      message_type, is_read, business_id, 
      business_type, ext_info, invalid, 
      gmt_create)
    values (#{userId,jdbcType=BIGINT}, #{messageTitle,jdbcType=VARCHAR}, #{messageContent,jdbcType=VARCHAR}, 
      #{messageType,jdbcType=TINYINT}, #{isRead,jdbcType=TINYINT}, #{businessId,jdbcType=BIGINT}, 
      #{businessType,jdbcType=TINYINT}, #{extInfo,jdbcType=VARCHAR}, #{invalid,jdbcType=TINYINT}, 
      #{gmtCreate,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.dt.train.management.center.model.dao.SystemMessageDO">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into system_message
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="userId != null">
        user_id,
      </if>
      <if test="messageTitle != null">
        message_title,
      </if>
      <if test="messageContent != null">
        message_content,
      </if>
      <if test="messageType != null">
        message_type,
      </if>
      <if test="isRead != null">
        is_read,
      </if>
      <if test="businessId != null">
        business_id,
      </if>
      <if test="businessType != null">
        business_type,
      </if>
      <if test="extInfo != null">
        ext_info,
      </if>
      <if test="invalid != null">
        invalid,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="userId != null">
        #{userId,jdbcType=BIGINT},
      </if>
      <if test="messageTitle != null">
        #{messageTitle,jdbcType=VARCHAR},
      </if>
      <if test="messageContent != null">
        #{messageContent,jdbcType=VARCHAR},
      </if>
      <if test="messageType != null">
        #{messageType,jdbcType=TINYINT},
      </if>
      <if test="isRead != null">
        #{isRead,jdbcType=TINYINT},
      </if>
      <if test="businessId != null">
        #{businessId,jdbcType=BIGINT},
      </if>
      <if test="businessType != null">
        #{businessType,jdbcType=TINYINT},
      </if>
      <if test="extInfo != null">
        #{extInfo,jdbcType=VARCHAR},
      </if>
      <if test="invalid != null">
        #{invalid,jdbcType=TINYINT},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.dt.train.management.center.model.dao.SystemMessageDO">
    update system_message
    <set>
      <if test="userId != null">
        user_id = #{userId,jdbcType=BIGINT},
      </if>
      <if test="messageTitle != null">
        message_title = #{messageTitle,jdbcType=VARCHAR},
      </if>
      <if test="messageContent != null">
        message_content = #{messageContent,jdbcType=VARCHAR},
      </if>
      <if test="messageType != null">
        message_type = #{messageType,jdbcType=TINYINT},
      </if>
      <if test="isRead != null">
        is_read = #{isRead,jdbcType=TINYINT},
      </if>
      <if test="businessId != null">
        business_id = #{businessId,jdbcType=BIGINT},
      </if>
      <if test="businessType != null">
        business_type = #{businessType,jdbcType=TINYINT},
      </if>
      <if test="extInfo != null">
        ext_info = #{extInfo,jdbcType=VARCHAR},
      </if>
      <if test="invalid != null">
        invalid = #{invalid,jdbcType=TINYINT},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.dt.train.management.center.model.dao.SystemMessageDO">
    update system_message
    set user_id = #{userId,jdbcType=BIGINT},
      message_title = #{messageTitle,jdbcType=VARCHAR},
      message_content = #{messageContent,jdbcType=VARCHAR},
      message_type = #{messageType,jdbcType=TINYINT},
      is_read = #{isRead,jdbcType=TINYINT},
      business_id = #{businessId,jdbcType=BIGINT},
      business_type = #{businessType,jdbcType=TINYINT},
      ext_info = #{extInfo,jdbcType=VARCHAR},
      invalid = #{invalid,jdbcType=TINYINT},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>

  <sql id="Base_Where_Clause">
    <where>
      <if test="userId != null">
        AND user_id = #{userId}
      </if>
      <if test="messageTitle != null and messageTitle != ''">
        AND message_title like concat('%', #{messageTitle}, '%')
      </if>
      <if test="messageContent != null and messageContent != ''">
        AND message_content like concat('%', #{messageContent}, '%')
      </if>
      <if test="messageType != null">
        AND message_type = #{messageType}
      </if>
      <if test="isRead != null">
        AND is_read = #{isRead}
      </if>
      <if test="businessId != null">
        AND business_id = #{businessId}
      </if>
      <if test="businessType != null">
        AND business_type = #{businessType}
      </if>
      <if test="startDate != null">
        AND gmt_create &gt;= #{startDate}
      </if>
      <if test="endDate != null">
        AND gmt_create &lt;= #{endDate}
      </if>
      <if test="invalid != null">
        AND invalid = #{invalid}
      </if>

    </where>
  </sql>

  <select id="query" resultMap="BaseResultMap">
    SELECT * FROM system_message
    <include refid="Base_Where_Clause" />
    ORDER BY id DESC
    <if test="rowStart != null and pageSize != null">
      limit #{rowStart,jdbcType=INTEGER}, #{pageSize,jdbcType=INTEGER}
    </if>
</select>

<select id="count" resultType="long">
    SELECT count(*) FROM system_message
    <include refid="Base_Where_Clause" />
</select>

<update id="markAsRead">
    UPDATE system_message
    SET is_read = 1
    WHERE id = #{messageId} AND is_read = 0
</update>

<update id="markAllAsReadByUserId">
    UPDATE system_message
    SET is_read = 1
    WHERE user_id = #{userId} AND is_read = 0
</update>

  <!-- insertBatch --> 

  <insert id="insertBatch">
    insert into system_message (user_id, message_title, message_content, 
      message_type, is_read, business_id, 
      business_type, ext_info, 
      gmt_create)
    values
    <foreach collection="systemMessageDOs" item="item" separator=",">
      (#{item.userId,jdbcType=BIGINT}, #{item.messageTitle,jdbcType=VARCHAR}, #{item.messageContent,jdbcType=VARCHAR}, 
      #{item.messageType,jdbcType=TINYINT}, #{item.isRead,jdbcType=TINYINT}, #{item.businessId,jdbcType=BIGINT}, 
      #{item.businessType,jdbcType=TINYINT}, #{item.extInfo,jdbcType=VARCHAR}, 
      #{item.gmtCreate,jdbcType=TIMESTAMP})
    </foreach>
  </insert>

  <!-- deleteBatch --> 

  <update id="deleteBatch" parameterType="java.util.List">
    update system_message
    set invalid = 1
    where id in
    <foreach collection="messageIds" item="item" separator="," open="(" close=")">
      #{item,jdbcType=BIGINT}
    </foreach>
  </update>
</mapper>