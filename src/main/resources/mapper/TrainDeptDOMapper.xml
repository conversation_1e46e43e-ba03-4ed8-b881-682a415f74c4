<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dt.train.management.center.mapper.TrainDeptDOMapper">
  <resultMap id="BaseResultMap" type="com.dt.train.management.center.model.dao.TrainDeptDO">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="parent_id" jdbcType="INTEGER" property="parentId" />
    <result column="dept_name" jdbcType="VARCHAR" property="deptName" />
    <result column="dept_name_short" jdbcType="VARCHAR" property="deptNameShort" />
    <result column="dept_type" jdbcType="TINYINT" property="deptType" />
    <result column="dept_level" jdbcType="INTEGER" property="deptLevel" />
    <result column="hidden" jdbcType="TINYINT" property="hidden" />
    <result column="invalid" jdbcType="TINYINT" property="invalid" />
    <result column="created_by" jdbcType="BIGINT" property="createdBy" />
    <result column="updated_by" jdbcType="BIGINT" property="updatedBy" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
  </resultMap>
  <sql id="Base_Column_List">
    id, parent_id, dept_name,dept_name_short, dept_type, dept_level, hidden, invalid, created_by,
    updated_by, gmt_create, gmt_modified
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from train_dept
    where id = #{id,jdbcType=INTEGER}
  </select>
  <select id="selectByDeptNames" resultType="com.dt.train.management.center.model.dao.TrainDeptDO" resultMap="BaseResultMap">
    SELECT
    <include refid="Base_Column_List" />
    FROM train_dept
    WHERE dept_name IN
    <foreach item="name" collection="deptNames" open="(" separator="," close=")">
      #{name,jdbcType=VARCHAR}
    </foreach>
    AND invalid = 0
    ORDER BY id ASC
  </select>
  <select id="selectChildrenByParentIds" resultType="com.dt.train.management.center.model.dao.TrainDeptDO" resultMap="BaseResultMap">
    SELECT
    <include refid="Base_Column_List" />
    FROM train_dept
    WHERE parent_id IN
    <foreach item="id" collection="parentDeptIds" open="(" separator="," close=")">
      #{id}
    </foreach>
    AND invalid = 0
  </select>
  <select id="selectAllDepts" resultType="com.dt.train.management.center.model.dao.TrainDeptDO" resultMap="BaseResultMap">
    SELECT
    <include refid="Base_Column_List" />
    FROM train_dept
    WHERE invalid = 0
  </select>
  <select id="selectByDeptIds" resultType="com.dt.train.management.center.model.dao.TrainDeptDO" resultMap="BaseResultMap">
    SELECT
    <include refid="Base_Column_List" />
    FROM train_dept
    WHERE invalid = 0
    AND id IN
    <foreach item="id" collection="deptIds" open="(" separator="," close=")">
      #{id}
    </foreach>
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from train_dept
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.dt.train.management.center.model.dao.TrainDeptDO">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into train_dept (parent_id,  dept_name,dept_name_short,
      dept_type, dept_level, hidden, 
      invalid, created_by, updated_by, 
      gmt_create, gmt_modified)
    values (#{parentId,jdbcType=INTEGER}, #{deptName,jdbcType=VARCHAR},#{deptNameShort,jdbcType=VARCHAR},
      #{deptType,jdbcType=TINYINT}, #{deptLevel,jdbcType=INTEGER}, #{hidden,jdbcType=TINYINT}, 
      #{invalid,jdbcType=TINYINT}, #{createdBy,jdbcType=BIGINT}, #{updatedBy,jdbcType=BIGINT}, 
      #{gmtCreate,jdbcType=TIMESTAMP}, #{gmtModified,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.dt.train.management.center.model.dao.TrainDeptDO">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into train_dept
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="parentId != null">
        parent_id,
      </if>
      <if test="deptName != null">
        dept_name,
      </if>
      <if test="deptNameShort != null">
        dept_name_short,
      </if>
      <if test="deptType != null">
        dept_type,
      </if>
      <if test="deptLevel != null">
        dept_level,
      </if>
      <if test="hidden != null">
        hidden,
      </if>
      <if test="invalid != null">
        invalid,
      </if>
      <if test="createdBy != null">
        created_by,
      </if>
      <if test="updatedBy != null">
        updated_by,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtModified != null">
        gmt_modified,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="parentId != null">
        #{parentId,jdbcType=INTEGER},
      </if>
      <if test="deptName != null">
        #{deptName,jdbcType=VARCHAR},
      </if>
      <if test="deptNameShort != null">
        #{deptNameShort,jdbcType=VARCHAR},
      </if>
      <if test="deptType != null">
        #{deptType,jdbcType=TINYINT},
      </if>
      <if test="deptLevel != null">
        #{deptLevel,jdbcType=INTEGER},
      </if>
      <if test="hidden != null">
        #{hidden,jdbcType=TINYINT},
      </if>
      <if test="invalid != null">
        #{invalid,jdbcType=TINYINT},
      </if>
      <if test="createdBy != null">
        #{createdBy,jdbcType=BIGINT},
      </if>
      <if test="updatedBy != null">
        #{updatedBy,jdbcType=BIGINT},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModified != null">
        #{gmtModified,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.dt.train.management.center.model.dao.TrainDeptDO">
    update train_dept
    <set>
      <if test="parentId != null">
        parent_id = #{parentId,jdbcType=INTEGER},
      </if>
      <if test="deptName != null">
        dept_name = #{deptName,jdbcType=VARCHAR},
      </if>
      <if test="deptNameShort != null">
        dept_name_short = #{deptNameShort,jdbcType=VARCHAR},
      </if>
      <if test="deptType != null">
        dept_type = #{deptType,jdbcType=TINYINT},
      </if>
      <if test="deptLevel != null">
        dept_level = #{deptLevel,jdbcType=INTEGER},
      </if>
      <if test="hidden != null">
        hidden = #{hidden,jdbcType=TINYINT},
      </if>
      <if test="invalid != null">
        invalid = #{invalid,jdbcType=TINYINT},
      </if>
      <if test="createdBy != null">
        created_by = #{createdBy,jdbcType=BIGINT},
      </if>
      <if test="updatedBy != null">
        updated_by = #{updatedBy,jdbcType=BIGINT},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModified != null">
        gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.dt.train.management.center.model.dao.TrainDeptDO">
    update train_dept
    set parent_id = #{parentId,jdbcType=INTEGER},
      dept_name = #{deptName,jdbcType=VARCHAR},
      dept_name_short = #{deptNameShort,jdbcType=VARCHAR},
      dept_type = #{deptType,jdbcType=TINYINT},
      dept_level = #{deptLevel,jdbcType=INTEGER},
      hidden = #{hidden,jdbcType=TINYINT},
      invalid = #{invalid,jdbcType=TINYINT},
      created_by = #{createdBy,jdbcType=BIGINT},
      updated_by = #{updatedBy,jdbcType=BIGINT},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_modified = #{gmtModified,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>