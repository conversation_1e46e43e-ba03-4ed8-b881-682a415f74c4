<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dt.train.management.center.mapper.CommonLogDOMapper">
  <resultMap id="BaseResultMap" type="com.dt.train.management.center.model.dao.CommonLogDO">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="log_type" jdbcType="VARCHAR" property="logType" />
    <result column="log_second_type" jdbcType="VARCHAR" property="logSecondType" />
    <result column="ip" jdbcType="VARCHAR" property="ip" />
    <result column="user_id" jdbcType="VARCHAR" property="userId" />
    <result column="user_name" jdbcType="VARCHAR" property="userName" />
    <result column="business_id" jdbcType="VARCHAR" property="businessId" />
    <result column="business_type" jdbcType="VARCHAR" property="businessType" />
    <result column="ext_info" jdbcType="VARCHAR" property="extInfo" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.dt.train.management.center.model.dao.CommonLogDO">
    <result column="detail" jdbcType="LONGVARCHAR" property="detail" />
  </resultMap>
  <sql id="Base_Column_List">
    id, log_type, log_second_type, ip, user_id, user_name, business_id, business_type, ext_info, 
    gmt_create
  </sql>
  <sql id="Blob_Column_List">
    detail
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="ResultMapWithBLOBs">
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from common_log
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from common_log
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.dt.train.management.center.model.dao.CommonLogDO">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into common_log (log_type, log_second_type, ip, 
      user_id, user_name, business_id, business_type, 
      ext_info, gmt_create, detail
      )
    values (#{logType,jdbcType=VARCHAR}, #{logSecondType,jdbcType=VARCHAR}, #{ip,jdbcType=VARCHAR}, 
      #{userId,jdbcType=VARCHAR}, #{userName,jdbcType=VARCHAR}, #{businessId,jdbcType=VARCHAR}, #{businessType,jdbcType=VARCHAR}, 
      #{extInfo,jdbcType=VARCHAR}, #{gmtCreate,jdbcType=TIMESTAMP}, #{detail,jdbcType=LONGVARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.dt.train.management.center.model.dao.CommonLogDO">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into common_log
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="logType != null">
        log_type,
      </if>
      <if test="logSecondType != null">
        log_second_type,
      </if>
      <if test="ip != null">
        ip,
      </if>
      <if test="userId != null">
        user_id,
      </if>
      <if test="userName != null">
        user_name,
      </if>
      <if test="businessId != null">
        business_id,
      </if>
      <if test="businessType != null">
        business_type,
      </if>
      <if test="extInfo != null">
        ext_info,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="detail != null">
        detail,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="logType != null">
        #{logType,jdbcType=VARCHAR},
      </if>
      <if test="logSecondType != null">
        #{logSecondType,jdbcType=VARCHAR},
      </if>
      <if test="ip != null">
        #{ip,jdbcType=VARCHAR},
      </if>
      <if test="userId != null">
        #{userId,jdbcType=VARCHAR},
      </if>
      <if test="userName != null">
        #{userName,jdbcType=VARCHAR},
      </if>
      <if test="businessId != null">
        #{businessId,jdbcType=VARCHAR},
      </if>
      <if test="businessType != null">
        #{businessType,jdbcType=VARCHAR},
      </if>
      <if test="extInfo != null">
        #{extInfo,jdbcType=VARCHAR},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="detail != null">
        #{detail,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.dt.train.management.center.model.dao.CommonLogDO">
    update common_log
    <set>
      <if test="logType != null">
        log_type = #{logType,jdbcType=VARCHAR},
      </if>
      <if test="logSecondType != null">
        log_second_type = #{logSecondType,jdbcType=VARCHAR},
      </if>
      <if test="ip != null">
        ip = #{ip,jdbcType=VARCHAR},
      </if>
      <if test="userId != null">
        user_id = #{userId,jdbcType=VARCHAR},
      </if>
      <if test="userName != null">
        user_name = #{userName,jdbcType=VARCHAR},
      </if>
      <if test="businessId != null">
        business_id = #{businessId,jdbcType=VARCHAR},
      </if>
      <if test="businessType != null">
        business_type = #{businessType,jdbcType=VARCHAR},
      </if>
      <if test="extInfo != null">
        ext_info = #{extInfo,jdbcType=VARCHAR},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="detail != null">
        detail = #{detail,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.dt.train.management.center.model.dao.CommonLogDO">
    update common_log
    set log_type = #{logType,jdbcType=VARCHAR},
      log_second_type = #{logSecondType,jdbcType=VARCHAR},
      ip = #{ip,jdbcType=VARCHAR},
      user_id = #{userId,jdbcType=VARCHAR},
      user_name = #{userName,jdbcType=VARCHAR},
      business_id = #{businessId,jdbcType=VARCHAR},
      business_type = #{businessType,jdbcType=VARCHAR},
      ext_info = #{extInfo,jdbcType=VARCHAR},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      detail = #{detail,jdbcType=LONGVARCHAR}
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.dt.train.management.center.model.dao.CommonLogDO">
    update common_log
    set log_type = #{logType,jdbcType=VARCHAR},
      log_second_type = #{logSecondType,jdbcType=VARCHAR},
      ip = #{ip,jdbcType=VARCHAR},
      user_id = #{userId,jdbcType=VARCHAR},
      user_name = #{userName,jdbcType=VARCHAR},
      business_id = #{businessId,jdbcType=VARCHAR},
      business_type = #{businessType,jdbcType=VARCHAR},
      ext_info = #{extInfo,jdbcType=VARCHAR},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>

  <sql id="where_query">
    <where>
      <if test="logType != null">
        and log_type = #{logType,jdbcType=VARCHAR}
      </if>
      <if test="logSecondType != null and logSecondType != ''">
        and log_second_type = #{logSecondType,jdbcType=VARCHAR}
      </if>
      <if test="userId != null and userId != ''">
        and user_id = #{userId,jdbcType=VARCHAR}
      </if>
      <if test="userName != null and userName != ''">
        and user_name like concat('%', #{userName,jdbcType=VARCHAR}, '%')
      </if>
      <if test="businessId != null and businessId != ''">
        and business_id = #{businessId,jdbcType=VARCHAR}
      </if>
      <if test="businessType != null">
        and business_type = #{businessType,jdbcType=VARCHAR}
      </if>
      <if test="ip != null and ip != ''">
        and ip like concat('%', #{ip,jdbcType=VARCHAR}, '%')
      </if>
    </where>
  </sql>
   <!-- selectByQuery --> 

  <select id="selectByQuery" resultMap="ResultMapWithBLOBs">
    select
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from common_log
    <include refid="where_query"/>
    order by gmt_create desc
    <if test="rowStart != null and pageSize != null">
      limit #{rowStart,jdbcType=INTEGER}, #{pageSize,jdbcType=INTEGER}
    </if>
  </select>  
</mapper>