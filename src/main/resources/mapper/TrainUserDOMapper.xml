<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dt.train.management.center.mapper.TrainUserDOMapper">
  <resultMap id="BaseResultMap" type="com.dt.train.management.center.model.dao.TrainUserDO">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="user_id" jdbcType="BIGINT" property="userId" />
    <result column="ding_talk_user_id" jdbcType="INTEGER" property="dingTalkUserId" />
    <result column="crm_user_id" jdbcType="VARCHAR" property="crmUserId" />
    <result column="dept_ids" jdbcType="VARCHAR" property="deptIds" />
    <result column="user_name" jdbcType="VARCHAR" property="userName" />
    <result column="email" jdbcType="VARCHAR" property="email" />
    <result column="phone" jdbcType="VARCHAR" property="phone" />
    <result column="user_position" jdbcType="VARCHAR" property="userPosition" />
    <result column="user_role" jdbcType="VARCHAR" property="userRole" />
    <result column="role_id" jdbcType="INTEGER" property="roleId" />
    <result column="work_place" jdbcType="VARCHAR" property="workPlace" />
    <result column="user_status" jdbcType="TINYINT" property="userStatus" />
    <result column="user_type" jdbcType="TINYINT" property="userType" />
    <result column="invalid" jdbcType="TINYINT" property="invalid" />
    <result column="created_by" jdbcType="BIGINT" property="createdBy" />
    <result column="updated_by" jdbcType="BIGINT" property="updatedBy" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
    <result column="manage_dept_ids" jdbcType="VARCHAR" property="manageDeptIds" />
  </resultMap>
  <sql id="Base_Column_List">
    id, user_id, ding_talk_user_id, crm_user_id, dept_ids, manage_dept_ids, user_name, email, phone, user_position,user_role, role_id,
    work_place, user_status, user_type, invalid, created_by, updated_by, gmt_create, gmt_modified
  </sql>
  <sql id="Base_Where_List">
      <if test="userId != null">
        AND user_id = #{userId,jdbcType=BIGINT}
      </if>
      <if test="dingTalkUserId != null">
        AND ding_talk_user_id = #{dingTalkUserId,jdbcType=INTEGER}
      </if>
      <if test="crmUserId != null">
        AND crm_user_id = #{crmUserId,jdbcType=VARCHAR}
      </if>
      <if test="userName != null">
        AND user_name LIKE CONCAT('%', #{userName,jdbcType=VARCHAR}, '%')
      </if>
      <if test="userNameExact != null and userNameExact != ''">
        AND user_name = #{userNameExact,jdbcType=VARCHAR}
      </if>
      <if test="phone != null">
        AND phone = #{phone,jdbcType=VARCHAR}
      </if>
      <if test="email != null">
        AND email = #{email,jdbcType=VARCHAR}
      </if>
      <if test="userPosition != null">
        AND user_position = #{userPosition,jdbcType=VARCHAR}
      </if>
      <if test="roleId != null">
        AND role_id = #{roleId,jdbcType=INTEGER}
      </if>
      <if test="workPlace != null">
        AND work_place = #{workPlace,jdbcType=VARCHAR}
      </if>
      <if test="userStatus != null">
        AND user_status = #{userStatus,jdbcType=TINYINT}
      </if>
      <if test="userType != null">
        AND user_type = #{userType,jdbcType=TINYINT}
      </if>
      <if test="deptIds != null and deptIds.size() > 0">
      AND (
      <foreach collection="deptIds" item="deptId" separator=" OR ">
        dept_ids like CONCAT('%', #{deptId,jdbcType=VARCHAR}, '%')
      </foreach>
      )
     </if>
      <if test="manageDeptIds != null and manageDeptIds.size() > 0">
      AND (
      <foreach collection="manageDeptIds" item="manageDeptId" separator=" OR ">
        manage_dept_ids like CONCAT('%', #{manageDeptId,jdbcType=VARCHAR}, '%')
      </foreach>
      )
     </if>
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from train_user
    where id = #{id,jdbcType=INTEGER}
  </select>
  <select id="selectByPhones" resultMap="BaseResultMap">
    SELECT
    <include refid="Base_Column_List" />
    FROM train_user
    WHERE phone IN
    <foreach item="phone" collection="phoneList" open="(" separator="," close=")">
      #{phone,jdbcType=VARCHAR}
    </foreach>
    AND invalid = 0
  </select>
  <select id="selectAllByPhones" resultMap="BaseResultMap">
    SELECT
    <include refid="Base_Column_List" />
    FROM train_user
    WHERE phone IN
    <foreach item="phone" collection="phoneList" open="(" separator="," close=")">
      #{phone,jdbcType=VARCHAR}
    </foreach>
  </select>
    <select id="selectValidUsers" resultType="com.dt.train.management.center.model.dao.TrainUserDO" resultMap="BaseResultMap">
      SELECT
      <include refid="Base_Column_List" />
      FROM train_user
      WHERE 1=1
      AND invalid = 0
    </select>
    <select id="selectByUserId" resultType="com.dt.train.management.center.model.dao.TrainUserDO" resultMap="BaseResultMap">
      SELECT
      <include refid="Base_Column_List" />
      FROM train_user
      WHERE 1=1
      AND user_id = #{userId,jdbcType=BIGINT}
      AND invalid = 0
    </select>
  <select id="selectByQuery" resultType="com.dt.train.management.center.model.dao.TrainUserDO" resultMap="BaseResultMap" >
    select
    <include refid="Base_Column_List" />
    from train_user
    where invalid = 0 and dept_ids != '20000'
    <include refid="Base_Where_List" />
    order by CAST(SUBSTRING_INDEX(dept_ids, ';', 1) AS UNSIGNED) asc, user_type asc, id asc
    <if test="rowStart != null and pageSize != null">
      limit #{rowStart,jdbcType=INTEGER}, #{pageSize,jdbcType=INTEGER}
    </if>
  </select>
  <select id="countByQuery" resultType="java.lang.Long" parameterType="com.dt.train.management.center.model.dao.TrainUserDO">
    select count(*) from train_user
    where invalid = 0 and dept_ids != '20000'
    <include refid="Base_Where_List" />
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from train_user
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.dt.train.management.center.model.dao.TrainUserDO">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into train_user (user_id, ding_talk_user_id, crm_user_id,
      dept_ids, manage_dept_ids, user_name, email,
      phone, user_position,user_role, role_id, work_place,
      user_status, user_type, invalid, created_by,
      updated_by, gmt_create, gmt_modified
      )
    values (#{userId,jdbcType=BIGINT}, #{dingTalkUserId,jdbcType=VARCHAR}, #{crmUserId,jdbcType=INTEGER},
      #{deptIds,jdbcType=VARCHAR}, #{manageDeptIds,jdbcType=VARCHAR}, #{userName,jdbcType=VARCHAR}, #{email,jdbcType=VARCHAR},
      #{phone,jdbcType=VARCHAR}, #{userPosition,jdbcType=VARCHAR},#{userRole,jdbcType=VARCHAR}, #{roleId,jdbcType=INTEGER}, #{workPlace,jdbcType=VARCHAR},
      #{userStatus,jdbcType=TINYINT}, #{userType,jdbcType=TINYINT}, #{invalid,jdbcType=TINYINT}, #{createdBy,jdbcType=BIGINT},
      #{updatedBy,jdbcType=BIGINT}, #{gmtCreate,jdbcType=TIMESTAMP}, #{gmtModified,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.dt.train.management.center.model.dao.TrainUserDO">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into train_user
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="userId != null">
        user_id,
      </if>
      <if test="dingTalkUserId != null">
        ding_talk_user_id,
      </if>
      <if test="crmUserId != null">
        crm_user_id,
      </if>
      <if test="deptIds != null">
        dept_ids,
      </if>
      <if test="manageDeptIds != null">
        manage_dept_ids,
      </if>
      <if test="userName != null">
        user_name,
      </if>
      <if test="email != null">
        email,
      </if>
      <if test="phone != null">
        phone,
      </if>
      <if test="userPosition != null">
        user_position,
      </if>
        <if test="userRole != null">
        user_role,
      </if>
      <if test="roleId != null">
        role_id,
      </if>
      <if test="workPlace != null">
        work_place,
      </if>
      <if test="userStatus != null">
        user_status,
      </if>
      <if test="userType != null">
        user_type,
      </if>
      <if test="invalid != null">
        invalid,
      </if>
      <if test="createdBy != null">
        created_by,
      </if>
      <if test="updatedBy != null">
        updated_by,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtModified != null">
        gmt_modified,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="userId != null">
        #{userId,jdbcType=BIGINT},
      </if>
      <if test="dingTalkUserId != null">
        #{dingTalkUserId,jdbcType=INTEGER},
      </if>
      <if test="crmUserId != null">
        #{crmUserId,jdbcType=INTEGER},
      </if>
      <if test="deptIds != null">
        #{deptIds,jdbcType=VARCHAR},
      </if>
      <if test="manageDeptIds != null">
        #{manageDeptIds,jdbcType=VARCHAR},
      </if>
      <if test="userName != null">
        #{userName,jdbcType=VARCHAR},
      </if>
      <if test="email != null">
        #{email,jdbcType=VARCHAR},
      </if>
      <if test="phone != null">
        #{phone,jdbcType=VARCHAR},
      </if>
      <if test="userPosition != null">
        #{userPosition,jdbcType=VARCHAR},
      </if>
        <if test="userRole != null">
        #{userRole,jdbcType=VARCHAR},
      </if>
      <if test="roleId != null">
        #{roleId,jdbcType=INTEGER},
      </if>
      <if test="workPlace != null">
        #{workPlace,jdbcType=VARCHAR},
      </if>
      <if test="userStatus != null">
        #{userStatus,jdbcType=TINYINT},
      </if>
      <if test="userType != null">
        #{userType,jdbcType=TINYINT},
      </if>
      <if test="invalid != null">
        #{invalid,jdbcType=TINYINT},
      </if>
      <if test="createdBy != null">
        #{createdBy,jdbcType=BIGINT},
      </if>
      <if test="updatedBy != null">
        #{updatedBy,jdbcType=BIGINT},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModified != null">
        #{gmtModified,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>

  <update id="updateByPrimaryKeySelective" parameterType="com.dt.train.management.center.model.dao.TrainUserDO">
    update train_user
    <set>
      <if test="userId != null">
        user_id = #{userId,jdbcType=BIGINT},
      </if>
      <if test="dingTalkUserId != null">
        ding_talk_user_id = #{dingTalkUserId,jdbcType=VARCHAR},
      </if>
      <if test="crmUserId != null">
        crm_user_id = #{crmUserId,jdbcType=VARCHAR},
      </if>
      <if test="deptIds != null">
        dept_ids = #{deptIds,jdbcType=VARCHAR},
      </if>
      <if test="manageDeptIds != null">
        manage_dept_ids = #{manageDeptIds,jdbcType=VARCHAR},
      </if>
      <if test="userName != null">
        user_name = #{userName,jdbcType=VARCHAR},
      </if>
      <if test="email != null">
        email = #{email,jdbcType=VARCHAR},
      </if>
      <if test="phone != null">
        phone = #{phone,jdbcType=VARCHAR},
      </if>
      <if test="userPosition != null">
        user_position = #{userPosition,jdbcType=VARCHAR},
      </if>
        <if test="userRole != null">
        user_role = #{userRole,jdbcType=VARCHAR},
      </if>
      <if test="roleId != null">
        role_id = #{roleId,jdbcType=INTEGER},
      </if>
      <if test="workPlace != null">
        work_place = #{workPlace,jdbcType=VARCHAR},
      </if>
      <if test="userStatus != null">
        user_status = #{userStatus,jdbcType=TINYINT},
      </if>
      <if test="userType != null">
        user_type = #{userType,jdbcType=TINYINT},
      </if>
      <if test="invalid != null">
        invalid = #{invalid,jdbcType=TINYINT},
      </if>
      <if test="createdBy != null">
        created_by = #{createdBy,jdbcType=BIGINT},
      </if>
      <if test="updatedBy != null">
        updated_by = #{updatedBy,jdbcType=BIGINT},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModified != null">
        gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.dt.train.management.center.model.dao.TrainUserDO">
    update train_user
    set user_id = #{userId,jdbcType=BIGINT},
      ding_talk_user_id = #{dingTalkUserId,jdbcType=VARCHAR},
      crm_user_id = #{crmUserId,jdbcType=VARCHAR},
      dept_ids = #{deptIds,jdbcType=VARCHAR},
      manage_dept_ids = #{manageDeptIds,jdbcType=VARCHAR},
      user_name = #{userName,jdbcType=VARCHAR},
      email = #{email,jdbcType=VARCHAR},
      phone = #{phone,jdbcType=VARCHAR},
      user_position = #{userPosition,jdbcType=VARCHAR},
      user_role = #{userRole,jdbcType=VARCHAR},
      role_id = #{roleId,jdbcType=INTEGER},
      work_place = #{workPlace,jdbcType=VARCHAR},
      user_status = #{userStatus,jdbcType=TINYINT},
      user_type = #{userType,jdbcType=TINYINT},
      invalid = #{invalid,jdbcType=TINYINT},
      created_by = #{createdBy,jdbcType=BIGINT},
      updated_by = #{updatedBy,jdbcType=BIGINT},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_modified = #{gmtModified,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>

  <!-- selectByCrmUserId --> 

  <select id="selectByCrmUserId" resultType="com.dt.train.management.center.model.dao.TrainUserDO" resultMap="BaseResultMap">
    SELECT
    <include refid="Base_Column_List" />
    FROM train_user
    WHERE crm_user_id = #{crmUserId,jdbcType=VARCHAR}
    AND invalid = 0
  </select>

  <!-- countUsersByRoleId --> 

  <select id="countUsersByRoleId" resultType="java.lang.Integer" parameterType="java.lang.Integer">
    SELECT COUNT(*) FROM train_user
    WHERE role_id = #{roleId,jdbcType=INTEGER}
    AND invalid = 0 and user_status = 0 and dept_ids != 20000;
  </select>

    <select id="selectDingTalkUserIdsByUserIds" resultType="com.dt.train.management.center.model.dao.TrainUserDO" resultMap="BaseResultMap">
      SELECT <include refid="Base_Column_List" />
      FROM train_user
      WHERE user_id IN
      <foreach item="userId" collection="userIdList" open="(" separator="," close=")">
        #{userId,jdbcType=BIGINT}
      </foreach>
    </select>

  <select id="selectMinUserId" resultType="java.lang.Long">
    SELECT MIN(user_id) FROM train_user WHERE user_id IS NOT NULL
  </select>

  <select id="countPartTimeUsersByDeptId" resultType="java.lang.Integer">
    select count(1) from train_user
    where user_type = 1
    and dept_ids = #{deptId,jdbcType=VARCHAR}
  </select>

</mapper>
