<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dt.train.management.center.mapper.DictMappingDOMapper">
  <resultMap id="BaseResultMap" type="com.dt.train.management.center.model.dao.DictMappingDO">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="dict_code" jdbcType="VARCHAR" property="dictCode" />
    <result column="third_dict_code" jdbcType="VARCHAR" property="thirdDictCode" />
    <result column="third_system" jdbcType="VARCHAR" property="thirdSystem" />
    <result column="dict_type" jdbcType="VARCHAR" property="dictType" />
    <result column="dict_value" jdbcType="VARCHAR" property="dictValue" />
    <result column="dict_desc" jdbcType="VARCHAR" property="dictDesc" />
    <result column="dict_order" jdbcType="INTEGER" property="dictOrder" />
    <result column="invalid" jdbcType="TINYINT" property="invalid" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
  </resultMap>
  <sql id="Base_Column_List">
    id, dict_code, third_dict_code, third_system, dict_type, dict_value, dict_desc, dict_order, 
    invalid, gmt_create, gmt_modified
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from dict_mapping
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from dict_mapping
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByDictTypeAndDictValueIn">
    delete from dict_mapping
    where dict_type = #{dictType,jdbcType=VARCHAR}
    and dict_value in
    <foreach collection="nameSet" item="item" open="(" separator="," close=")">
      #{item,jdbcType=VARCHAR}
    </foreach>
  </delete>
  <insert id="insert" parameterType="com.dt.train.management.center.model.dao.DictMappingDO">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into dict_mapping (dict_code, third_dict_code, third_system, 
      dict_type, dict_value, dict_desc, 
      dict_order, invalid, gmt_create, 
      gmt_modified)
    values (#{dictCode,jdbcType=VARCHAR}, #{thirdDictCode,jdbcType=VARCHAR}, #{thirdSystem,jdbcType=VARCHAR}, 
      #{dictType,jdbcType=VARCHAR}, #{dictValue,jdbcType=VARCHAR}, #{dictDesc,jdbcType=VARCHAR}, 
      #{dictOrder,jdbcType=INTEGER}, #{invalid,jdbcType=TINYINT}, #{gmtCreate,jdbcType=TIMESTAMP}, 
      #{gmtModified,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.dt.train.management.center.model.dao.DictMappingDO">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into dict_mapping
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="dictCode != null">
        dict_code,
      </if>
      <if test="thirdDictCode != null">
        third_dict_code,
      </if>
      <if test="thirdSystem != null">
        third_system,
      </if>
      <if test="dictType != null">
        dict_type,
      </if>
      <if test="dictValue != null">
        dict_value,
      </if>
      <if test="dictDesc != null">
        dict_desc,
      </if>
      <if test="dictOrder != null">
        dict_order,
      </if>
      <if test="invalid != null">
        invalid,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtModified != null">
        gmt_modified,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="dictCode != null">
        #{dictCode,jdbcType=VARCHAR},
      </if>
      <if test="thirdDictCode != null">
        #{thirdDictCode,jdbcType=VARCHAR},
      </if>
      <if test="thirdSystem != null">
        #{thirdSystem,jdbcType=VARCHAR},
      </if>
      <if test="dictType != null">
        #{dictType,jdbcType=VARCHAR},
      </if>
      <if test="dictValue != null">
        #{dictValue,jdbcType=VARCHAR},
      </if>
      <if test="dictDesc != null">
        #{dictDesc,jdbcType=VARCHAR},
      </if>
      <if test="dictOrder != null">
        #{dictOrder,jdbcType=INTEGER},
      </if>
      <if test="invalid != null">
        #{invalid,jdbcType=TINYINT},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModified != null">
        #{gmtModified,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.dt.train.management.center.model.dao.DictMappingDO">
    update dict_mapping
    <set>
      <if test="dictCode != null">
        dict_code = #{dictCode,jdbcType=VARCHAR},
      </if>
      <if test="thirdDictCode != null">
        third_dict_code = #{thirdDictCode,jdbcType=VARCHAR},
      </if>
      <if test="thirdSystem != null">
        third_system = #{thirdSystem,jdbcType=VARCHAR},
      </if>
      <if test="dictType != null">
        dict_type = #{dictType,jdbcType=VARCHAR},
      </if>
      <if test="dictValue != null">
        dict_value = #{dictValue,jdbcType=VARCHAR},
      </if>
      <if test="dictDesc != null">
        dict_desc = #{dictDesc,jdbcType=VARCHAR},
      </if>
      <if test="dictOrder != null">
        dict_order = #{dictOrder,jdbcType=INTEGER},
      </if>
      <if test="invalid != null">
        invalid = #{invalid,jdbcType=TINYINT},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModified != null">
        gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.dt.train.management.center.model.dao.DictMappingDO">
    update dict_mapping
    set dict_code = #{dictCode,jdbcType=VARCHAR},
      third_dict_code = #{thirdDictCode,jdbcType=VARCHAR},
      third_system = #{thirdSystem,jdbcType=VARCHAR},
      dict_type = #{dictType,jdbcType=VARCHAR},
      dict_value = #{dictValue,jdbcType=VARCHAR},
      dict_desc = #{dictDesc,jdbcType=VARCHAR},
      dict_order = #{dictOrder,jdbcType=INTEGER},
      invalid = #{invalid,jdbcType=TINYINT},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_modified = #{gmtModified,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>