<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dt.train.management.center.mapper.TrainCourseAreaRelationDOMapper">
  <resultMap id="BaseResultMap" type="com.dt.train.management.center.model.dao.TrainCourseAreaRelationDO">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="project_id" jdbcType="INTEGER" property="projectId" />
    <result column="course_id" jdbcType="INTEGER" property="courseId" />
    <result column="relation_type" jdbcType="TINYINT" property="relationType" />
    <result column="province_id" jdbcType="VARCHAR" property="provinceId" />
    <result column="city_id" jdbcType="VARCHAR" property="cityId" />
    <result column="area_id" jdbcType="VARCHAR" property="areaId" />
    <result column="province" jdbcType="VARCHAR" property="province" />
    <result column="city" jdbcType="VARCHAR" property="city" />
    <result column="area" jdbcType="VARCHAR" property="area" />
    <result column="invalid" jdbcType="TINYINT" property="invalid" />
    <result column="created_by" jdbcType="BIGINT" property="createdBy" />
    <result column="updated_by" jdbcType="BIGINT" property="updatedBy" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
  </resultMap>
  <sql id="Base_Column_List">
    id, project_id, course_id, relation_type, province_id, city_id, area_id, province, city, area, invalid, 
    created_by, updated_by, gmt_create, gmt_modified
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from train_course_area_relation
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from train_course_area_relation
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.dt.train.management.center.model.dao.TrainCourseAreaRelationDO">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into train_course_area_relation (project_id, course_id, relation_type, province_id, 
      city_id, area_id, province, 
      city, area, invalid, 
      created_by, updated_by, gmt_create, 
      gmt_modified)
    values (#{projectId,jdbcType=INTEGER}, #{courseId,jdbcType=INTEGER}, #{relationType,jdbcType=TINYINT}, #{provinceId,jdbcType=VARCHAR}, 
      #{cityId,jdbcType=VARCHAR}, #{areaId,jdbcType=VARCHAR}, #{province,jdbcType=VARCHAR}, 
      #{city,jdbcType=VARCHAR}, #{area,jdbcType=VARCHAR}, #{invalid,jdbcType=TINYINT}, 
      #{createdBy,jdbcType=BIGINT}, #{updatedBy,jdbcType=BIGINT}, #{gmtCreate,jdbcType=TIMESTAMP}, 
      #{gmtModified,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.dt.train.management.center.model.dao.TrainCourseAreaRelationDO">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into train_course_area_relation
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="projectId != null">
        project_id,
      </if>
      <if test="courseId != null">
        course_id,
      </if>
      <if test="relationType != null">
        relation_type,
      </if>
      <if test="provinceId != null">
        province_id,
      </if>
      <if test="cityId != null">
        city_id,
      </if>
      <if test="areaId != null">
        area_id,
      </if>
      <if test="province != null">
        province,
      </if>
      <if test="city != null">
        city,
      </if>
      <if test="area != null">
        area,
      </if>
      <if test="invalid != null">
        invalid,
      </if>
      <if test="createdBy != null">
        created_by,
      </if>
      <if test="updatedBy != null">
        updated_by,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtModified != null">
        gmt_modified,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="projectId != null">
        #{projectId,jdbcType=INTEGER},
      </if>
      <if test="courseId != null">
        #{courseId,jdbcType=INTEGER},
      </if>
      <if test="relationType != null">
        #{relationType,jdbcType=TINYINT},
      </if>
      <if test="provinceId != null">
        #{provinceId,jdbcType=VARCHAR},
      </if>
      <if test="cityId != null">
        #{cityId,jdbcType=VARCHAR},
      </if>
      <if test="areaId != null">
        #{areaId,jdbcType=VARCHAR},
      </if>
      <if test="province != null">
        #{province,jdbcType=VARCHAR},
      </if>
      <if test="city != null">
        #{city,jdbcType=VARCHAR},
      </if>
      <if test="area != null">
        #{area,jdbcType=VARCHAR},
      </if>
      <if test="invalid != null">
        #{invalid,jdbcType=TINYINT},
      </if>
      <if test="createdBy != null">
        #{createdBy,jdbcType=BIGINT},
      </if>
      <if test="updatedBy != null">
        #{updatedBy,jdbcType=BIGINT},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModified != null">
        #{gmtModified,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.dt.train.management.center.model.dao.TrainCourseAreaRelationDO">
    update train_course_area_relation
    <set>
      <if test="projectId != null">
        project_id = #{projectId,jdbcType=INTEGER},
      </if>
      <if test="courseId != null">
        course_id = #{courseId,jdbcType=INTEGER},
      </if>
      <if test="relationType != null">
        relation_type = #{relationType,jdbcType=TINYINT},
      </if>
      <if test="provinceId != null">
        province_id = #{provinceId,jdbcType=VARCHAR},
      </if>
      <if test="cityId != null">
        city_id = #{cityId,jdbcType=VARCHAR},
      </if>
      <if test="areaId != null">
        area_id = #{areaId,jdbcType=VARCHAR},
      </if>
      <if test="province != null">
        province = #{province,jdbcType=VARCHAR},
      </if>
      <if test="city != null">
        city = #{city,jdbcType=VARCHAR},
      </if>
      <if test="area != null">
        area = #{area,jdbcType=VARCHAR},
      </if>
      <if test="invalid != null">
        invalid = #{invalid,jdbcType=TINYINT},
      </if>
      <if test="createdBy != null">
        created_by = #{createdBy,jdbcType=BIGINT},
      </if>
      <if test="updatedBy != null">
        updated_by = #{updatedBy,jdbcType=BIGINT},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModified != null">
        gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.dt.train.management.center.model.dao.TrainCourseAreaRelationDO">
    update train_course_area_relation
    set project_id = #{projectId,jdbcType=INTEGER},
      course_id = #{courseId,jdbcType=INTEGER},
      relation_type = #{relationType,jdbcType=TINYINT},
      province_id = #{provinceId,jdbcType=VARCHAR},
      city_id = #{cityId,jdbcType=VARCHAR},
      area_id = #{areaId,jdbcType=VARCHAR},
      province = #{province,jdbcType=VARCHAR},
      city = #{city,jdbcType=VARCHAR},
      area = #{area,jdbcType=VARCHAR},
      invalid = #{invalid,jdbcType=TINYINT},
      created_by = #{createdBy,jdbcType=BIGINT},
      updated_by = #{updatedBy,jdbcType=BIGINT},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_modified = #{gmtModified,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>

  <!-- deleteByCourseId --> 

  <update id="deleteByCourseId">
    update train_course_area_relation
    set invalid = 1
    where course_id = #{courseId,jdbcType=INTEGER}
  </update>

  <sql id="Where_Clause">
    <where>
      <if test="projectId != null">
        project_id = #{projectId,jdbcType=INTEGER}
      </if>
      <if test="courseId != null">
        course_id = #{courseId,jdbcType=INTEGER}
      </if>
    </where>
  </sql>
  <!-- selectByCourseId --> 

  <select id="selectByQuery" parameterType="com.dt.train.management.center.model.dto.project.TrainCourseAreaRelationQueryDTO" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from train_course_area_relation
    <include refid="Where_Clause" />
  </select>

  <!-- selectByProjectIds --> 

  <select id="selectByProjectIds" parameterType="java.util.List" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from train_course_area_relation
    where project_id in
    <foreach item="projectId" collection="projectIds" separator="," open="(" close=")">
      #{projectId,jdbcType=INTEGER}
    </foreach>
    and invalid = 0
  </select>

  <!-- selectByCourseIds --> 

  <select id="selectByCourseIds" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from train_course_area_relation
    where course_id in
    <foreach item="courseId" collection="courseIds" separator="," open="(" close=")">
      #{courseId,jdbcType=INTEGER}
    </foreach>
    and invalid = 0
  </select>
</mapper>