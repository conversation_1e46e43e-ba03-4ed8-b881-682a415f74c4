<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dt.train.management.center.mapper.TrainCheckTaskDOMapper">
  <resultMap id="BaseResultMap" type="com.dt.train.management.center.model.dao.TrainCheckTaskDO">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="project_id" jdbcType="INTEGER" property="projectId" />
    <result column="course_id" jdbcType="INTEGER" property="courseId" />
    <result column="task_id" jdbcType="INTEGER" property="taskId" />
    <result column="user_id" jdbcType="BIGINT" property="userId" />
    <result column="user_name" jdbcType="VARCHAR" property="userName" />
    <result column="check_dimension" jdbcType="TINYINT" property="checkDimension" />
    <result column="check_in_status" jdbcType="TINYINT" property="checkInStatus" />
    <result column="check_out_status" jdbcType="TINYINT" property="checkOutStatus" />
    <result column="check_on_site_status" jdbcType="TINYINT" property="checkOnSiteStatus" />
    <result column="check_status" jdbcType="TINYINT" property="checkStatus" />
    <result column="check_date" jdbcType="TIMESTAMP" property="checkDate" />
    <result column="invalid" jdbcType="TINYINT" property="invalid" />
    <result column="created_by" jdbcType="BIGINT" property="createdBy" />
    <result column="updated_by" jdbcType="BIGINT" property="updatedBy" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
  </resultMap>
  <sql id="Base_Column_List">
    id, project_id, course_id, task_id, user_id, user_name, check_dimension, check_in_status, check_out_status, 
    check_on_site_status, check_status, check_date, invalid, created_by, updated_by, 
    gmt_create, gmt_modified
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from train_check_task
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from train_check_task
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.dt.train.management.center.model.dao.TrainCheckTaskDO">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into train_check_task (project_id, course_id, task_id, 
      user_id, user_name, check_dimension, check_in_status, 
      check_out_status, check_on_site_status, check_status, 
      check_date, invalid, created_by, 
      updated_by, gmt_create, gmt_modified
      )
    values (#{projectId,jdbcType=INTEGER}, #{courseId,jdbcType=INTEGER}, #{taskId,jdbcType=INTEGER}, 
      #{userId,jdbcType=BIGINT}, #{userName,jdbcType=VARCHAR}, #{checkDimension,jdbcType=TINYINT}, #{checkInStatus,jdbcType=TINYINT}, 
      #{checkOutStatus,jdbcType=TINYINT}, #{checkOnSiteStatus,jdbcType=TINYINT}, #{checkStatus,jdbcType=TINYINT}, 
      #{checkDate,jdbcType=TIMESTAMP}, #{invalid,jdbcType=TINYINT}, #{createdBy,jdbcType=BIGINT}, 
      #{updatedBy,jdbcType=BIGINT}, #{gmtCreate,jdbcType=TIMESTAMP}, #{gmtModified,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.dt.train.management.center.model.dao.TrainCheckTaskDO">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into train_check_task
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="projectId != null">
        project_id,
      </if>
      <if test="courseId != null">
        course_id,
      </if>
      <if test="taskId != null">
        task_id,
      </if>
      <if test="userId != null">
        user_id,
      </if>
      <if test="userName != null">
        user_name,
      </if>
      <if test="checkDimension != null">
        check_dimension,
      </if>
      <if test="checkInStatus != null">
        check_in_status,
      </if>
      <if test="checkOutStatus != null">
        check_out_status,
      </if>
      <if test="checkOnSiteStatus != null">
        check_on_site_status,
      </if>
      <if test="checkStatus != null">
        check_status,
      </if>
      <if test="checkDate != null">
        check_date,
      </if>
      <if test="invalid != null">
        invalid,
      </if>
      <if test="createdBy != null">
        created_by,
      </if>
      <if test="updatedBy != null">
        updated_by,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtModified != null">
        gmt_modified,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="projectId != null">
        #{projectId,jdbcType=INTEGER},
      </if>
      <if test="courseId != null">
        #{courseId,jdbcType=INTEGER},
      </if>
      <if test="taskId != null">
        #{taskId,jdbcType=INTEGER},
      </if>
      <if test="userId != null">
        #{userId,jdbcType=BIGINT},
      </if>
      <if test="userName != null">
        #{userName,jdbcType=VARCHAR},
      </if>
      <if test="checkDimension != null">
        #{checkDimension,jdbcType=TINYINT},
      </if>
      <if test="checkInStatus != null">
        #{checkInStatus,jdbcType=TINYINT},
      </if>
      <if test="checkOutStatus != null">
        #{checkOutStatus,jdbcType=TINYINT},
      </if>
      <if test="checkOnSiteStatus != null">
        #{checkOnSiteStatus,jdbcType=TINYINT},
      </if>
      <if test="checkStatus != null">
        #{checkStatus,jdbcType=TINYINT},
      </if>
      <if test="checkDate != null">
        #{checkDate,jdbcType=TIMESTAMP},
      </if>
      <if test="invalid != null">
        #{invalid,jdbcType=TINYINT},
      </if>
      <if test="createdBy != null">
        #{createdBy,jdbcType=BIGINT},
      </if>
      <if test="updatedBy != null">
        #{updatedBy,jdbcType=BIGINT},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModified != null">
        #{gmtModified,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.dt.train.management.center.model.dao.TrainCheckTaskDO">
    update train_check_task
    <set>
      <if test="projectId != null">
        project_id = #{projectId,jdbcType=INTEGER},
      </if>
      <if test="courseId != null">
        course_id = #{courseId,jdbcType=INTEGER},
      </if>
      <if test="taskId != null">
        task_id = #{taskId,jdbcType=INTEGER},
      </if>
      <if test="userId != null">
        user_id = #{userId,jdbcType=BIGINT},
      </if>
      <if test="userName != null">
        user_name = #{userName,jdbcType=VARCHAR},
      </if>
      <if test="checkDimension != null">
        check_dimension = #{checkDimension,jdbcType=TINYINT},
      </if>
      <if test="checkInStatus != null">
        check_in_status = #{checkInStatus,jdbcType=TINYINT},
      </if>
      <if test="checkOutStatus != null">
        check_out_status = #{checkOutStatus,jdbcType=TINYINT},
      </if>
      <if test="checkOnSiteStatus != null">
        check_on_site_status = #{checkOnSiteStatus,jdbcType=TINYINT},
      </if>
      <if test="checkStatus != null">
        check_status = #{checkStatus,jdbcType=TINYINT},
      </if>
      <if test="checkDate != null">
        check_date = #{checkDate,jdbcType=TIMESTAMP},
      </if>
      <if test="invalid != null">
        invalid = #{invalid,jdbcType=TINYINT},
      </if>
      <if test="createdBy != null">
        created_by = #{createdBy,jdbcType=BIGINT},
      </if>
      <if test="updatedBy != null">
        updated_by = #{updatedBy,jdbcType=BIGINT},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModified != null">
        gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.dt.train.management.center.model.dao.TrainCheckTaskDO">
    update train_check_task
    set project_id = #{projectId,jdbcType=INTEGER},
      course_id = #{courseId,jdbcType=INTEGER},
      task_id = #{taskId,jdbcType=INTEGER},
      user_id = #{userId,jdbcType=BIGINT},
      user_name = #{userName,jdbcType=VARCHAR},
      check_dimension = #{checkDimension,jdbcType=TINYINT},
      check_in_status = #{checkInStatus,jdbcType=TINYINT},
      check_out_status = #{checkOutStatus,jdbcType=TINYINT},
      check_on_site_status = #{checkOnSiteStatus,jdbcType=TINYINT},
      check_status = #{checkStatus,jdbcType=TINYINT},
      check_date = #{checkDate,jdbcType=TIMESTAMP},
      invalid = #{invalid,jdbcType=TINYINT},
      created_by = #{createdBy,jdbcType=BIGINT},
      updated_by = #{updatedBy,jdbcType=BIGINT},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_modified = #{gmtModified,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>