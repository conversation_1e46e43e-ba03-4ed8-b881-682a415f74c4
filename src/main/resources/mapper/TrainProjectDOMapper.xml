<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dt.train.management.center.mapper.TrainProjectDOMapper">
  <resultMap id="BaseResultMap" type="com.dt.train.management.center.model.dao.TrainProjectDO">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="crm_project_id" jdbcType="VARCHAR" property="crmProjectId" />
    <result column="project_name" jdbcType="VARCHAR" property="projectName" />
    <result column="project_code" jdbcType="VARCHAR" property="projectCode" />
    <result column="project_area" jdbcType="VARCHAR" property="projectArea" />
    <result column="project_area_id" jdbcType="INTEGER" property="projectAreaId" />
    <result column="province_id" jdbcType="VARCHAR" property="provinceId" />
    <result column="city_id" jdbcType="VARCHAR" property="cityId" />
    <result column="area_id" jdbcType="VARCHAR" property="areaId" />
    <result column="province" jdbcType="VARCHAR" property="province" />
    <result column="city" jdbcType="VARCHAR" property="city" />
    <result column="area" jdbcType="VARCHAR" property="area" />
    <result column="project_type" jdbcType="TINYINT" property="projectType" />
    <result column="project_level" jdbcType="TINYINT" property="projectLevel" />
    <result column="start_date" jdbcType="TIMESTAMP" property="startDate" />
    <result column="end_date" jdbcType="TIMESTAMP" property="endDate" />
    <result column="project_status" jdbcType="TINYINT" property="projectStatus" />
    <result column="schedule_status" jdbcType="TINYINT" property="scheduleStatus" />
    <result column="is_remote" jdbcType="TINYINT" property="isRemote" />
    <result column="action_type" jdbcType="TINYINT" property="actionType" />
    <result column="execute_contact_name" jdbcType="VARCHAR" property="executeContactName" />
    <result column="execute_contact_phone" jdbcType="VARCHAR" property="executeContactPhone" />
    <result column="execute_contact_job" jdbcType="VARCHAR" property="executeContactJob" />
    <result column="total_hours" jdbcType="VARCHAR" property="totalHours" />
    <result column="total_user_count" jdbcType="INTEGER" property="totalUserCount" />
    <result column="project_manager_id" jdbcType="VARCHAR" property="projectManagerId" />
    <result column="project_manager_name" jdbcType="VARCHAR" property="projectManagerName" />
    <result column="project_editor_id" jdbcType="VARCHAR" property="projectEditorId" />
    <result column="project_editor_name" jdbcType="VARCHAR" property="projectEditorName" />
    <result column="service_object" jdbcType="VARCHAR" property="serviceObject" />
    <result column="service_content" jdbcType="VARCHAR" property="serviceContent" />
    <result column="sales_user_id" jdbcType="BIGINT" property="salesUserId" />
    <result column="sales_user_name" jdbcType="VARCHAR" property="salesUserName" />
    <result column="province_sales_manager_id" jdbcType="BIGINT" property="provinceSalesManagerId" />
    <result column="province_sales_manager_name" jdbcType="VARCHAR" property="provinceSalesManagerName" />
    <result column="region_sales_director_id" jdbcType="BIGINT" property="regionSalesDirectorId" />
    <result column="region_sales_director_name" jdbcType="VARCHAR" property="regionSalesDirectorName" />
    <result column="business_type" jdbcType="VARCHAR" property="businessType" />
    <result column="project_mode" jdbcType="VARCHAR" property="projectMode" />
    <result column="project_sub_mode" jdbcType="VARCHAR" property="projectSubMode" />
    <result column="customer_name" jdbcType="VARCHAR" property="customerName" />
    <result column="sync_type" jdbcType="TINYINT" property="syncType" />
    <result column="teach_days" jdbcType="REAL" property="teachDays" />
    <result column="teach_course_count" jdbcType="INTEGER" property="teachCourseCount" />
    <result column="teach_remark" jdbcType="VARCHAR" property="teachRemark" />
    <result column="project_helper_id" jdbcType="VARCHAR" property="projectHelperId" />
    <result column="project_helper_name" jdbcType="VARCHAR" property="projectHelperName" />
    <result column="live_editor_id" jdbcType="VARCHAR" property="liveEditorId" />
    <result column="live_editor_name" jdbcType="VARCHAR" property="liveEditorName" />
    <result column="sync_time" jdbcType="TIMESTAMP" property="syncTime" />
    <result column="invalid" jdbcType="TINYINT" property="invalid" />
    <result column="created_by" jdbcType="BIGINT" property="createdBy" />
    <result column="updated_by" jdbcType="BIGINT" property="updatedBy" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
  </resultMap>
  <sql id="Base_Column_List">
    id, crm_project_id, project_name, project_code, project_area, project_area_id, province_id, 
    city_id, area_id, province, city, area, project_type, project_level, start_date, 
    end_date, project_status, schedule_status, is_remote, action_type, execute_contact_name, 
    execute_contact_phone, execute_contact_job, total_hours, total_user_count, project_manager_id, 
    project_manager_name, project_editor_id, project_editor_name, service_object, service_content, 
    sales_user_id, sales_user_name, province_sales_manager_id, province_sales_manager_name, 
    region_sales_director_id, region_sales_director_name, business_type, project_mode, 
    project_sub_mode, customer_name, sync_type, teach_days, teach_course_count, teach_remark, 
    project_helper_id, project_helper_name, live_editor_id, live_editor_name, sync_time, 
    invalid, created_by, updated_by, gmt_create, gmt_modified
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from train_project
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from train_project
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.dt.train.management.center.model.dao.TrainProjectDO">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into train_project (crm_project_id, project_name, project_code, 
      project_area, project_area_id, province_id, 
      city_id, area_id, province, 
      city, area, project_type, 
      project_level, start_date, end_date, 
      project_status, schedule_status, is_remote, 
      action_type, execute_contact_name, execute_contact_phone, 
      execute_contact_job, total_hours, total_user_count, 
      project_manager_id, project_manager_name, project_editor_id, 
      project_editor_name, service_object, service_content, 
      sales_user_id, sales_user_name, province_sales_manager_id, 
      province_sales_manager_name, region_sales_director_id, 
      region_sales_director_name, business_type, 
      project_mode, project_sub_mode, customer_name, 
      sync_type, teach_days, teach_course_count, 
      teach_remark, project_helper_id, project_helper_name, 
      live_editor_id, live_editor_name, sync_time, 
      invalid, created_by, updated_by, 
      gmt_create, gmt_modified)
    values (#{crmProjectId,jdbcType=VARCHAR}, #{projectName,jdbcType=VARCHAR}, #{projectCode,jdbcType=VARCHAR}, 
      #{projectArea,jdbcType=VARCHAR}, #{projectAreaId,jdbcType=INTEGER}, #{provinceId,jdbcType=VARCHAR}, 
      #{cityId,jdbcType=VARCHAR}, #{areaId,jdbcType=VARCHAR}, #{province,jdbcType=VARCHAR}, 
      #{city,jdbcType=VARCHAR}, #{area,jdbcType=VARCHAR}, #{projectType,jdbcType=TINYINT}, 
      #{projectLevel,jdbcType=TINYINT}, #{startDate,jdbcType=TIMESTAMP}, #{endDate,jdbcType=TIMESTAMP}, 
      #{projectStatus,jdbcType=TINYINT}, #{scheduleStatus,jdbcType=TINYINT}, #{isRemote,jdbcType=TINYINT}, 
      #{actionType,jdbcType=TINYINT}, #{executeContactName,jdbcType=VARCHAR}, #{executeContactPhone,jdbcType=VARCHAR}, 
      #{executeContactJob,jdbcType=VARCHAR}, #{totalHours,jdbcType=VARCHAR}, #{totalUserCount,jdbcType=INTEGER}, 
      #{projectManagerId,jdbcType=VARCHAR}, #{projectManagerName,jdbcType=VARCHAR}, #{projectEditorId,jdbcType=VARCHAR}, 
      #{projectEditorName,jdbcType=VARCHAR}, #{serviceObject,jdbcType=VARCHAR}, #{serviceContent,jdbcType=VARCHAR}, 
      #{salesUserId,jdbcType=BIGINT}, #{salesUserName,jdbcType=VARCHAR}, #{provinceSalesManagerId,jdbcType=BIGINT}, 
      #{provinceSalesManagerName,jdbcType=VARCHAR}, #{regionSalesDirectorId,jdbcType=BIGINT}, 
      #{regionSalesDirectorName,jdbcType=VARCHAR}, #{businessType,jdbcType=VARCHAR}, 
      #{projectMode,jdbcType=VARCHAR}, #{projectSubMode,jdbcType=VARCHAR}, #{customerName,jdbcType=VARCHAR}, 
      #{syncType,jdbcType=TINYINT}, #{teachDays,jdbcType=REAL}, #{teachCourseCount,jdbcType=INTEGER}, 
      #{teachRemark,jdbcType=VARCHAR}, #{projectHelperId,jdbcType=VARCHAR}, #{projectHelperName,jdbcType=VARCHAR}, 
      #{liveEditorId,jdbcType=VARCHAR}, #{liveEditorName,jdbcType=VARCHAR}, #{syncTime,jdbcType=TIMESTAMP}, 
      #{invalid,jdbcType=TINYINT}, #{createdBy,jdbcType=BIGINT}, #{updatedBy,jdbcType=BIGINT}, 
      #{gmtCreate,jdbcType=TIMESTAMP}, #{gmtModified,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.dt.train.management.center.model.dao.TrainProjectDO">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into train_project
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="crmProjectId != null">
        crm_project_id,
      </if>
      <if test="projectName != null">
        project_name,
      </if>
      <if test="projectCode != null">
        project_code,
      </if>
      <if test="projectArea != null">
        project_area,
      </if>
      <if test="projectAreaId != null">
        project_area_id,
      </if>
      <if test="provinceId != null">
        province_id,
      </if>
      <if test="cityId != null">
        city_id,
      </if>
      <if test="areaId != null">
        area_id,
      </if>
      <if test="province != null">
        province,
      </if>
      <if test="city != null">
        city,
      </if>
      <if test="area != null">
        area,
      </if>
      <if test="projectType != null">
        project_type,
      </if>
      <if test="projectLevel != null">
        project_level,
      </if>
      <if test="startDate != null">
        start_date,
      </if>
      <if test="endDate != null">
        end_date,
      </if>
      <if test="projectStatus != null">
        project_status,
      </if>
      <if test="scheduleStatus != null">
        schedule_status,
      </if>
      <if test="isRemote != null">
        is_remote,
      </if>
      <if test="actionType != null">
        action_type,
      </if>
      <if test="executeContactName != null">
        execute_contact_name,
      </if>
      <if test="executeContactPhone != null">
        execute_contact_phone,
      </if>
      <if test="executeContactJob != null">
        execute_contact_job,
      </if>
      <if test="totalHours != null">
        total_hours,
      </if>
      <if test="totalUserCount != null">
        total_user_count,
      </if>
      <if test="projectManagerId != null">
        project_manager_id,
      </if>
      <if test="projectManagerName != null">
        project_manager_name,
      </if>
      <if test="projectEditorId != null">
        project_editor_id,
      </if>
      <if test="projectEditorName != null">
        project_editor_name,
      </if>
      <if test="serviceObject != null">
        service_object,
      </if>
      <if test="serviceContent != null">
        service_content,
      </if>
      <if test="salesUserId != null">
        sales_user_id,
      </if>
      <if test="salesUserName != null">
        sales_user_name,
      </if>
      <if test="provinceSalesManagerId != null">
        province_sales_manager_id,
      </if>
      <if test="provinceSalesManagerName != null">
        province_sales_manager_name,
      </if>
      <if test="regionSalesDirectorId != null">
        region_sales_director_id,
      </if>
      <if test="regionSalesDirectorName != null">
        region_sales_director_name,
      </if>
      <if test="businessType != null">
        business_type,
      </if>
      <if test="projectMode != null">
        project_mode,
      </if>
      <if test="projectSubMode != null">
        project_sub_mode,
      </if>
      <if test="customerName != null">
        customer_name,
      </if>
      <if test="syncType != null">
        sync_type,
      </if>
      <if test="teachDays != null">
        teach_days,
      </if>
      <if test="teachCourseCount != null">
        teach_course_count,
      </if>
      <if test="teachRemark != null">
        teach_remark,
      </if>
      <if test="projectHelperId != null">
        project_helper_id,
      </if>
      <if test="projectHelperName != null">
        project_helper_name,
      </if>
      <if test="liveEditorId != null">
        live_editor_id,
      </if>
      <if test="liveEditorName != null">
        live_editor_name,
      </if>
      <if test="syncTime != null">
        sync_time,
      </if>
      <if test="invalid != null">
        invalid,
      </if>
      <if test="createdBy != null">
        created_by,
      </if>
      <if test="updatedBy != null">
        updated_by,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtModified != null">
        gmt_modified,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="crmProjectId != null">
        #{crmProjectId,jdbcType=VARCHAR},
      </if>
      <if test="projectName != null">
        #{projectName,jdbcType=VARCHAR},
      </if>
      <if test="projectCode != null">
        #{projectCode,jdbcType=VARCHAR},
      </if>
      <if test="projectArea != null">
        #{projectArea,jdbcType=VARCHAR},
      </if>
      <if test="projectAreaId != null">
        #{projectAreaId,jdbcType=INTEGER},
      </if>
      <if test="provinceId != null">
        #{provinceId,jdbcType=VARCHAR},
      </if>
      <if test="cityId != null">
        #{cityId,jdbcType=VARCHAR},
      </if>
      <if test="areaId != null">
        #{areaId,jdbcType=VARCHAR},
      </if>
      <if test="province != null">
        #{province,jdbcType=VARCHAR},
      </if>
      <if test="city != null">
        #{city,jdbcType=VARCHAR},
      </if>
      <if test="area != null">
        #{area,jdbcType=VARCHAR},
      </if>
      <if test="projectType != null">
        #{projectType,jdbcType=TINYINT},
      </if>
      <if test="projectLevel != null">
        #{projectLevel,jdbcType=TINYINT},
      </if>
      <if test="startDate != null">
        #{startDate,jdbcType=TIMESTAMP},
      </if>
      <if test="endDate != null">
        #{endDate,jdbcType=TIMESTAMP},
      </if>
      <if test="projectStatus != null">
        #{projectStatus,jdbcType=TINYINT},
      </if>
      <if test="scheduleStatus != null">
        #{scheduleStatus,jdbcType=TINYINT},
      </if>
      <if test="isRemote != null">
        #{isRemote,jdbcType=TINYINT},
      </if>
      <if test="actionType != null">
        #{actionType,jdbcType=TINYINT},
      </if>
      <if test="executeContactName != null">
        #{executeContactName,jdbcType=VARCHAR},
      </if>
      <if test="executeContactPhone != null">
        #{executeContactPhone,jdbcType=VARCHAR},
      </if>
      <if test="executeContactJob != null">
        #{executeContactJob,jdbcType=VARCHAR},
      </if>
      <if test="totalHours != null">
        #{totalHours,jdbcType=VARCHAR},
      </if>
      <if test="totalUserCount != null">
        #{totalUserCount,jdbcType=INTEGER},
      </if>
      <if test="projectManagerId != null">
        #{projectManagerId,jdbcType=VARCHAR},
      </if>
      <if test="projectManagerName != null">
        #{projectManagerName,jdbcType=VARCHAR},
      </if>
      <if test="projectEditorId != null">
        #{projectEditorId,jdbcType=VARCHAR},
      </if>
      <if test="projectEditorName != null">
        #{projectEditorName,jdbcType=VARCHAR},
      </if>
      <if test="serviceObject != null">
        #{serviceObject,jdbcType=VARCHAR},
      </if>
      <if test="serviceContent != null">
        #{serviceContent,jdbcType=VARCHAR},
      </if>
      <if test="salesUserId != null">
        #{salesUserId,jdbcType=BIGINT},
      </if>
      <if test="salesUserName != null">
        #{salesUserName,jdbcType=VARCHAR},
      </if>
      <if test="provinceSalesManagerId != null">
        #{provinceSalesManagerId,jdbcType=BIGINT},
      </if>
      <if test="provinceSalesManagerName != null">
        #{provinceSalesManagerName,jdbcType=VARCHAR},
      </if>
      <if test="regionSalesDirectorId != null">
        #{regionSalesDirectorId,jdbcType=BIGINT},
      </if>
      <if test="regionSalesDirectorName != null">
        #{regionSalesDirectorName,jdbcType=VARCHAR},
      </if>
      <if test="businessType != null">
        #{businessType,jdbcType=VARCHAR},
      </if>
      <if test="projectMode != null">
        #{projectMode,jdbcType=VARCHAR},
      </if>
      <if test="projectSubMode != null">
        #{projectSubMode,jdbcType=VARCHAR},
      </if>
      <if test="customerName != null">
        #{customerName,jdbcType=VARCHAR},
      </if>
      <if test="syncType != null">
        #{syncType,jdbcType=TINYINT},
      </if>
      <if test="teachDays != null">
        #{teachDays,jdbcType=REAL},
      </if>
      <if test="teachCourseCount != null">
        #{teachCourseCount,jdbcType=INTEGER},
      </if>
      <if test="teachRemark != null">
        #{teachRemark,jdbcType=VARCHAR},
      </if>
      <if test="projectHelperId != null">
        #{projectHelperId,jdbcType=VARCHAR},
      </if>
      <if test="projectHelperName != null">
        #{projectHelperName,jdbcType=VARCHAR},
      </if>
      <if test="liveEditorId != null">
        #{liveEditorId,jdbcType=VARCHAR},
      </if>
      <if test="liveEditorName != null">
        #{liveEditorName,jdbcType=VARCHAR},
      </if>
      <if test="syncTime != null">
        #{syncTime,jdbcType=TIMESTAMP},
      </if>
      <if test="invalid != null">
        #{invalid,jdbcType=TINYINT},
      </if>
      <if test="createdBy != null">
        #{createdBy,jdbcType=BIGINT},
      </if>
      <if test="updatedBy != null">
        #{updatedBy,jdbcType=BIGINT},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModified != null">
        #{gmtModified,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.dt.train.management.center.model.dao.TrainProjectDO">
    update train_project
    <set>
      <if test="crmProjectId != null">
        crm_project_id = #{crmProjectId,jdbcType=VARCHAR},
      </if>
      <if test="projectName != null">
        project_name = #{projectName,jdbcType=VARCHAR},
      </if>
      <if test="projectCode != null">
        project_code = #{projectCode,jdbcType=VARCHAR},
      </if>
      <if test="projectArea != null">
        project_area = #{projectArea,jdbcType=VARCHAR},
      </if>
      <if test="projectAreaId != null">
        project_area_id = #{projectAreaId,jdbcType=INTEGER},
      </if>
      <if test="provinceId != null">
        province_id = #{provinceId,jdbcType=VARCHAR},
      </if>
      <if test="cityId != null">
        city_id = #{cityId,jdbcType=VARCHAR},
      </if>
      <if test="areaId != null">
        area_id = #{areaId,jdbcType=VARCHAR},
      </if>
      <if test="province != null">
        province = #{province,jdbcType=VARCHAR},
      </if>
      <if test="city != null">
        city = #{city,jdbcType=VARCHAR},
      </if>
      <if test="area != null">
        area = #{area,jdbcType=VARCHAR},
      </if>
      <if test="projectType != null">
        project_type = #{projectType,jdbcType=TINYINT},
      </if>
      <if test="projectLevel != null">
        project_level = #{projectLevel,jdbcType=TINYINT},
      </if>
      <if test="startDate != null">
        start_date = #{startDate,jdbcType=TIMESTAMP},
      </if>
      <if test="endDate != null">
        end_date = #{endDate,jdbcType=TIMESTAMP},
      </if>
      <if test="projectStatus != null">
        project_status = #{projectStatus,jdbcType=TINYINT},
      </if>
      <if test="scheduleStatus != null">
        schedule_status = #{scheduleStatus,jdbcType=TINYINT},
      </if>
      <if test="isRemote != null">
        is_remote = #{isRemote,jdbcType=TINYINT},
      </if>
      <if test="actionType != null">
        action_type = #{actionType,jdbcType=TINYINT},
      </if>
      <if test="executeContactName != null">
        execute_contact_name = #{executeContactName,jdbcType=VARCHAR},
      </if>
      <if test="executeContactPhone != null">
        execute_contact_phone = #{executeContactPhone,jdbcType=VARCHAR},
      </if>
      <if test="executeContactJob != null">
        execute_contact_job = #{executeContactJob,jdbcType=VARCHAR},
      </if>
      <if test="totalHours != null">
        total_hours = #{totalHours,jdbcType=VARCHAR},
      </if>
      <if test="totalUserCount != null">
        total_user_count = #{totalUserCount,jdbcType=INTEGER},
      </if>
      <if test="projectManagerId != null">
        project_manager_id = #{projectManagerId,jdbcType=VARCHAR},
      </if>
      <if test="projectManagerName != null">
        project_manager_name = #{projectManagerName,jdbcType=VARCHAR},
      </if>
      <if test="projectEditorId != null">
        project_editor_id = #{projectEditorId,jdbcType=VARCHAR},
      </if>
      <if test="projectEditorName != null">
        project_editor_name = #{projectEditorName,jdbcType=VARCHAR},
      </if>
      <if test="serviceObject != null">
        service_object = #{serviceObject,jdbcType=VARCHAR},
      </if>
      <if test="serviceContent != null">
        service_content = #{serviceContent,jdbcType=VARCHAR},
      </if>
      <if test="salesUserId != null">
        sales_user_id = #{salesUserId,jdbcType=BIGINT},
      </if>
      <if test="salesUserName != null">
        sales_user_name = #{salesUserName,jdbcType=VARCHAR},
      </if>
      <if test="provinceSalesManagerId != null">
        province_sales_manager_id = #{provinceSalesManagerId,jdbcType=BIGINT},
      </if>
      <if test="provinceSalesManagerName != null">
        province_sales_manager_name = #{provinceSalesManagerName,jdbcType=VARCHAR},
      </if>
      <if test="regionSalesDirectorId != null">
        region_sales_director_id = #{regionSalesDirectorId,jdbcType=BIGINT},
      </if>
      <if test="regionSalesDirectorName != null">
        region_sales_director_name = #{regionSalesDirectorName,jdbcType=VARCHAR},
      </if>
      <if test="businessType != null">
        business_type = #{businessType,jdbcType=VARCHAR},
      </if>
      <if test="projectMode != null">
        project_mode = #{projectMode,jdbcType=VARCHAR},
      </if>
      <if test="projectSubMode != null">
        project_sub_mode = #{projectSubMode,jdbcType=VARCHAR},
      </if>
      <if test="customerName != null">
        customer_name = #{customerName,jdbcType=VARCHAR},
      </if>
      <if test="syncType != null">
        sync_type = #{syncType,jdbcType=TINYINT},
      </if>
      <if test="teachDays != null">
        teach_days = #{teachDays,jdbcType=REAL},
      </if>
      <if test="teachCourseCount != null">
        teach_course_count = #{teachCourseCount,jdbcType=INTEGER},
      </if>
      <if test="teachRemark != null">
        teach_remark = #{teachRemark,jdbcType=VARCHAR},
      </if>
      <if test="projectHelperId != null">
        project_helper_id = #{projectHelperId,jdbcType=VARCHAR},
      </if>
      <if test="projectHelperName != null">
        project_helper_name = #{projectHelperName,jdbcType=VARCHAR},
      </if>
      <if test="liveEditorId != null">
        live_editor_id = #{liveEditorId,jdbcType=VARCHAR},
      </if>
      <if test="liveEditorName != null">
        live_editor_name = #{liveEditorName,jdbcType=VARCHAR},
      </if>
      <if test="syncTime != null">
        sync_time = #{syncTime,jdbcType=TIMESTAMP},
      </if>
      <if test="invalid != null">
        invalid = #{invalid,jdbcType=TINYINT},
      </if>
      <if test="createdBy != null">
        created_by = #{createdBy,jdbcType=BIGINT},
      </if>
      <if test="updatedBy != null">
        updated_by = #{updatedBy,jdbcType=BIGINT},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModified != null">
        gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.dt.train.management.center.model.dao.TrainProjectDO">
    update train_project
    set crm_project_id = #{crmProjectId,jdbcType=VARCHAR},
      project_name = #{projectName,jdbcType=VARCHAR},
      project_code = #{projectCode,jdbcType=VARCHAR},
      project_area = #{projectArea,jdbcType=VARCHAR},
      project_area_id = #{projectAreaId,jdbcType=INTEGER},
      province_id = #{provinceId,jdbcType=VARCHAR},
      city_id = #{cityId,jdbcType=VARCHAR},
      area_id = #{areaId,jdbcType=VARCHAR},
      province = #{province,jdbcType=VARCHAR},
      city = #{city,jdbcType=VARCHAR},
      area = #{area,jdbcType=VARCHAR},
      project_type = #{projectType,jdbcType=TINYINT},
      project_level = #{projectLevel,jdbcType=TINYINT},
      start_date = #{startDate,jdbcType=TIMESTAMP},
      end_date = #{endDate,jdbcType=TIMESTAMP},
      project_status = #{projectStatus,jdbcType=TINYINT},
      schedule_status = #{scheduleStatus,jdbcType=TINYINT},
      is_remote = #{isRemote,jdbcType=TINYINT},
      action_type = #{actionType,jdbcType=TINYINT},
      execute_contact_name = #{executeContactName,jdbcType=VARCHAR},
      execute_contact_phone = #{executeContactPhone,jdbcType=VARCHAR},
      execute_contact_job = #{executeContactJob,jdbcType=VARCHAR},
      total_hours = #{totalHours,jdbcType=VARCHAR},
      total_user_count = #{totalUserCount,jdbcType=INTEGER},
      project_manager_id = #{projectManagerId,jdbcType=VARCHAR},
      project_manager_name = #{projectManagerName,jdbcType=VARCHAR},
      project_editor_id = #{projectEditorId,jdbcType=VARCHAR},
      project_editor_name = #{projectEditorName,jdbcType=VARCHAR},
      service_object = #{serviceObject,jdbcType=VARCHAR},
      service_content = #{serviceContent,jdbcType=VARCHAR},
      sales_user_id = #{salesUserId,jdbcType=BIGINT},
      sales_user_name = #{salesUserName,jdbcType=VARCHAR},
      province_sales_manager_id = #{provinceSalesManagerId,jdbcType=BIGINT},
      province_sales_manager_name = #{provinceSalesManagerName,jdbcType=VARCHAR},
      region_sales_director_id = #{regionSalesDirectorId,jdbcType=BIGINT},
      region_sales_director_name = #{regionSalesDirectorName,jdbcType=VARCHAR},
      business_type = #{businessType,jdbcType=VARCHAR},
      project_mode = #{projectMode,jdbcType=VARCHAR},
      project_sub_mode = #{projectSubMode,jdbcType=VARCHAR},
      customer_name = #{customerName,jdbcType=VARCHAR},
      sync_type = #{syncType,jdbcType=TINYINT},
      teach_days = #{teachDays,jdbcType=REAL},
      teach_course_count = #{teachCourseCount,jdbcType=INTEGER},
      teach_remark = #{teachRemark,jdbcType=VARCHAR},
      project_helper_id = #{projectHelperId,jdbcType=VARCHAR},
      project_helper_name = #{projectHelperName,jdbcType=VARCHAR},
      live_editor_id = #{liveEditorId,jdbcType=VARCHAR},
      live_editor_name = #{liveEditorName,jdbcType=VARCHAR},
      sync_time = #{syncTime,jdbcType=TIMESTAMP},
      invalid = #{invalid,jdbcType=TINYINT},
      created_by = #{createdBy,jdbcType=BIGINT},
      updated_by = #{updatedBy,jdbcType=BIGINT},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_modified = #{gmtModified,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>