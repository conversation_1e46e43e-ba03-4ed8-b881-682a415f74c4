<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dt.train.management.center.mapper.SysPermissionDOMapper">
  <resultMap id="BaseResultMap" type="com.dt.train.management.center.model.dao.SysPermissionDO">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="parent_id" jdbcType="INTEGER" property="parentId" />
    <result column="permission_name" jdbcType="VARCHAR" property="permissionName" />
    <result column="permission_type" jdbcType="VARCHAR" property="permissionType" />
    <result column="permission_key" jdbcType="VARCHAR" property="permissionKey" />
    <result column="p_path" jdbcType="VARCHAR" property="pPath" />
    <result column="icon" jdbcType="VARCHAR" property="icon" />
    <result column="sort_order" jdbcType="INTEGER" property="sortOrder" />
    <result column="can_select" jdbcType="TINYINT" property="canSelect" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
  </resultMap>
  <sql id="Base_Column_List">
    id, parent_id, permission_name, permission_type, permission_key, p_path, icon, sort_order,
    can_select, remark, gmt_create, gmt_modified
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from sys_permission
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from sys_permission
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.dt.train.management.center.model.dao.SysPermissionDO">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into sys_permission (parent_id, permission_name, permission_type,
      permission_key, p_path, icon,
      sort_order, can_select, remark,
      gmt_create, gmt_modified)
    values (#{parentId,jdbcType=INTEGER}, #{permissionName,jdbcType=VARCHAR}, #{permissionType,jdbcType=VARCHAR},
      #{permissionKey,jdbcType=VARCHAR}, #{pPath,jdbcType=VARCHAR}, #{icon,jdbcType=VARCHAR},
      #{sortOrder,jdbcType=INTEGER}, #{canSelect,jdbcType=TINYINT}, #{remark,jdbcType=VARCHAR},
      #{gmtCreate,jdbcType=TIMESTAMP}, #{gmtModified,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.dt.train.management.center.model.dao.SysPermissionDO">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into sys_permission
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="parentId != null">
        parent_id,
      </if>
      <if test="permissionName != null">
        permission_name,
      </if>
      <if test="permissionType != null">
        permission_type,
      </if>
      <if test="permissionKey != null">
        permission_key,
      </if>
      <if test="pPath != null">
        p_path,
      </if>
      <if test="icon != null">
        icon,
      </if>
      <if test="sortOrder != null">
        sort_order,
      </if>
      <if test="canSelect != null">
        can_select,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtModified != null">
        gmt_modified,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="parentId != null">
        #{parentId,jdbcType=INTEGER},
      </if>
      <if test="permissionName != null">
        #{permissionName,jdbcType=VARCHAR},
      </if>
      <if test="permissionType != null">
        #{permissionType,jdbcType=VARCHAR},
      </if>
      <if test="permissionKey != null">
        #{permissionKey,jdbcType=VARCHAR},
      </if>
      <if test="pPath != null">
        #{pPath,jdbcType=VARCHAR},
      </if>
      <if test="icon != null">
        #{icon,jdbcType=VARCHAR},
      </if>
      <if test="sortOrder != null">
        #{sortOrder,jdbcType=INTEGER},
      </if>
      <if test="canSelect != null">
        #{canSelect,jdbcType=TINYINT},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModified != null">
        #{gmtModified,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.dt.train.management.center.model.dao.SysPermissionDO">
    update sys_permission
    <set>
      <if test="parentId != null">
        parent_id = #{parentId,jdbcType=INTEGER},
      </if>
      <if test="permissionName != null">
        permission_name = #{permissionName,jdbcType=VARCHAR},
      </if>
      <if test="permissionType != null">
        permission_type = #{permissionType,jdbcType=VARCHAR},
      </if>
      <if test="permissionKey != null">
        permission_key = #{permissionKey,jdbcType=VARCHAR},
      </if>
      <if test="pPath != null">
        p_path = #{pPath,jdbcType=VARCHAR},
      </if>
      <if test="icon != null">
        icon = #{icon,jdbcType=VARCHAR},
      </if>
      <if test="sortOrder != null">
        sort_order = #{sortOrder,jdbcType=INTEGER},
      </if>
      <if test="canSelect != null">
        can_select = #{canSelect,jdbcType=TINYINT},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModified != null">
        gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.dt.train.management.center.model.dao.SysPermissionDO">
    update sys_permission
    set parent_id = #{parentId,jdbcType=INTEGER},
      permission_name = #{permissionName,jdbcType=VARCHAR},
      permission_type = #{permissionType,jdbcType=VARCHAR},
      permission_key = #{permissionKey,jdbcType=VARCHAR},
      p_path = #{pPath,jdbcType=VARCHAR},
      icon = #{icon,jdbcType=VARCHAR},
      sort_order = #{sortOrder,jdbcType=INTEGER},
      can_select = #{canSelect,jdbcType=TINYINT},
      remark = #{remark,jdbcType=VARCHAR},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_modified = #{gmtModified,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>

  <!-- selectAll --> 

  <select id="selectAll" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from sys_permission
  </select>

</mapper>