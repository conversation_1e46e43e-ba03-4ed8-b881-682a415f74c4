<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dt.train.management.center.mapper.TrainCourseUserRelationDOMapper">
  <resultMap id="BaseResultMap" type="com.dt.train.management.center.model.dao.TrainCourseUserRelationDO">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="project_id" jdbcType="INTEGER" property="projectId" />
    <result column="project_area_id" jdbcType="INTEGER" property="projectAreaId" />
    <result column="course_id" jdbcType="INTEGER" property="courseId" />
    <result column="course_area_id" jdbcType="INTEGER" property="courseAreaId" />
    <result column="user_id" jdbcType="BIGINT" property="userId" />
    <result column="user_name" jdbcType="VARCHAR" property="userName" />
    <result column="origin_user_name" jdbcType="VARCHAR" property="originUserName" />
    <result column="user_type" jdbcType="TINYINT" property="userType" />
    <result column="user_dept_id" jdbcType="VARCHAR" property="userDeptId" />
    <result column="user_dept_name" jdbcType="VARCHAR" property="userDeptName" />
    <result column="relation_type" jdbcType="INTEGER" property="relationType" />
    <result column="start_date" jdbcType="TIMESTAMP" property="startDate" />
    <result column="end_date" jdbcType="TIMESTAMP" property="endDate" />
    <result column="invalid" jdbcType="TINYINT" property="invalid" />
    <result column="created_by" jdbcType="BIGINT" property="createdBy" />
    <result column="updated_by" jdbcType="BIGINT" property="updatedBy" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
  </resultMap>
  <sql id="Base_Column_List">
    id, project_id, project_area_id, course_id, course_area_id, user_id, user_name, origin_user_name, user_type, user_dept_id, user_dept_name, relation_type, start_date, end_date, invalid,
    created_by, updated_by, gmt_create, gmt_modified
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from train_course_user_relation
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from train_course_user_relation
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.dt.train.management.center.model.dao.TrainCourseUserRelationDO">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into train_course_user_relation (project_id, project_area_id, course_id, course_area_id, user_id, user_name, origin_user_name, user_type,
      user_dept_id, user_dept_name, relation_type, start_date, end_date,
      invalid, created_by, updated_by,
      gmt_create, gmt_modified)
    values (#{projectId,jdbcType=INTEGER}, #{projectAreaId,jdbcType=INTEGER}, #{courseId,jdbcType=INTEGER}, #{courseAreaId,jdbcType=INTEGER}, #{userId,jdbcType=BIGINT},
      #{userName,jdbcType=VARCHAR}, #{originUserName,jdbcType=VARCHAR}, #{userType,jdbcType=TINYINT}, #{userDeptId,jdbcType=VARCHAR}, #{userDeptName,jdbcType=VARCHAR}, #{relationType,jdbcType=INTEGER}, #{startDate,jdbcType=TIMESTAMP}, #{endDate,jdbcType=TIMESTAMP},
      #{invalid,jdbcType=TINYINT}, #{createdBy,jdbcType=BIGINT}, #{updatedBy,jdbcType=BIGINT},
      #{gmtCreate,jdbcType=TIMESTAMP}, #{gmtModified,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.dt.train.management.center.model.dao.TrainCourseUserRelationDO">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into train_course_user_relation
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="projectId != null">
        project_id,
      </if>
      <if test="projectAreaId != null">
        project_area_id,
      </if>
      <if test="courseId != null">
        course_id,
      </if>
      <if test="courseAreaId != null">
        course_area_id,
      </if>
      <if test="userId != null">
        user_id,
      </if>
      <if test="userName != null">
        user_name,
      </if>
      <if test="originUserName != null">
        origin_user_name,
      </if>
      <if test="userType != null">
        user_type,
      </if>
      <if test="userDeptId != null">
        user_dept_id,
      </if>
      <if test="userDeptName != null">
        user_dept_name,
      </if>
      <if test="relationType != null">
        relation_type,
      </if>
      <if test="startDate != null">
        start_date,
      </if>
      <if test="endDate != null">
        end_date,
      </if>
      <if test="invalid != null">
        invalid,
      </if>
      <if test="createdBy != null">
        created_by,
      </if>
      <if test="updatedBy != null">
        updated_by,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtModified != null">
        gmt_modified,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="projectId != null">
        #{projectId,jdbcType=INTEGER},
      </if>
      <if test="projectAreaId != null">
        #{projectAreaId,jdbcType=INTEGER},
      </if>
      <if test="courseId != null">
        #{courseId,jdbcType=INTEGER},
      </if>
      <if test="courseAreaId != null">
        #{courseAreaId,jdbcType=INTEGER},
      </if>
      <if test="userId != null">
        #{userId,jdbcType=BIGINT},
      </if>
      <if test="userName != null">
        #{userName,jdbcType=VARCHAR},
      </if>
      <if test="originUserName != null">
        #{originUserName,jdbcType=VARCHAR},
      </if>
      <if test="userType != null">
        #{userType,jdbcType=TINYINT},
      </if>
      <if test="userDeptId != null">
        #{userDeptId,jdbcType=VARCHAR},
      </if>
      <if test="userDeptName != null">
        #{userDeptName,jdbcType=VARCHAR},
      </if>
      <if test="relationType != null">
        #{relationType,jdbcType=INTEGER},
      </if>
      <if test="startDate != null">
        #{startDate,jdbcType=TIMESTAMP},
      </if>
      <if test="endDate != null">
        #{endDate,jdbcType=TIMESTAMP},
      </if>
      <if test="invalid != null">
        #{invalid,jdbcType=TINYINT},
      </if>
      <if test="createdBy != null">
        #{createdBy,jdbcType=BIGINT},
      </if>
      <if test="updatedBy != null">
        #{updatedBy,jdbcType=BIGINT},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModified != null">
        #{gmtModified,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.dt.train.management.center.model.dao.TrainCourseUserRelationDO">
    update train_course_user_relation
    <set>
      <if test="projectId != null">
        project_id = #{projectId,jdbcType=INTEGER},
      </if>
      <if test="projectAreaId != null">
        project_area_id = #{projectAreaId,jdbcType=INTEGER},
      </if>
      <if test="courseId != null">
        course_id = #{courseId,jdbcType=INTEGER},
      </if>
      <if test="courseAreaId != null">
        course_area_id = #{courseAreaId,jdbcType=INTEGER},
      </if>
      <if test="userId != null">
        user_id = #{userId,jdbcType=BIGINT},
      </if>
      <if test="userName != null">
        user_name = #{userName,jdbcType=VARCHAR},
      </if>
      <if test="originUserName != null">
        origin_user_name = #{originUserName,jdbcType=VARCHAR},
      </if>
      <if test="userType != null">
        user_type = #{userType,jdbcType=TINYINT},
      </if>
      <if test="userDeptId != null">
        user_dept_id = #{userDeptId,jdbcType=VARCHAR},
      </if>
      <if test="userDeptName != null">
        user_dept_name = #{userDeptName,jdbcType=VARCHAR},
      </if>
      <if test="relationType != null">
        relation_type = #{relationType,jdbcType=INTEGER},
      </if>
      <if test="startDate != null">
        start_date = #{startDate,jdbcType=TIMESTAMP},
      </if>
      <if test="endDate != null">
        end_date = #{endDate,jdbcType=TIMESTAMP},
      </if>
      <if test="invalid != null">
        invalid = #{invalid,jdbcType=TINYINT},
      </if>
      <if test="createdBy != null">
        created_by = #{createdBy,jdbcType=BIGINT},
      </if>
      <if test="updatedBy != null">
        updated_by = #{updatedBy,jdbcType=BIGINT},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModified != null">
        gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.dt.train.management.center.model.dao.TrainCourseUserRelationDO">
    update train_course_user_relation
    set project_id = #{projectId,jdbcType=INTEGER},
      project_area_id = #{projectAreaId,jdbcType=INTEGER},
      course_id = #{courseId,jdbcType=INTEGER},
      course_area_id = #{courseAreaId,jdbcType=INTEGER},
      user_id = #{userId,jdbcType=BIGINT},
      user_name = #{userName,jdbcType=VARCHAR},
      origin_user_name = #{originUserName,jdbcType=VARCHAR},
      user_type = #{userType,jdbcType=TINYINT},
      user_dept_id = #{userDeptId,jdbcType=VARCHAR},
      user_dept_name = #{userDeptName,jdbcType=VARCHAR},
      relation_type = #{relationType,jdbcType=INTEGER},
      start_date = #{startDate,jdbcType=TIMESTAMP},
      end_date = #{endDate,jdbcType=TIMESTAMP},
      invalid = #{invalid,jdbcType=TINYINT},
      created_by = #{createdBy,jdbcType=BIGINT},
      updated_by = #{updatedBy,jdbcType=BIGINT},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_modified = #{gmtModified,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>

  <!-- deleteByCourseIdAndRelationType --> 

  <update id="deleteByCourseIdAndRelationType" parameterType="com.dt.train.management.center.model.dao.TrainCourseUserRelationDO">
    update train_course_user_relation
    set invalid = 1
    where course_id = #{courseId,jdbcType=INTEGER}
    <if test="relationType != null">
      and relation_type = #{relationType,jdbcType=INTEGER}
    </if>
  </update>

  <!-- selectByProjectIds --> 

  <select id="selectByProjectIds" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from train_course_user_relation
    where project_id in
    <foreach collection="projectIds" item="projectId" separator="," open="(" close=")">
      #{projectId,jdbcType=INTEGER}
    </foreach>
    and invalid = 0
    order by start_date asc
  </select>

  <!-- selectByCourseIds --> 

  <select id="selectByCourseIds" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from train_course_user_relation
    where course_id in
    <foreach collection="courseIds" item="courseId" separator="," open="(" close=")">
      #{courseId,jdbcType=INTEGER}
    </foreach>
    and invalid = 0
    order by start_date asc
  </select>  

  <!-- selectByTime --> 

  <select id="selectByQuery" parameterType="com.dt.train.management.center.model.dto.project.CourseUserRelationQueryDTO" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from train_course_user_relation
    where invalid = 0
    <if test="startDate != null">
      and end_date &gt;= #{startDate,jdbcType=TIMESTAMP}
    </if>
    <if test="endDate != null">
      and start_date &lt;= #{endDate,jdbcType=TIMESTAMP}
    </if>
    <if test="userId != null">
      and user_id = #{userId,jdbcType=BIGINT}
    </if>
    <if test="courseId != null">
      and course_id = #{courseId,jdbcType=INTEGER}
    </if>
    <if test="projectId != null">
      and project_id = #{projectId,jdbcType=INTEGER}
    </if>
    order by start_date asc
  </select>

  <resultMap id="WithCourseResultMap" type="com.dt.train.management.center.model.dto.task.TrainCourseUserRelationDTOWithCourse" extends="BaseResultMap">
    <result column="course_name" jdbcType="VARCHAR" property="courseName" />
    <result column="course_form" jdbcType="INTEGER" property="courseForm" />
    <result column="course_status" jdbcType="INTEGER" property="courseStatus" />
  </resultMap>

  <sql id="WithCourse_Column_List">
    tcur.id, tcur.project_id, tcur.course_id, tcur.user_id, tcur.user_name, tcur.origin_user_name, tcur.user_dept_id, tcur.user_dept_name, tcur.relation_type, tcur.start_date, tcur.end_date,
    tc.course_name, tc.course_form, tc.course_status
  </sql>

  <!-- listWithCourseByQuery --> 

  <select id="listWithCourseByQuery" parameterType="com.dt.train.management.center.model.dto.project.CourseUserRelationQueryDTO" resultMap="WithCourseResultMap">
    select
    <include refid="WithCourse_Column_List" />
    from train_course_user_relation tcur
    left join train_course tc on tcur.course_id = tc.id
    where tcur.invalid = 0
    <if test="startDate != null">
      and end_date &gt;= #{startDate,jdbcType=TIMESTAMP}
    </if>
    <if test="endDate != null">
      and start_date &lt;= #{endDate,jdbcType=TIMESTAMP}
    </if>
    <if test="userId != null">
      and user_id = #{userId,jdbcType=BIGINT}
    </if>
    <if test="userIds != null and userIds.size() > 0">
      and user_id in
      <foreach collection="userIds" item="userId" separator="," open="(" close=")">
        #{userId,jdbcType=BIGINT}
      </foreach>
    </if>
    <if test="courseId != null">
      and course_id = #{courseId,jdbcType=INTEGER}
    </if>
    <if test="projectId != null">
      and project_id = #{projectId,jdbcType=INTEGER}
    </if>
    order by start_date asc
  </select>
  <update id="batchUpdateProjectAreaIdByProjectId">
      UPDATE train_course_user_relation
        SET project_area_id = #{newAreaId}
        WHERE project_id = #{projectId}
  </update>
</mapper>
