<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dt.train.management.center.mapper.SmartConfigDOMapper">
  <resultMap id="BaseResultMap" type="com.dt.train.management.center.model.dao.SmartConfigDO">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="config_key" jdbcType="VARCHAR" property="configKey" />
    <result column="second_key" jdbcType="VARCHAR" property="secondKey" />
    <result column="config_order" jdbcType="INTEGER" property="configOrder" />
    <result column="ext_info" jdbcType="VARCHAR" property="extInfo" />
    <result column="config_type" jdbcType="TINYINT" property="configType" />
    <result column="invalid" jdbcType="TINYINT" property="invalid" />
    <result column="created_by" jdbcType="BIGINT" property="createdBy" />
    <result column="updated_by" jdbcType="BIGINT" property="updatedBy" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.dt.train.management.center.model.dao.SmartConfigDO">
    <result column="config_value" jdbcType="LONGVARCHAR" property="configValue" />
  </resultMap>
  <sql id="Base_Column_List">
    id, config_key, second_key, config_order, ext_info, config_type, invalid, created_by, 
    updated_by, gmt_create, gmt_modified
  </sql>
  <sql id="Blob_Column_List">
    config_value
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="ResultMapWithBLOBs">
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from smart_config
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from smart_config
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.dt.train.management.center.model.dao.SmartConfigDO">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into smart_config (config_key, second_key, config_order, 
      ext_info, config_type, invalid, 
      created_by, updated_by, gmt_create, 
      gmt_modified, config_value)
    values (#{configKey,jdbcType=VARCHAR}, #{secondKey,jdbcType=VARCHAR}, #{configOrder,jdbcType=INTEGER}, 
      #{extInfo,jdbcType=VARCHAR}, #{configType,jdbcType=TINYINT}, #{invalid,jdbcType=TINYINT}, 
      #{createdBy,jdbcType=BIGINT}, #{updatedBy,jdbcType=BIGINT}, #{gmtCreate,jdbcType=TIMESTAMP}, 
      #{gmtModified,jdbcType=TIMESTAMP}, #{configValue,jdbcType=LONGVARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.dt.train.management.center.model.dao.SmartConfigDO">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into smart_config
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="configKey != null">
        config_key,
      </if>
      <if test="secondKey != null">
        second_key,
      </if>
      <if test="configOrder != null">
        config_order,
      </if>
      <if test="extInfo != null">
        ext_info,
      </if>
      <if test="configType != null">
        config_type,
      </if>
      <if test="invalid != null">
        invalid,
      </if>
      <if test="createdBy != null">
        created_by,
      </if>
      <if test="updatedBy != null">
        updated_by,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtModified != null">
        gmt_modified,
      </if>
      <if test="configValue != null">
        config_value,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="configKey != null">
        #{configKey,jdbcType=VARCHAR},
      </if>
      <if test="secondKey != null">
        #{secondKey,jdbcType=VARCHAR},
      </if>
      <if test="configOrder != null">
        #{configOrder,jdbcType=INTEGER},
      </if>
      <if test="extInfo != null">
        #{extInfo,jdbcType=VARCHAR},
      </if>
      <if test="configType != null">
        #{configType,jdbcType=TINYINT},
      </if>
      <if test="invalid != null">
        #{invalid,jdbcType=TINYINT},
      </if>
      <if test="createdBy != null">
        #{createdBy,jdbcType=BIGINT},
      </if>
      <if test="updatedBy != null">
        #{updatedBy,jdbcType=BIGINT},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModified != null">
        #{gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="configValue != null">
        #{configValue,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.dt.train.management.center.model.dao.SmartConfigDO">
    update smart_config
    <set>
      <if test="configKey != null">
        config_key = #{configKey,jdbcType=VARCHAR},
      </if>
      <if test="secondKey != null">
        second_key = #{secondKey,jdbcType=VARCHAR},
      </if>
      <if test="configOrder != null">
        config_order = #{configOrder,jdbcType=INTEGER},
      </if>
      <if test="extInfo != null">
        ext_info = #{extInfo,jdbcType=VARCHAR},
      </if>
      <if test="configType != null">
        config_type = #{configType,jdbcType=TINYINT},
      </if>
      <if test="invalid != null">
        invalid = #{invalid,jdbcType=TINYINT},
      </if>
      <if test="createdBy != null">
        created_by = #{createdBy,jdbcType=BIGINT},
      </if>
      <if test="updatedBy != null">
        updated_by = #{updatedBy,jdbcType=BIGINT},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModified != null">
        gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="configValue != null">
        config_value = #{configValue,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.dt.train.management.center.model.dao.SmartConfigDO">
    update smart_config
    set config_key = #{configKey,jdbcType=VARCHAR},
      second_key = #{secondKey,jdbcType=VARCHAR},
      config_order = #{configOrder,jdbcType=INTEGER},
      ext_info = #{extInfo,jdbcType=VARCHAR},
      config_type = #{configType,jdbcType=TINYINT},
      invalid = #{invalid,jdbcType=TINYINT},
      created_by = #{createdBy,jdbcType=BIGINT},
      updated_by = #{updatedBy,jdbcType=BIGINT},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
      config_value = #{configValue,jdbcType=LONGVARCHAR}
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.dt.train.management.center.model.dao.SmartConfigDO">
    update smart_config
    set config_key = #{configKey,jdbcType=VARCHAR},
      second_key = #{secondKey,jdbcType=VARCHAR},
      config_order = #{configOrder,jdbcType=INTEGER},
      ext_info = #{extInfo,jdbcType=VARCHAR},
      config_type = #{configType,jdbcType=TINYINT},
      invalid = #{invalid,jdbcType=TINYINT},
      created_by = #{createdBy,jdbcType=BIGINT},
      updated_by = #{updatedBy,jdbcType=BIGINT},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_modified = #{gmtModified,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>

  <!-- listByKey --> 

  <select id="listByKey" resultMap="ResultMapWithBLOBs">
    select
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from smart_config
    where config_key = #{configKey,jdbcType=VARCHAR}
    <if test="secondKey != null">
      and second_key = #{secondKey,jdbcType=VARCHAR}
    </if>
    ORDER BY config_order
  </select>
  
</mapper>