<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dt.train.management.center.mapper.SysRolePermissionDOMapper">
  <resultMap id="BaseResultMap" type="com.dt.train.management.center.model.dao.SysRolePermissionDO">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="role_id" jdbcType="INTEGER" property="roleId" />
    <result column="permission_key" jdbcType="VARCHAR" property="permissionKey" />
    <result column="invalid" jdbcType="TINYINT" property="invalid" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
  </resultMap>
  <sql id="Base_Column_List">
    id, role_id, permission_key, invalid, gmt_create, gmt_modified
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from sys_role_permission
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from sys_role_permission
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.dt.train.management.center.model.dao.SysRolePermissionDO">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into sys_role_permission (role_id, permission_key, invalid, 
      gmt_create, gmt_modified)
    values (#{roleId,jdbcType=INTEGER}, #{permissionKey,jdbcType=VARCHAR}, #{invalid,jdbcType=TINYINT}, 
      #{gmtCreate,jdbcType=TIMESTAMP}, #{gmtModified,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.dt.train.management.center.model.dao.SysRolePermissionDO">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into sys_role_permission
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="roleId != null">
        role_id,
      </if>
      <if test="permissionKey != null">
        permission_key,
      </if>
      <if test="invalid != null">
        invalid,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtModified != null">
        gmt_modified,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="roleId != null">
        #{roleId,jdbcType=INTEGER},
      </if>
      <if test="permissionKey != null">
        #{permissionKey,jdbcType=VARCHAR},
      </if>
      <if test="invalid != null">
        #{invalid,jdbcType=TINYINT},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModified != null">
        #{gmtModified,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.dt.train.management.center.model.dao.SysRolePermissionDO">
    update sys_role_permission
    <set>
      <if test="roleId != null">
        role_id = #{roleId,jdbcType=INTEGER},
      </if>
      <if test="permissionKey != null">
        permission_key = #{permissionKey,jdbcType=VARCHAR},
      </if>
      <if test="invalid != null">
        invalid = #{invalid,jdbcType=TINYINT},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModified != null">
        gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.dt.train.management.center.model.dao.SysRolePermissionDO">
    update sys_role_permission
    set role_id = #{roleId,jdbcType=INTEGER},
      permission_key = #{permissionKey,jdbcType=VARCHAR},
      invalid = #{invalid,jdbcType=TINYINT},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_modified = #{gmtModified,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>

  <update id="deleteByRoleId" parameterType="java.lang.Integer">
    update sys_role_permission
    set invalid = 1,
        gmt_modified = now()
    where role_id = #{roleId,jdbcType=INTEGER}
  </update>

  <select id="getPermissionKeysByRoleId" resultType="java.lang.String">
    SELECT permission_key
    FROM sys_role_permission
    WHERE role_id = #{roleId,jdbcType=INTEGER} AND invalid = 0
  </select>

  <insert id="batchInsert">
    insert into sys_role_permission (role_id, permission_key, gmt_create, gmt_modified)
    values
    <foreach collection="permissionKeys" item="permissionKey" separator=",">
      (#{roleId,jdbcType=INTEGER}, #{permissionKey,jdbcType=VARCHAR}, 
       now(), now())
    </foreach>
  </insert>
</mapper>
