<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dt.train.management.center.mapper.TrainCourseDOMapper">
  <resultMap id="BaseResultMap" type="com.dt.train.management.center.model.dao.TrainCourseDO">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="project_id" jdbcType="INTEGER" property="projectId" />
    <result column="project_name" jdbcType="VARCHAR" property="projectName" />
    <result column="project_code" jdbcType="VARCHAR" property="projectCode" />
    <result column="course_name" jdbcType="VARCHAR" property="courseName" />
    <result column="course_status" jdbcType="TINYINT" property="courseStatus" />
    <result column="course_form" jdbcType="TINYINT" property="courseForm" />
    <result column="course_type" jdbcType="TINYINT" property="courseType" />
    <result column="course_people_num" jdbcType="INTEGER" property="coursePeopleNum" />
    <result column="service_object" jdbcType="VARCHAR" property="serviceObject" />
    <result column="is_remote" jdbcType="TINYINT" property="isRemote" />
    <result column="course_area" jdbcType="VARCHAR" property="courseArea" />
    <result column="course_area_id" jdbcType="INTEGER" property="courseAreaId" />
    <result column="has_plan_desc" jdbcType="BIT" property="hasPlanDesc" />
    <result column="has_execution_progress" jdbcType="BIT" property="hasExecutionProgress" />
    <result column="plan_start_date" jdbcType="TIMESTAMP" property="planStartDate" />
    <result column="plan_end_date" jdbcType="TIMESTAMP" property="planEndDate" />
    <result column="plan_days" jdbcType="REAL" property="planDays" />
    <result column="real_start_date" jdbcType="TIMESTAMP" property="realStartDate" />
    <result column="real_end_date" jdbcType="TIMESTAMP" property="realEndDate" />
    <result column="real_days" jdbcType="REAL" property="realDays" />
    <result column="total_person_days" jdbcType="REAL" property="totalPersonDays" />
    <result column="invalid" jdbcType="TINYINT" property="invalid" />
    <result column="created_by" jdbcType="BIGINT" property="createdBy" />
    <result column="updated_by" jdbcType="BIGINT" property="updatedBy" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.dt.train.management.center.model.dao.TrainCourseDOWithBLOBs">
    <result column="plan_desc" jdbcType="LONGVARCHAR" property="planDesc" />
    <result column="execution_progress" jdbcType="LONGVARCHAR" property="executionProgress" />
  </resultMap>
  <sql id="Base_Column_List">
    id, project_id, project_name, project_code, course_name, course_status, course_form, 
    course_type, course_people_num, service_object, is_remote, course_area, course_area_id, 
    has_plan_desc, has_execution_progress, plan_start_date, plan_end_date, plan_days, 
    real_start_date, real_end_date, real_days, total_person_days, invalid, created_by, 
    updated_by, gmt_create, gmt_modified
  </sql>
  <sql id="Blob_Column_List">
    plan_desc, execution_progress
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="ResultMapWithBLOBs">
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from train_course
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from train_course
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.dt.train.management.center.model.dao.TrainCourseDOWithBLOBs">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into train_course (project_id, project_name, project_code, 
      course_name, course_status, course_form, 
      course_type, course_people_num, service_object, 
      is_remote, course_area, course_area_id, 
      has_plan_desc, has_execution_progress, plan_start_date, 
      plan_end_date, plan_days, real_start_date, 
      real_end_date, real_days, total_person_days, 
      invalid, created_by, updated_by, 
      gmt_create, gmt_modified, plan_desc, 
      execution_progress)
    values (#{projectId,jdbcType=INTEGER}, #{projectName,jdbcType=VARCHAR}, #{projectCode,jdbcType=VARCHAR}, 
      #{courseName,jdbcType=VARCHAR}, #{courseStatus,jdbcType=TINYINT}, #{courseForm,jdbcType=TINYINT}, 
      #{courseType,jdbcType=TINYINT}, #{coursePeopleNum,jdbcType=INTEGER}, #{serviceObject,jdbcType=VARCHAR}, 
      #{isRemote,jdbcType=TINYINT}, #{courseArea,jdbcType=VARCHAR}, #{courseAreaId,jdbcType=INTEGER}, 
      #{hasPlanDesc,jdbcType=BIT}, #{hasExecutionProgress,jdbcType=BIT}, #{planStartDate,jdbcType=TIMESTAMP}, 
      #{planEndDate,jdbcType=TIMESTAMP}, #{planDays,jdbcType=REAL}, #{realStartDate,jdbcType=TIMESTAMP}, 
      #{realEndDate,jdbcType=TIMESTAMP}, #{realDays,jdbcType=REAL}, #{totalPersonDays,jdbcType=REAL}, 
      #{invalid,jdbcType=TINYINT}, #{createdBy,jdbcType=BIGINT}, #{updatedBy,jdbcType=BIGINT}, 
      #{gmtCreate,jdbcType=TIMESTAMP}, #{gmtModified,jdbcType=TIMESTAMP}, #{planDesc,jdbcType=LONGVARCHAR}, 
      #{executionProgress,jdbcType=LONGVARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.dt.train.management.center.model.dao.TrainCourseDOWithBLOBs">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into train_course
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="projectId != null">
        project_id,
      </if>
      <if test="projectName != null">
        project_name,
      </if>
      <if test="projectCode != null">
        project_code,
      </if>
      <if test="courseName != null">
        course_name,
      </if>
      <if test="courseStatus != null">
        course_status,
      </if>
      <if test="courseForm != null">
        course_form,
      </if>
      <if test="courseType != null">
        course_type,
      </if>
      <if test="coursePeopleNum != null">
        course_people_num,
      </if>
      <if test="serviceObject != null">
        service_object,
      </if>
      <if test="isRemote != null">
        is_remote,
      </if>
      <if test="courseArea != null">
        course_area,
      </if>
      <if test="courseAreaId != null">
        course_area_id,
      </if>
      <if test="hasPlanDesc != null">
        has_plan_desc,
      </if>
      <if test="hasExecutionProgress != null">
        has_execution_progress,
      </if>
      <if test="planStartDate != null">
        plan_start_date,
      </if>
      <if test="planEndDate != null">
        plan_end_date,
      </if>
      <if test="planDays != null">
        plan_days,
      </if>
      <if test="realStartDate != null">
        real_start_date,
      </if>
      <if test="realEndDate != null">
        real_end_date,
      </if>
      <if test="realDays != null">
        real_days,
      </if>
      <if test="totalPersonDays != null">
        total_person_days,
      </if>
      <if test="invalid != null">
        invalid,
      </if>
      <if test="createdBy != null">
        created_by,
      </if>
      <if test="updatedBy != null">
        updated_by,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtModified != null">
        gmt_modified,
      </if>
      <if test="planDesc != null">
        plan_desc,
      </if>
      <if test="executionProgress != null">
        execution_progress,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="projectId != null">
        #{projectId,jdbcType=INTEGER},
      </if>
      <if test="projectName != null">
        #{projectName,jdbcType=VARCHAR},
      </if>
      <if test="projectCode != null">
        #{projectCode,jdbcType=VARCHAR},
      </if>
      <if test="courseName != null">
        #{courseName,jdbcType=VARCHAR},
      </if>
      <if test="courseStatus != null">
        #{courseStatus,jdbcType=TINYINT},
      </if>
      <if test="courseForm != null">
        #{courseForm,jdbcType=TINYINT},
      </if>
      <if test="courseType != null">
        #{courseType,jdbcType=TINYINT},
      </if>
      <if test="coursePeopleNum != null">
        #{coursePeopleNum,jdbcType=INTEGER},
      </if>
      <if test="serviceObject != null">
        #{serviceObject,jdbcType=VARCHAR},
      </if>
      <if test="isRemote != null">
        #{isRemote,jdbcType=TINYINT},
      </if>
      <if test="courseArea != null">
        #{courseArea,jdbcType=VARCHAR},
      </if>
      <if test="courseAreaId != null">
        #{courseAreaId,jdbcType=INTEGER},
      </if>
      <if test="hasPlanDesc != null">
        #{hasPlanDesc,jdbcType=BIT},
      </if>
      <if test="hasExecutionProgress != null">
        #{hasExecutionProgress,jdbcType=BIT},
      </if>
      <if test="planStartDate != null">
        #{planStartDate,jdbcType=TIMESTAMP},
      </if>
      <if test="planEndDate != null">
        #{planEndDate,jdbcType=TIMESTAMP},
      </if>
      <if test="planDays != null">
        #{planDays,jdbcType=REAL},
      </if>
      <if test="realStartDate != null">
        #{realStartDate,jdbcType=TIMESTAMP},
      </if>
      <if test="realEndDate != null">
        #{realEndDate,jdbcType=TIMESTAMP},
      </if>
      <if test="realDays != null">
        #{realDays,jdbcType=REAL},
      </if>
      <if test="totalPersonDays != null">
        #{totalPersonDays,jdbcType=REAL},
      </if>
      <if test="invalid != null">
        #{invalid,jdbcType=TINYINT},
      </if>
      <if test="createdBy != null">
        #{createdBy,jdbcType=BIGINT},
      </if>
      <if test="updatedBy != null">
        #{updatedBy,jdbcType=BIGINT},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModified != null">
        #{gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="planDesc != null">
        #{planDesc,jdbcType=LONGVARCHAR},
      </if>
      <if test="executionProgress != null">
        #{executionProgress,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.dt.train.management.center.model.dao.TrainCourseDOWithBLOBs">
    update train_course
    <set>
      <if test="projectId != null">
        project_id = #{projectId,jdbcType=INTEGER},
      </if>
      <if test="projectName != null">
        project_name = #{projectName,jdbcType=VARCHAR},
      </if>
      <if test="projectCode != null">
        project_code = #{projectCode,jdbcType=VARCHAR},
      </if>
      <if test="courseName != null">
        course_name = #{courseName,jdbcType=VARCHAR},
      </if>
      <if test="courseStatus != null">
        course_status = #{courseStatus,jdbcType=TINYINT},
      </if>
      <if test="courseForm != null">
        course_form = #{courseForm,jdbcType=TINYINT},
      </if>
      <if test="courseType != null">
        course_type = #{courseType,jdbcType=TINYINT},
      </if>
      <if test="coursePeopleNum != null">
        course_people_num = #{coursePeopleNum,jdbcType=INTEGER},
      </if>
      <if test="serviceObject != null">
        service_object = #{serviceObject,jdbcType=VARCHAR},
      </if>
      <if test="isRemote != null">
        is_remote = #{isRemote,jdbcType=TINYINT},
      </if>
      <if test="courseArea != null">
        course_area = #{courseArea,jdbcType=VARCHAR},
      </if>
      <if test="courseAreaId != null">
        course_area_id = #{courseAreaId,jdbcType=INTEGER},
      </if>
      <if test="hasPlanDesc != null">
        has_plan_desc = #{hasPlanDesc,jdbcType=BIT},
      </if>
      <if test="hasExecutionProgress != null">
        has_execution_progress = #{hasExecutionProgress,jdbcType=BIT},
      </if>
        plan_start_date = #{planStartDate,jdbcType=TIMESTAMP},
        plan_end_date = #{planEndDate,jdbcType=TIMESTAMP},
      <if test="planDays != null">
        plan_days = #{planDays,jdbcType=REAL},
      </if>
      <if test="realStartDate != null">
        real_start_date = #{realStartDate,jdbcType=TIMESTAMP},
      </if>
      <if test="realEndDate != null">
        real_end_date = #{realEndDate,jdbcType=TIMESTAMP},
      </if>
      <if test="realDays != null">
        real_days = #{realDays,jdbcType=REAL},
      </if>
      <if test="totalPersonDays != null">
        total_person_days = #{totalPersonDays,jdbcType=REAL},
      </if>
      <if test="invalid != null">
        invalid = #{invalid,jdbcType=TINYINT},
      </if>
      <if test="createdBy != null">
        created_by = #{createdBy,jdbcType=BIGINT},
      </if>
      <if test="updatedBy != null">
        updated_by = #{updatedBy,jdbcType=BIGINT},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModified != null">
        gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="planDesc != null">
        plan_desc = #{planDesc,jdbcType=LONGVARCHAR},
      </if>
      <if test="executionProgress != null">
        execution_progress = #{executionProgress,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.dt.train.management.center.model.dao.TrainCourseDOWithBLOBs">
    update train_course
    set project_id = #{projectId,jdbcType=INTEGER},
      project_name = #{projectName,jdbcType=VARCHAR},
      project_code = #{projectCode,jdbcType=VARCHAR},
      course_name = #{courseName,jdbcType=VARCHAR},
      course_status = #{courseStatus,jdbcType=TINYINT},
      course_form = #{courseForm,jdbcType=TINYINT},
      course_type = #{courseType,jdbcType=TINYINT},
      course_people_num = #{coursePeopleNum,jdbcType=INTEGER},
      service_object = #{serviceObject,jdbcType=VARCHAR},
      is_remote = #{isRemote,jdbcType=TINYINT},
      course_area = #{courseArea,jdbcType=VARCHAR},
      course_area_id = #{courseAreaId,jdbcType=INTEGER},
      has_plan_desc = #{hasPlanDesc,jdbcType=BIT},
      has_execution_progress = #{hasExecutionProgress,jdbcType=BIT},
      plan_start_date = #{planStartDate,jdbcType=TIMESTAMP},
      plan_end_date = #{planEndDate,jdbcType=TIMESTAMP},
      plan_days = #{planDays,jdbcType=REAL},
      real_start_date = #{realStartDate,jdbcType=TIMESTAMP},
      real_end_date = #{realEndDate,jdbcType=TIMESTAMP},
      real_days = #{realDays,jdbcType=REAL},
      total_person_days = #{totalPersonDays,jdbcType=REAL},
      invalid = #{invalid,jdbcType=TINYINT},
      created_by = #{createdBy,jdbcType=BIGINT},
      updated_by = #{updatedBy,jdbcType=BIGINT},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
      plan_desc = #{planDesc,jdbcType=LONGVARCHAR},
      execution_progress = #{executionProgress,jdbcType=LONGVARCHAR}
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.dt.train.management.center.model.dao.TrainCourseDO">
    update train_course
    set project_id = #{projectId,jdbcType=INTEGER},
      project_name = #{projectName,jdbcType=VARCHAR},
      project_code = #{projectCode,jdbcType=VARCHAR},
      course_name = #{courseName,jdbcType=VARCHAR},
      course_status = #{courseStatus,jdbcType=TINYINT},
      course_form = #{courseForm,jdbcType=TINYINT},
      course_type = #{courseType,jdbcType=TINYINT},
      course_people_num = #{coursePeopleNum,jdbcType=INTEGER},
      service_object = #{serviceObject,jdbcType=VARCHAR},
      is_remote = #{isRemote,jdbcType=TINYINT},
      course_area = #{courseArea,jdbcType=VARCHAR},
      course_area_id = #{courseAreaId,jdbcType=INTEGER},
      has_plan_desc = #{hasPlanDesc,jdbcType=BIT},
      has_execution_progress = #{hasExecutionProgress,jdbcType=BIT},
      plan_start_date = #{planStartDate,jdbcType=TIMESTAMP},
      plan_end_date = #{planEndDate,jdbcType=TIMESTAMP},
      plan_days = #{planDays,jdbcType=REAL},
      real_start_date = #{realStartDate,jdbcType=TIMESTAMP},
      real_end_date = #{realEndDate,jdbcType=TIMESTAMP},
      real_days = #{realDays,jdbcType=REAL},
      total_person_days = #{totalPersonDays,jdbcType=REAL},
      invalid = #{invalid,jdbcType=TINYINT},
      created_by = #{createdBy,jdbcType=BIGINT},
      updated_by = #{updatedBy,jdbcType=BIGINT},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_modified = #{gmtModified,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>