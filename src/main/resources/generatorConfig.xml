<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE generatorConfiguration
        PUBLIC "-//mybatis.org//DTD MyBatis Generator Configuration 1.0//EN"
        "http://mybatis.org/dtd/mybatis-generator-config_1_0.dtd">
<generatorConfiguration>

    <context id="ctx" targetRuntime="MyBatis3">
        <!-- 生成的Java文件的编码 -->
        <property name="javaFileEncoding" value="UTF-8"/>

        <!--<context id="ctx" targetRuntime="MyBatis3" >defaultModelType="conditional">-->
        <!--<plugin type="plugin.SelectByPagePlugin"/>-->
        <!--此处是将Example改名为Criteria 当然 想改成什么都行~ -->
        <plugin type="org.mybatis.generator.plugins.RenameExampleClassPlugin">
            <property name="searchString" value="Example"/>
            <property name="replaceString" value="Criteria"/>
        </plugin>

        <!-- 使用lombok插件@Data注解, 因此忽略生成get, set方法 -->
        <plugin type="com.dt.mybatis.generator.config.v1.IgnoreSetterAndGetterPlugin"/>

        <!-- 定制代码生成器注解 -->
        <commentGenerator type="com.dt.mybatis.generator.config.v1.CustomCommentGenerator"/>

        <jdbcConnection driverClass="com.mysql.jdbc.Driver"
                        connectionURL="***********************************************************************************************************"
                        userId="srt_dt" password="t81Qxr2EZ8ws" />

        <javaTypeResolver>
            <property name="forceBigDecimals" value="true"/>
        </javaTypeResolver>

        <!-- targetProject:自动生成代码的位置 -->
        <javaModelGenerator targetPackage="com.dt.train.management.center.model.dao" targetProject="src/main/java">
            <property name="enableSubPackages" value="true"/>
            <property name="trimStrings" value="true"/>
        </javaModelGenerator>

        <sqlMapGenerator targetPackage="mapper" targetProject="src/main/resources">
            <property name="enableSubPackages" value="true"/>
        </sqlMapGenerator>

        <javaClientGenerator type="XMLMAPPER" targetPackage="com.dt.train.management.center.mapper"
                             targetProject="src/main/java">
            <property name="enableSubPackages" value="true"/>
        </javaClientGenerator>

        <table tableName="train_project" domainObjectName="TrainProjectDO"
               enableCountByExample="false"
               enableUpdateByExample="false"
               enableDeleteByExample="false"
               enableSelectByExample="false"
               selectByExampleQueryId="false">
            <generatedKey column="id" sqlStatement="MySql" identity="true"/>
            <columnOverride column="role_status" javaType="java.lang.Integer" jdbcType="TINYINT" />
            <columnOverride column="project_type" javaType="java.lang.Integer" jdbcType="TINYINT" />
            <columnOverride column="project_level" javaType="java.lang.Integer" jdbcType="TINYINT" />
            <columnOverride column="can_select" javaType="java.lang.Integer" jdbcType="TINYINT" />
            <columnOverride column="project_status" javaType="java.lang.Integer" jdbcType="TINYINT" />
            <columnOverride column="action_type" javaType="java.lang.Integer" jdbcType="TINYINT" />
            <columnOverride column="course_form" javaType="java.lang.Integer" jdbcType="TINYINT" />
            <columnOverride column="schedule_status" javaType="java.lang.Integer" jdbcType="TINYINT" />
            <columnOverride column="sync_type" javaType="java.lang.Integer" jdbcType="TINYINT" />
            <columnOverride column="user_status" javaType="java.lang.Integer" jdbcType="TINYINT" />
            <columnOverride column="task_type" javaType="java.lang.Integer" jdbcType="TINYINT" />
            <columnOverride column="check_dimension" javaType="java.lang.Integer" jdbcType="TINYINT" />
            <columnOverride column="check_in_status" javaType="java.lang.Integer" jdbcType="TINYINT" />
            <columnOverride column="check_on_site_status" javaType="java.lang.Integer" jdbcType="TINYINT" />
            <columnOverride column="check_status" javaType="java.lang.Integer" jdbcType="TINYINT" />
            <columnOverride column="check_out_status" javaType="java.lang.Integer" jdbcType="TINYINT" />
            <columnOverride column="invalid" javaType="java.lang.Integer" jdbcType="TINYINT" />
            <columnOverride column="check_type" javaType="java.lang.Integer" jdbcType="TINYINT" />
            <columnOverride column="course_status" javaType="java.lang.Integer" jdbcType="TINYINT" />
            <columnOverride column="course_type" javaType="java.lang.Integer" jdbcType="TINYINT" />
            <columnOverride column="is_remote" javaType="java.lang.Integer" jdbcType="TINYINT" />
            <!-- ts字段为记录最后更新时间, 由系统自动更新, 业务代码不使用. 主要用于大数据集市抽数. 因此代码生成器忽略该字段 -->
            <ignoreColumn column="ts"/>
        </table>
    </context>
</generatorConfiguration>