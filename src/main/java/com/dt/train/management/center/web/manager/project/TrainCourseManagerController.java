package com.dt.train.management.center.web.manager.project;

import java.util.Date;

import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.dt.framework.business.dto.web.common.WebResult;
import com.dt.train.management.center.enums.ManagementExceptionEnum;
import com.dt.train.management.center.enums.ProjectStatusEnum;
import com.dt.train.management.center.manager.ScheduleManager;
import com.dt.train.management.center.model.dto.project.TrainCourseSaveOrUpdateDTO;
import com.dt.train.management.center.model.vo.project.ScheduleCalendarVO;
import com.dt.train.management.center.model.vo.project.TrainCourseVO;
import com.dt.train.management.center.model.vo.project.TrainProjectVO;
import com.dt.train.management.center.service.project.TrainCourseService;
import com.dt.train.management.center.service.project.TrainProjectService;
import com.dt.train.management.center.utils.ExceptionUtil;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;

@RestController
@RequestMapping("/manager/course")
@Api(tags = "班次管理")
public class TrainCourseManagerController {

    private final TrainCourseService trainCourseService;

    private final TrainProjectService trainProjectService;

    private final ScheduleManager scheduleManager;

    public TrainCourseManagerController(TrainCourseService trainCourseService, TrainProjectService trainProjectService, ScheduleManager scheduleManager) {
        this.trainCourseService = trainCourseService;
        this.trainProjectService = trainProjectService;
        this.scheduleManager = scheduleManager;
    }

    @PostMapping
    @ApiOperation("保存或更新班次")
    @Transactional(rollbackFor = Exception.class)
    public WebResult<TrainCourseVO> saveOrUpdate(@Validated @RequestBody TrainCourseSaveOrUpdateDTO trainCourseDTO) {
        trainCourseService.preCheckSaveOrUpdate(trainCourseDTO);
        TrainProjectVO trainProjectVO = trainProjectService.updateScheduleStatus(trainCourseDTO.getProjectId(), 1);
        if(trainCourseDTO.getId() == null){
            if(trainProjectVO.getProjectStatus() == ProjectStatusEnum.COMPLETED.getValue()) {
                throw ExceptionUtil.businessException(ManagementExceptionEnum.PROJECT_ALREADY_COMPLETED);
            }
        }
        if(trainCourseDTO.getExecuteUsers() != null){
            trainCourseDTO.getExecuteUsers().forEach(user -> {
                user.setProjectAreaId(trainProjectVO.getProjectAreaId());
                user.setCourseAreaId(trainCourseDTO.getCourseAreaId());
            });
        }
        if(trainCourseDTO.getSupportUsers() != null){
            trainCourseDTO.getSupportUsers().forEach(user -> {
                user.setProjectAreaId(trainProjectVO.getProjectAreaId());
                user.setCourseAreaId(trainCourseDTO.getCourseAreaId());
            });
        }
        trainCourseDTO.setProjectName(trainProjectVO.getProjectName());
        trainCourseDTO.setProjectCode(trainProjectVO.getProjectCode());
        TrainCourseVO trainCourseVO = trainCourseService.saveOrUpdateTrainCourse(trainCourseDTO);
        // trainProjectService.updateIsRemote(trainCourseDTO.getProjectId());
        return WebResult.successData(trainCourseVO);
    }

    @DeleteMapping
    @ApiOperation("删除班次")
    @Transactional(rollbackFor = Exception.class)
    public WebResult<Void> delete(@ApiParam(value = "班次ID") @RequestParam Integer courseId) {
        TrainCourseVO trainCourseVO = trainCourseService.preCheckDelete(courseId);
        trainCourseService.delete(courseId);
        trainProjectService.updateScheduleStatus(trainCourseVO.getProjectId());
        // trainProjectService.updateIsRemote(trainCourseVO.getProjectId());
        return WebResult.success();
    }
    
    @GetMapping("/calendar")
    @ApiOperation("获取排班日历")
    public WebResult<ScheduleCalendarVO> getCalendar(@ApiParam(value = "开始时间") @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") Date startDate
    , @ApiParam(value = "结束时间") @RequestParam  @DateTimeFormat(pattern = "yyyy-MM-dd") Date endDate) {
        ScheduleCalendarVO scheduleCalendarVO = scheduleManager.getCalendar(startDate, endDate);
        return WebResult.successData(scheduleCalendarVO);
    }

}
