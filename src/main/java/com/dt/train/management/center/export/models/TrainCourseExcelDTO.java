package com.dt.train.management.center.export.models;

import java.util.Date;

import org.apache.poi.ss.usermodel.FillPatternType;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.HeadStyle;

import io.swagger.annotations.ApiModelProperty;

import com.alibaba.excel.annotation.write.style.HeadFontStyle;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import com.alibaba.excel.annotation.write.style.ColumnWidth;

import lombok.Data;

@Data
@HeadRowHeight(30)
@HeadStyle(fillPatternType = FillPatternType.SOLID_FOREGROUND, fillForegroundColor = 49)
@HeadFontStyle(color = 9, fontHeightInPoints=11)
public class TrainCourseExcelDTO {

    @ExcelProperty("班次id")
    @ColumnWidth(10)
    private Integer id;

    @ExcelProperty("班次名称")
    @ColumnWidth(15)
    private String courseName;

    @ExcelProperty("班次状态")
    @ColumnWidth(15)
    private String courseStatus; // 将 Integer 转换为 String 便于显示中文

    @ExcelProperty("所属项目编码")
    @ColumnWidth(15)
    private String projectCode;

    @ExcelProperty("所属项目名称") 
    @ColumnWidth(15)
    private String projectName;

    @ExcelProperty("班次形式")
    @ColumnWidth(15)
    private String courseForm;

    @ExcelProperty("主题类型")
    @ColumnWidth(15)
    private String courseTypeName;

    @ExcelProperty("班次人数")
    @ColumnWidth(15)
    private Integer coursePeopleNum;

    @ExcelProperty("服务对象")
    @ColumnWidth(15)
    private String serviceObject;

    @ExcelProperty("计划说明文字")
    @ColumnWidth(20)
    private String planDescTxt;

    @ExcelProperty("计划说明图片地址")
    @ColumnWidth(20)
    private String planDescImage;

    @ExcelProperty("计划日期")
    @ColumnWidth(20)
    private String contractDate;  //计划开始时间 - 计划结束时间

    @ExcelProperty("计划天数")
    @ColumnWidth(15)
    private Float contractDays;

    @ExcelProperty("是否异地")
    @HeadStyle(fillPatternType = FillPatternType.SOLID_FOREGROUND, fillForegroundColor = 30)
    @ColumnWidth(15)
    private String isRemote; // 将 Integer 转换为 String 便于显示中文

    @ExcelProperty("执行区域")
    @HeadStyle(fillPatternType = FillPatternType.SOLID_FOREGROUND, fillForegroundColor = 30)
    @ColumnWidth(15)
    private String courseArea;

    @ExcelProperty("执行地点")
    @HeadStyle(fillPatternType = FillPatternType.SOLID_FOREGROUND, fillForegroundColor = 30)
    @ColumnWidth(20)
    private String courseProvinceCity;

    @ExcelProperty("实际执行起止日期")
    @HeadStyle(fillPatternType = FillPatternType.SOLID_FOREGROUND, fillForegroundColor = 30)
    @ColumnWidth(25)
    private String realDate; // 实际执行起止日期

    @ExcelProperty("实际执行天数")
    @HeadStyle(fillPatternType = FillPatternType.SOLID_FOREGROUND, fillForegroundColor = 30)
    @ColumnWidth(15)
    private Float realDays;

    @ExcelProperty("执行人")
    @HeadStyle(fillPatternType = FillPatternType.SOLID_FOREGROUND, fillForegroundColor = 30)
    @ColumnWidth(20)
    private String executors; // 执行人姓名列表
    @ExcelProperty("支援人")
    @HeadStyle(fillPatternType = FillPatternType.SOLID_FOREGROUND, fillForegroundColor = 30)
    @ColumnWidth(20)
    private String supporters; // 支援人姓名列表
    @ExcelProperty("排班人数")
    @HeadStyle(fillPatternType = FillPatternType.SOLID_FOREGROUND, fillForegroundColor = 30)
    @ColumnWidth(15)
    private Integer scheduledPersonCount; // 排班人数
    @ExcelProperty("排班人天")
    @HeadStyle(fillPatternType = FillPatternType.SOLID_FOREGROUND, fillForegroundColor = 30)
    @ColumnWidth(15)
    private Float courseManDay; // 班次人天
    @ExcelProperty("执行进展说明文字")
    @HeadStyle(fillPatternType = FillPatternType.SOLID_FOREGROUND, fillForegroundColor = 30)
    @ColumnWidth(25)
    private String progressDescTxt; // 执行进展说明文字
    @ExcelProperty("执行进展说明图片地址")
    @HeadStyle(fillPatternType = FillPatternType.SOLID_FOREGROUND, fillForegroundColor = 30)
    @ColumnWidth(25)
    private String progressDescImage; // 执行进展说明图片地址

    @ExcelProperty("班次创建时间")
    @HeadStyle(fillPatternType = FillPatternType.SOLID_FOREGROUND, fillForegroundColor = 30)
    @ColumnWidth(20)
    private String gmtCreate;
}
