package com.dt.train.management.center.model.dao;

import java.util.Date;

/**
 * 
 * 表名: train_project
 *
 * @mbg.generated
 */
@lombok.Data
public class TrainProjectDO {
    /**
     * 字段描述: 主键ID
     *
     * 字段名: train_project.id
     *
     * @mbg.generated
     */
    private Integer id;

    /**
     * 字段描述: CRM项目ID
     *
     * 字段名: train_project.crm_project_id
     *
     * @mbg.generated
     */
    private String crmProjectId;

    /**
     * 字段描述: 项目名称
     *
     * 字段名: train_project.project_name
     *
     * @mbg.generated
     */
    private String projectName;

    /**
     * 字段描述: 项目编码
     *
     * 字段名: train_project.project_code
     *
     * @mbg.generated
     */
    private String projectCode;

    /**
     * 字段描述: 项目所属区域
     *
     * 字段名: train_project.project_area
     *
     * @mbg.generated
     */
    private String projectArea;

    /**
     * 字段描述: 项目所属区域id
     *
     * 字段名: train_project.project_area_id
     *
     * @mbg.generated
     */
    private Integer projectAreaId;

    /**
     * 字段描述: 省份id
     *
     * 字段名: train_project.province_id
     *
     * @mbg.generated
     */
    private String provinceId;

    /**
     * 字段描述: 城市id
     *
     * 字段名: train_project.city_id
     *
     * @mbg.generated
     */
    private String cityId;

    /**
     * 字段描述: 地区id
     *
     * 字段名: train_project.area_id
     *
     * @mbg.generated
     */
    private String areaId;

    /**
     * 字段描述: 省份
     *
     * 字段名: train_project.province
     *
     * @mbg.generated
     */
    private String province;

    /**
     * 字段描述: 城市
     *
     * 字段名: train_project.city
     *
     * @mbg.generated
     */
    private String city;

    /**
     * 字段描述: 区县
     *
     * 字段名: train_project.area
     *
     * @mbg.generated
     */
    private String area;

    /**
     * 字段描述: 立项类型,0-正式立项，1-预立项
     *
     * 字段名: train_project.project_type
     *
     * @mbg.generated
     */
    private Integer projectType;

    /**
     * 字段描述: 项目级别,0-国家，1-省，2-市，3-区县，3-院校，4-学校，5-全国统招
     *
     * 字段名: train_project.project_level
     *
     * @mbg.generated
     */
    private Integer projectLevel;

    /**
     * 字段描述: 项目开始时间
     *
     * 字段名: train_project.start_date
     *
     * @mbg.generated
     */
    private Date startDate;

    /**
     * 字段描述: 项目结束时间
     *
     * 字段名: train_project.end_date
     *
     * @mbg.generated
     */
    private Date endDate;

    /**
     * 字段描述: 项目状态,0-未开始，1-进行中，2-已完成
     *
     * 字段名: train_project.project_status
     *
     * @mbg.generated
     */
    private Integer projectStatus;

    /**
     * 字段描述: 排期状态,0-未排期，1-已排期
     *
     * 字段名: train_project.schedule_status
     *
     * @mbg.generated
     */
    private Integer scheduleStatus;

    /**
     * 字段描述: 是否异地，0-否，1-是
     *
     * 字段名: train_project.is_remote
     *
     * @mbg.generated
     */
    private Integer isRemote;

    /**
     * 字段描述: 执行类型，0-一次集中，1-分段实施
     *
     * 字段名: train_project.action_type
     *
     * @mbg.generated
     */
    private Integer actionType;

    /**
     * 字段描述: 执行对接人姓名
     *
     * 字段名: train_project.execute_contact_name
     *
     * @mbg.generated
     */
    private String executeContactName;

    /**
     * 字段描述: 执行对接人电话
     *
     * 字段名: train_project.execute_contact_phone
     *
     * @mbg.generated
     */
    private String executeContactPhone;

    /**
     * 字段描述: 执行对接人职务
     *
     * 字段名: train_project.execute_contact_job
     *
     * @mbg.generated
     */
    private String executeContactJob;

    /**
     * 字段描述: 项目总学时
     *
     * 字段名: train_project.total_hours
     *
     * @mbg.generated
     */
    private String totalHours;

    /**
     * 字段描述: 执行总人数
     *
     * 字段名: train_project.total_user_count
     *
     * @mbg.generated
     */
    private Integer totalUserCount;

    /**
     * 字段描述: 项目经理id
     *
     * 字段名: train_project.project_manager_id
     *
     * @mbg.generated
     */
    private String projectManagerId;

    /**
     * 字段描述: 项目经理名称
     *
     * 字段名: train_project.project_manager_name
     *
     * @mbg.generated
     */
    private String projectManagerName;

    /**
     * 字段描述: 项目编辑id
     *
     * 字段名: train_project.project_editor_id
     *
     * @mbg.generated
     */
    private String projectEditorId;

    /**
     * 字段描述: 责任编辑名称
     *
     * 字段名: train_project.project_editor_name
     *
     * @mbg.generated
     */
    private String projectEditorName;

    /**
     * 字段描述: 服务对象
     *
     * 字段名: train_project.service_object
     *
     * @mbg.generated
     */
    private String serviceObject;

    /**
     * 字段描述: 服务内容
     *
     * 字段名: train_project.service_content
     *
     * @mbg.generated
     */
    private String serviceContent;

    /**
     * 字段描述: 销售人员id
     *
     * 字段名: train_project.sales_user_id
     *
     * @mbg.generated
     */
    private Long salesUserId;

    /**
     * 字段描述: 销售人员名称
     *
     * 字段名: train_project.sales_user_name
     *
     * @mbg.generated
     */
    private String salesUserName;

    /**
     * 字段描述: 省区销售经理id
     *
     * 字段名: train_project.province_sales_manager_id
     *
     * @mbg.generated
     */
    private Long provinceSalesManagerId;

    /**
     * 字段描述: 省区销售经理名称
     *
     * 字段名: train_project.province_sales_manager_name
     *
     * @mbg.generated
     */
    private String provinceSalesManagerName;

    /**
     * 字段描述: 区域销售总监id
     *
     * 字段名: train_project.region_sales_director_id
     *
     * @mbg.generated
     */
    private Long regionSalesDirectorId;

    /**
     * 字段描述: 区域销售总监名称
     *
     * 字段名: train_project.region_sales_director_name
     *
     * @mbg.generated
     */
    private String regionSalesDirectorName;

    /**
     * 字段描述: 业务类型
     *
     * 字段名: train_project.business_type
     *
     * @mbg.generated
     */
    private String businessType;

    /**
     * 字段描述: 项目模式
     *
     * 字段名: train_project.project_mode
     *
     * @mbg.generated
     */
    private String projectMode;

    /**
     * 字段描述: 项目子模式
     *
     * 字段名: train_project.project_sub_mode
     *
     * @mbg.generated
     */
    private String projectSubMode;

    /**
     * 字段描述: 客户单位名称
     *
     * 字段名: train_project.customer_name
     *
     * @mbg.generated
     */
    private String customerName;

    /**
     * 字段描述: 同步方式,0-自动，1-手动
     *
     * 字段名: train_project.sync_type
     *
     * @mbg.generated
     */
    private Integer syncType;

    /**
     * 字段描述: 面授总天数
     *
     * 字段名: train_project.teach_days
     *
     * @mbg.generated
     */
    private Float teachDays;

    /**
     * 字段描述: 面授班次
     *
     * 字段名: train_project.teach_course_count
     *
     * @mbg.generated
     */
    private Integer teachCourseCount;

    /**
     * 字段描述: 面授备注说明
     *
     * 字段名: train_project.teach_remark
     *
     * @mbg.generated
     */
    private String teachRemark;

    /**
     * 字段描述: 项目协助人,多个人;分隔
     *
     * 字段名: train_project.project_helper_id
     *
     * @mbg.generated
     */
    private String projectHelperId;

    /**
     * 字段描述: 项目协助人名称,多个人;分隔
     *
     * 字段名: train_project.project_helper_name
     *
     * @mbg.generated
     */
    private String projectHelperName;

    /**
     * 字段描述: 学支编辑,多个人;分隔
     *
     * 字段名: train_project.live_editor_id
     *
     * @mbg.generated
     */
    private String liveEditorId;

    /**
     * 字段描述: 学支编辑名称,多个人;分隔
     *
     * 字段名: train_project.live_editor_name
     *
     * @mbg.generated
     */
    private String liveEditorName;

    /**
     * 字段描述: 最后同步crm时间
     *
     * 字段名: train_project.sync_time
     *
     * @mbg.generated
     */
    private Date syncTime;

    /**
     * 字段描述: 是否已删除 0 否 1 是
     *
     * 字段名: train_project.invalid
     *
     * @mbg.generated
     */
    private Integer invalid;

    /**
     * 字段描述: 创建人
     *
     * 字段名: train_project.created_by
     *
     * @mbg.generated
     */
    private Long createdBy;

    /**
     * 字段描述: 更新人
     *
     * 字段名: train_project.updated_by
     *
     * @mbg.generated
     */
    private Long updatedBy;

    /**
     * 字段描述: 创建时间
     *
     * 字段名: train_project.gmt_create
     *
     * @mbg.generated
     */
    private Date gmtCreate;

    /**
     * 字段描述: 修改时间
     *
     * 字段名: train_project.gmt_modified
     *
     * @mbg.generated
     */
    private Date gmtModified;
}