package com.dt.train.management.center.service.user;

import java.util.List;
import org.springframework.stereotype.Service;

import com.dt.framework.business.dto.page.Pagination;
import com.dt.train.management.center.enums.RightLevelEnum;
import com.dt.train.management.center.model.dao.TrainUserDO;
import com.dt.train.management.center.model.dto.user.TrainUserQueryDTO;
import com.dt.train.management.center.model.vo.user.TrainUserVO;

@Service
public interface TrainUserService {

    /**
     * 获取用户权限等级
     * @param userId 用户id
     * @param userDeptIdsStr 用户部门id
     * @param deptId 部门id
     * @return 权限等级
     */
    RightLevelEnum getRightLevel(Long userId, String userDeptIdsStr, Integer deptId);

    /**
     * 检查用户是否有当前部门权限
     *
     * @param userId 用户id
     * @param deptId 部门id
     */
    void checkUserAndDepartment(Long userId,String deptId);

    Pagination<TrainUserVO> pageQuery(TrainUserQueryDTO query);

    List<TrainUserVO> getByQuery(TrainUserQueryDTO query);

    TrainUserVO getUserInfoByUserId(Long userId);

    TrainUserVO getUserInfoByCrmUserId(String crmUserId);

    Integer countUsersByRoleId(Integer roleId);

    /**
     * 修改用户状态（启用/停用）
     * @param userDO 用户对象，需包含id和userStatus
     */
    void updateUserStatus(TrainUserDO userDO);
    
    /**
     * 获取所有离职用户ID列表
     * @return 离职用户ID列表
     */
    List<Long> getResignedUserIds();
}
