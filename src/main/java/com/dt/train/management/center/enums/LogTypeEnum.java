package com.dt.train.management.center.enums;

import java.util.Arrays;

import lombok.Getter;

@Getter
public enum LogTypeEnum {

    AUDIT_LOG("audit_log", "审计日志");

    private String code;

    private String name;

    LogTypeEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    /**
     * 根据code获取枚举
     * @param code
     * @return
     */
    public static LogTypeEnum getByCode(String code) {
        return Arrays.stream(LogTypeEnum.values()).filter(logTypeEnum -> logTypeEnum.getCode().equals(code)).findFirst().orElse(null);
    }
}
