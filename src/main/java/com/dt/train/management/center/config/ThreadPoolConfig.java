package com.dt.train.management.center.config;

import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadFactory;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import com.google.common.util.concurrent.ThreadFactoryBuilder;

import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

@Slf4j
@Configuration
public class ThreadPoolConfig {

    @Bean
    public ThreadPoolExecutor projectSyncExecutor() {
        ThreadFactory threadFactory = new ThreadFactoryBuilder()
            .setNameFormat("project-sync-%d") // 设置线程名前缀
            .build();

        ThreadPoolExecutor executor = new ThreadPoolExecutor(
            2, 2, 0, TimeUnit.MILLISECONDS, 
            new LinkedBlockingQueue<>(100),
            threadFactory // 传入自定义的 ThreadFactory
        );
        return executor;
    }

    @Bean("toolTextReadExecutor")
    public ThreadPoolTaskExecutor getAsyncExecutor() {

        ThreadPoolTaskExecutor threadPoolTaskExecutor = new ThreadPoolTaskExecutor();
        threadPoolTaskExecutor.setThreadNamePrefix("async-toolTextRead-");
        //线程池核心线程数
        threadPoolTaskExecutor.setCorePoolSize(8);
        //线程池最大线程数 等待队列满之后开启
        threadPoolTaskExecutor.setMaxPoolSize(16);
        //队列数，默认 Integer.MAX 大于0为LinkedBlockingQueue 小于等于0为 SynchronousQueue
        threadPoolTaskExecutor.setQueueCapacity(10000);
        //达到最大线程数后续任务丢弃
        threadPoolTaskExecutor.setRejectedExecutionHandler(new ThreadPoolExecutor.AbortPolicy());
        //开启核心线程数支持空闲释放
        threadPoolTaskExecutor.setAllowCoreThreadTimeOut(false);
        //设置等待任务完成后再关闭
        threadPoolTaskExecutor.setWaitForTasksToCompleteOnShutdown(true);
        return threadPoolTaskExecutor;
    }
}
