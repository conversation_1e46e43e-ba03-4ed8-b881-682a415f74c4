package com.dt.train.management.center.export.models;

import java.util.Map;

import com.dt.train.management.center.export.enums.ReportFormat;
import com.dt.train.management.center.export.enums.ReportType;

import lombok.Data;

@Data
public class ReportRequest {

    private ReportType reportType;
    private ReportFormat format;
    private Map<String, Object> parameters; // 动态参数，如 startDate, endDate, customerId 等

    // 构造函数
    public ReportRequest() {}

    public ReportRequest(ReportType reportType, ReportFormat format, Map<String, Object> parameters) {
        this.reportType = reportType;
        this.format = format;
        this.parameters = parameters;
    }
}