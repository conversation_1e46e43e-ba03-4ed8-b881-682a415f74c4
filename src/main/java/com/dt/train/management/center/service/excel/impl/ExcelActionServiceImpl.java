package com.dt.train.management.center.service.excel.impl;

import com.alibaba.fastjson.JSON;
import com.dt.train.management.center.feign.client.UserCenterFeignClient;
import com.dt.train.management.center.feign.param.RegionSearchParam;
import com.dt.train.management.center.feign.result.RegionDTO;
import com.dt.train.management.center.mapper.DictMappingDOMapper;
import com.dt.train.management.center.model.dao.DictMappingDO;
import com.dt.train.management.center.model.dao.TrainDeptDO;
import com.dt.train.management.center.model.dto.tools.RegionUploadDataDTO;
import com.dt.train.management.center.service.excel.ExcelActionService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
public class ExcelActionServiceImpl implements ExcelActionService {
    @Resource
    private DictMappingDOMapper dictMappingDOMapper;
    @Resource
    private UserCenterFeignClient userCenterFeignClient;
    /**
     * 处理excel上传数据
     * @param uploadDataDTOList 上传数据
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void regionHandler(List<RegionUploadDataDTO> uploadDataDTOList, Boolean isUpdate) {
        log.info("开始处理excel上传数据，总数：{}", uploadDataDTOList.size());
        if (CollectionUtils.isEmpty(uploadDataDTOList)) {
            return;
        }
        if (!isUpdate) {
            insert(uploadDataDTOList);
        } else {
            log.info("先删除错误的再新增");
            updateUploadDataDTOList(uploadDataDTOList);
        }

    }

    private void updateUploadDataDTOList(List<RegionUploadDataDTO> uploadDataDTOList) {
        Set<String> nameSet = uploadDataDTOList.stream().map(RegionUploadDataDTO::getRegionName).collect(Collectors.toSet());
        dictMappingDOMapper.deleteByDictTypeAndDictValueIn("区", nameSet);
        for (RegionUploadDataDTO uploadDataDTO : uploadDataDTOList) {
            DictMappingDO dictMappingDO = new DictMappingDO();
            dictMappingDO.setDictCode(uploadDataDTO.getUserCenterID());
            dictMappingDO.setThirdDictCode(uploadDataDTO.getCrmID());
            dictMappingDO.setThirdSystem("FXK_CRM");
            dictMappingDO.setDictType(uploadDataDTO.getRegionType());
            dictMappingDO.setDictValue(uploadDataDTO.getRegionName());
            dictMappingDO.setDictOrder(0);
            dictMappingDO.setDictDesc(uploadDataDTO.getRegionName());
            dictMappingDO.setInvalid(0);
            dictMappingDO.setGmtCreate(new Date());
            dictMappingDO.setGmtModified(new Date());
            dictMappingDOMapper.insertSelective(dictMappingDO);
        }
    }

    private void insert(List<RegionUploadDataDTO> uploadDataDTOList) {
        RegionSearchParam regionSearchParam =  new RegionSearchParam();
        List<RegionDTO> regionDTOS = userCenterFeignClient.listRegions(regionSearchParam);
        log.info("查询到用户中心区域数据，总数：{}", regionDTOS.size());
        //过滤出1级 2级 3级
        List<RegionDTO> level1List = regionDTOS.stream().filter(regionDTO -> regionDTO.getLevel() == 1).collect(Collectors.toList());
        List<RegionDTO> level2List = regionDTOS.stream().filter(regionDTO -> regionDTO.getLevel() == 2).collect(Collectors.toList());
        List<RegionDTO> level3List = regionDTOS.stream().filter(regionDTO -> regionDTO.getLevel() == 3).collect(Collectors.toList());
        Map<String, RegionDTO> leve1Map = level1List.stream().collect(Collectors.toMap(RegionDTO::getGaodeAdcode, Function.identity(), (existing, replacement) -> replacement));
        Map<String, RegionDTO> leve2Map = level2List.stream().collect(Collectors.toMap(RegionDTO::getGaodeAdcode, Function.identity(), (existing, replacement) -> replacement));
        Map<String, RegionDTO> leve3Map = level3List.stream().collect(Collectors.toMap(RegionDTO::getGaodeAdcode, Function.identity(), (existing, replacement) -> replacement));
        int count = 1000;
        for (RegionUploadDataDTO uploadDataDTO : uploadDataDTOList) {
            // 取导入编码的前六位匹配用户中心高德编码
            String gaodeCode = uploadDataDTO.getGaodeCode();
            String gaodeAdcode = (gaodeCode != null && gaodeCode.length() >= 6)
                    ? gaodeCode.substring(0, 6)
                    : gaodeCode;
            RegionDTO userCenterRegion;
            switch (uploadDataDTO.getRegionType().trim()) {
                case "省":
                    log.info("省 处理区域类型：{}", uploadDataDTO.getRegionType());
                    userCenterRegion = leve1Map.get(gaodeAdcode);
                    break;
                case "市":
                    log.info("市 处理区域类型：{}", uploadDataDTO.getRegionType());
                    userCenterRegion = leve2Map.get(gaodeAdcode);
                    break;
                case "区":
                    log.info("区 处理区域类型：{}", uploadDataDTO.getRegionType());
                    userCenterRegion = leve3Map.get(gaodeAdcode);
                    break;
                default:
                    log.info("default 处理区域类型：{}", uploadDataDTO.getRegionType());
                    userCenterRegion = null;
                    break;
            }
            DictMappingDO dictMappingDO = new DictMappingDO();
            String noneDictCode = "not_exist_region_"+count;
            if(userCenterRegion!=null){
                dictMappingDO.setDictCode(userCenterRegion.getId().toString());
                dictMappingDO.setDictValue(userCenterRegion.getFullName());
                // 描述取用中心，值取CRM
                dictMappingDO.setDictDesc(uploadDataDTO.getRegionName());
                // 类型以CRM为准，因为用户中心不全
                dictMappingDO.setDictType(uploadDataDTO.getRegionType());
            }else{
                log.info("未找到gaodeCode:{},gaodeAdcode{}对应的用户中心区域，使用{}作为字典编码", gaodeCode,gaodeAdcode, noneDictCode);
                dictMappingDO.setDictCode(noneDictCode);
                dictMappingDO.setDictValue(uploadDataDTO.getRegionName());
                dictMappingDO.setDictDesc(uploadDataDTO.getRegionName());
                dictMappingDO.setDictType(uploadDataDTO.getRegionType());
                count++;
            }
         dictMappingDO.setThirdDictCode(uploadDataDTO.getCrmID());
         dictMappingDO.setThirdSystem("FXK_CRM");
         dictMappingDO.setDictOrder(0);
         dictMappingDO.setInvalid(0);
         dictMappingDO.setGmtCreate(new Date());
         dictMappingDO.setGmtModified(new Date());
         dictMappingDOMapper.insertSelective(dictMappingDO);
     }
    }
}
