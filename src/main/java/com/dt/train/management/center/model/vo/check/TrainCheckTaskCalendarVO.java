package com.dt.train.management.center.model.vo.check;

import java.time.LocalDate;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;

import io.swagger.annotations.ApiModelProperty;

/**
 * 
 * 表名: train_check_task
 *
 * @mbg.generated
 */
@lombok.Data
public class TrainCheckTaskCalendarVO {

    /**
     * 字段描述: 用户ID
     *
     * 字段名: train_check_task.user_id
     *
     * @mbg.generated
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long userId;

    /**
     * 字段描述: 打卡日期
     *
     * 字段名: train_check_task.check_date
     *
     * @mbg.generated
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate checkDate;

    /**
     * 字段描述: 打卡状态
     *
     * 字段名: train_check_task.check_status
     *
     * @mbg.generated
     */
    @ApiModelProperty(value = "打卡状态,-1-待打卡,0-正常，1-异常")
    private Integer checkStatus;
}