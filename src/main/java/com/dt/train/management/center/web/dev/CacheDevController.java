package com.dt.train.management.center.web.dev;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.dt.train.management.center.utils.RedisUtil;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;

@RestController
@RequestMapping("/dev/cache")
@Api(tags = "系统消息开发接口", hidden = true)
public class CacheDevController {

    private final RedisUtil redisUtil;

    public CacheDevController(RedisUtil redisUtil) {
        this.redisUtil = redisUtil;
    }

    @ApiOperation(value = "删除redis缓存")
    @GetMapping("/delete")
    public String deleteRedisKey(@ApiParam(value = "redis key", required = true) @RequestParam String key) {
        redisUtil.del(key);
        return "success";
    }

}
