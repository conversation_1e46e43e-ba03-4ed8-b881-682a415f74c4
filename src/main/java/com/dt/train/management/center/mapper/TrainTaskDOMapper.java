package com.dt.train.management.center.mapper;

import com.dt.train.management.center.model.dao.TrainTaskDO;

public interface TrainTaskDOMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(TrainTaskDO row);

    int insertSelective(TrainTaskDO row);

    TrainTaskDO selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(TrainTaskDO row);

    int updateByPrimaryKey(TrainTaskDO row);
}