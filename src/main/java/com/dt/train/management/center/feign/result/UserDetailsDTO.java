package com.dt.train.management.center.feign.result;

import com.alibaba.fastjson.JSONObject;
import lombok.Data;

/**
 * Spring Security OAuth2 的用户信息接口实现
 *
 * <AUTHOR>
 * @date 2018/8/8
 */
@Data
public class UserDetailsDTO {

    /**
     * 用户ID主键
     */
    private String userid;
    /**
     * 集团Token
     */
    private String jtToken;
    /**
     * 用户名
     */
    private String username;

    /**
     * 密码
     */
    private String pwd;

    /**
     * 微信小程序OpenID
     */
    private String openId;
    /**
     * 微信unionId
     *
     * @mbg.generated
     */
    private String wechatid;
    /**
     * 微信unionId
     *
     * @mbg.generated
     */
    private String wechatOpenid;

    private String shjyUserId;

    private String zjfzUserId;

    private String eduyunUserId;

    private String jtUserId;

    private String mobile;

    private String loginName;

    private String nickname;
    /**
     * 是否有效
     */
    private Boolean yn;

    /**
     * 短信验证码登录，新注册用户，首次登录标识
     */
    private Boolean isFirstLogin;

    private JSONObject verifyInfo;

    /**
     * 自动注册后审核状态 (例：贵州)
     */
    private String approveState;

    private String oauthBizUserId;

    public Boolean isEnabled() {
        return this.yn;
    }
}
