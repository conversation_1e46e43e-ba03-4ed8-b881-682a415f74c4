package com.dt.train.management.center.model.dto.project;

import java.util.Date;
import java.util.List;

import org.springframework.format.annotation.DateTimeFormat;

import com.dt.framework.business.util.page.PaginationParams;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class TrainProjectQueryDTO implements PaginationParams{

    /**
     * 项目名称
     */
    @ApiModelProperty(value = "项目名称/编号")
    private String projectNameOrCode;

    /**
     * 所属区域组织
     */
    @ApiModelProperty(value = "所属区域组织")
    private String projectAreaId;

    /**
     * 所属区域组织及子区域组织
     */
    @ApiModelProperty(value = "所属区域组织及子区域组织")
    private List<String> projectAreaIds;
    

    /**
     * 项目状态
     */
    @ApiModelProperty(value = "项目状态,0-未开始，1-进行中，2-已完成")
    private List<Integer> projectStatus;

    /**
     * 排期状态
     */
    @ApiModelProperty(value = "排期状态,0-未排期，1-已排期")
    private Integer scheduleStatus;

    /**
     * 是否异地
     */
    @ApiModelProperty(value = "是否异地,0-否，1-是")
    private Integer isRemote;
    

    /**
     * 项目开始时间
     */
    @ApiModelProperty(value = "项目开始时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startDate;

    /**
     * 项目结束时间
     */
    @ApiModelProperty(value = "项目结束时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endDate;
    
    /**
     * 项目级别
     */
    @ApiModelProperty(value = "项目级别")
    private String projectLevel;
    
    /**
     * 字段描述: 省份id
     *
     * 字段名: train_project.province_id
     *
     */
    @ApiModelProperty(value = "省份id") 
    private String provinceId;

    /**
     * 字段描述: 城市id
     *
     * 字段名: train_project.city_id
     *
     */
    @ApiModelProperty(value = "城市id") 
    private String cityId;

    /**
     * 字段描述: 地区id
     *
     * 字段名: train_project.area_id
     *
     */
    @ApiModelProperty(value = "地区id") 
    private String areaId;


    @ApiModelProperty(value = "立项类型") 
    private Integer projectType;

    @ApiModelProperty(value = "执行类型") 
    private Integer actionType;

    @ApiModelProperty(value = "研修模式") 
    private Integer projectMode;

    @ApiModelProperty(value = "研修模式描述",hidden = true) 
    private String projectModeDesc;

    @ApiModelProperty(value = "服务对象（支持模糊查询）")
    private String serviceObject;

    @ApiModelProperty(value = "服务内容（支持模糊查询）")
    private String serviceContent;

    @ApiModelProperty(value = "责任编辑") 
    private String projectEditor;

    @ApiModelProperty(value = "项目执行人/支援人id") 
    private Long courseRelationUserId;

    @ApiModelProperty(value = "同步方式,0-自动，1-手动") 
    private Integer syncType;

    @ApiModelProperty(value = "当前组织id") 
    private String currentDeptId;

    /**
     * 排除项目区域
     */
    @ApiModelProperty(value = "排除项目区域", hidden=true)
    private List<String> excludeProjectAreaIds;
    
    @ApiModelProperty(value = "页码") 
    private Integer pageIndex = 0;

    @ApiModelProperty(value = "每页条数") 
    private Integer pageSize = 10;

    @ApiModelProperty(value = "开始时间(用于查询指定时间范围开始的项目)")
    private Date startTime;

    @ApiModelProperty(value = "结束时间(用于查询指定时间范围开始的项目)")
    private Date endTime;
}
