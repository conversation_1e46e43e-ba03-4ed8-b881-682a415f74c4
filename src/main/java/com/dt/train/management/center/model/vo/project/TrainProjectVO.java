package com.dt.train.management.center.model.vo.project;

import java.util.Date;
import java.util.List;

import com.dt.train.management.center.model.vo.user.TrainUserVO;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class TrainProjectVO {

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private Integer id;

    @ApiModelProperty(value = "CRM项目ID")
    private String crmProjectId;

    /**
     * 项目名称
     */
    @ApiModelProperty(value = "项目名称")
    private String projectName;

    /**
     * 项目编码
     */
    @ApiModelProperty(value = "项目编码")
    private String projectCode;

    /**
     * 项目区域
     */
    @ApiModelProperty(value = "项目区域")
    private String projectArea;

    /**
     * 项目区域ID
     */
    @ApiModelProperty(value = "项目区域ID")
    private Integer projectAreaId;

    /**
     * 省份
     */
    @ApiModelProperty(value = "省份")
    private String province;

    /**
     * 城市
     */
    @ApiModelProperty(value = "城市")
    private String city;

    /**
     * 区域
     */
    @ApiModelProperty(value = "区域")
    private String area;

    /**
     * 项目区域名称
     */
    @ApiModelProperty(value = "省份id")
    private String provinceId;

    /**
     * 城市id
     */
    @ApiModelProperty(value = "城市id")
    private String cityId;

    /**
     * 区域id
     */
    @ApiModelProperty(value = "区域id")
    private String areaId;

    /**
     * 项目类型
     */
    @ApiModelProperty(value = "项目类型")
    private Integer projectType;

    /**
     * 项目类型名称
     */
    @ApiModelProperty(value = "项目类型名称")
    private String projectTypeDesc;

    /**
     * 项目级别
     */
    @ApiModelProperty(value = "项目级别")
    private Integer projectLevel;

    /**
     * 项目级别名称
     */
    @ApiModelProperty(value = "项目级别名称")
    private String projectLevelDesc;

    /**
     * 项目开始时间
     */
    @ApiModelProperty(value = "项目开始时间")
    private Date startDate;

    /**
     * 项目结束时间
     */
    @ApiModelProperty(value = "项目结束时间")
    private Date endDate;

    /**
     * 项目状态
     */
    @ApiModelProperty(value = "项目状态")
    private Integer projectStatus;

    @ApiModelProperty(value = "排班状态")
    private Integer scheduleStatus;

    /**
     * 是否异地
     */
    @ApiModelProperty(value = "是否异地")
    private Integer isRemote;

    /**
     * 执行类型
     */
    @ApiModelProperty(value = "执行类型,，0-一次集中，1-分段实施")
    private Integer actionType;

    @ApiModelProperty(value = "执行对接人姓名")
    private String executeContactName;

    
    @ApiModelProperty(value = "执行对接人电话")
    private String executeContactPhone;

    @ApiModelProperty(value = "执行对接人职务")
    private String executeContactJob;
    
    /**
     * 总课时
     */
    @ApiModelProperty(value = "总课时")
    private String totalHours;

    /**
     * 项目经理ID
     */
    @ApiModelProperty(value = "项目经理ID")
    private String projectManagerId;

    @ApiModelProperty(value = "项目经理部门ID")
    private String projectManagerDeptId;

    @ApiModelProperty(value = "项目经理状态，0-在职，1-离职")
    private Integer projectManagerStatus;

    /**
     * 项目经理名称
     */
    @ApiModelProperty(value = "项目经理名称")
    private String projectManagerName;

    /**
     * 项目编辑ID
     */
    @ApiModelProperty(value = "项目编辑ID")
    private String projectEditorId;

    @ApiModelProperty(value = "项目编辑部门ID")
    private String projectEditorDeptId;

    @ApiModelProperty(value = "项目编辑状态，0-在职，1-离职")
    private Integer projectEditorStatus;

    /**
     * 项目编辑名称
     */
    @ApiModelProperty(value = "项目编辑名称")
    private String projectEditorName;

    /**
     * 服务对象
     */
    @ApiModelProperty(value = "服务对象")
    private String serviceObject;

    /**
     * 服务内容
     */
    @ApiModelProperty(value = "服务内容")
    private String serviceContent;

    /**
     * 销售人员ID
     */
    @ApiModelProperty(value = "销售人员ID")
    private String salesUserId;

    /**
     * 销售人员名称
     */
    @ApiModelProperty(value = "销售人员名称")
    private String salesUserName;

    /**
     * 省销售经理ID
     */
    @ApiModelProperty(value = "省销售经理ID")
    private String provinceSalesManagerId;

    /**
     * 省销售经理名称
     */
    @ApiModelProperty(value = "省销售经理名称")
    private String provinceSalesManagerName;

    /**
     * 区域销售总监ID
     */
    @ApiModelProperty(value = "区域销售总监ID")
    private String regionSalesDirectorId;

    /**
     * 区域销售总监名称
     */
    @ApiModelProperty(value = "区域销售总监名称")
    private String regionSalesDirectorName;

    /**
     * 业务类型
     */
    @ApiModelProperty(value = "业务类型")
    private String businessType;

    @ApiModelProperty(value = "业务类型名称")
    private String businessTypeDesc;

    /**
     * 项目模式
     */
    @ApiModelProperty(value = "项目模式")
    private String projectMode;

    /**
     * 项目子模式
     */
    @ApiModelProperty(value = "项目子模式")
    private String projectSubMode;

    /**
     * 客户名称
     */
    @ApiModelProperty(value = "客户名称")
    private String customerName;

    /**
     * 执行总人数
     */
    @ApiModelProperty(value = "执行总人数")
    private Integer totalUserCount;


    /**
     * 字段描述: 面授总天数
     *
     * 字段名: train_project.teach_days
     *
     * @mbg.generated
     */
    private Float teachDays;

    /**
     * 字段描述: 面授班次
     *
     * 字段名: train_project.teach_course_count
     *
     * @mbg.generated
     */
    private Integer teachCourseCount;

    /**
     * 字段描述: 面授备注说明
     *
     * 字段名: train_project.teach_remark
     *
     * @mbg.generated
     */
    private String teachRemark;

    /**
     * 字段描述: 项目协助人
     *
     * 字段名: train_project.project_helper_id
     *
     * @mbg.generated
     */
    private List<TrainUserVO> projectHelpers;

    /**
     * 字段描述: 学支编辑名称
     *
     * 字段名: train_project.live_editor_name
     *
     * @mbg.generated
     */
    private List<TrainUserVO> liveEditors;

    /**
     * 字段描述: 最后同步crm时间
     *
     * 字段名: train_project.sync_time
     *
     * @mbg.generated
     */
    private Date syncTime;
    
}
