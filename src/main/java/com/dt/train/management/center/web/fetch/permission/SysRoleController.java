package com.dt.train.management.center.web.fetch.permission;

import java.util.List;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.dt.framework.business.dto.web.common.WebResult;
import com.dt.train.management.center.model.vo.permission.SysRoleVO;
import com.dt.train.management.center.model.vo.permission.SysRoleWithPermissionKeysVO;
import com.dt.train.management.center.service.permission.SysRoleService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;


/**
 * 角色管理控制器
 * 提供角色相关的API接口
 */
@RestController
@RequestMapping("/fetch/role")
@Api(tags = "角色相关接口")
public class SysRoleController {

    private final SysRoleService sysRoleService;

    public SysRoleController(SysRoleService sysRoleService) {
        this.sysRoleService = sysRoleService;
    }

    /**
     * 获取角色列表
     * @return 角色列表
     */
    @GetMapping("list")
    @ApiOperation(value = "获取角色列表", notes = "获取系统中所有角色的列表")
    public WebResult<List<SysRoleVO>> getRoleList() {
        return WebResult.successData(sysRoleService.listAllRoles());
    }

    /**
     * 获取角色权限key列表
     * @param roleId 角色ID
     * @return 角色权限key列表
     */
    @GetMapping("permissions")
    @ApiOperation(value = "获取角色权限key列表", notes = "根据角色ID获取角色的权限key列表")
    public WebResult<SysRoleWithPermissionKeysVO> getRolePermissions(
        @ApiParam(value = "角色ID", required = true) @RequestParam Integer roleId) {
        if (roleId == null || roleId <= 0) {
            return WebResult.error("角色ID不能为空或无效");
        }
        SysRoleWithPermissionKeysVO permissionTree = sysRoleService.getRolePermissions(roleId);
        return WebResult.successData(permissionTree);
    }
}
