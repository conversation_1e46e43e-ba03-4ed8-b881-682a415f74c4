package com.dt.train.management.center.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.dt.train.management.center.model.dao.SystemMessageDO;
import com.dt.train.management.center.model.dto.message.SystemMessageQueryDTO;

public interface SystemMessageDOMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(SystemMessageDO row);

    int insertSelective(SystemMessageDO row);

    SystemMessageDO selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(SystemMessageDO row);

    int updateByPrimaryKey(SystemMessageDO row);

    // 根据用户ID查询消息列表，按创建时间降序
    List<SystemMessageDO> query(SystemMessageQueryDTO queryDTO);
    
    // 统计用户的未读消息数量
    long count(SystemMessageQueryDTO queryDTO);
    
    // 将单条消息标记为已读
    int markAsRead(@Param("messageId") Long messageId);
    
    // 将某个用户的所有消息标记为已读
    int markAllAsReadByUserId(@Param("userId") Long userId);

    void insertBatch(@Param("systemMessageDOs") List<SystemMessageDO> systemMessageDOs);

    void deleteBatch(@Param("messageIds") List<Long> messageIds);
}