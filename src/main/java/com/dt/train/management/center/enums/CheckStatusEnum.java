package com.dt.train.management.center.enums;

import java.util.Arrays;

import lombok.Getter;

@Getter
public enum CheckStatusEnum {

    //打卡状态,0-未打卡，1-正常打卡，2-迟到打卡,3-早退打卡
    NOT_CHECKED(0, "未打卡"),
    NORMAL(1, "正常打卡"),
    LATE(2, "迟到打卡"),
    EARLY(3, "早退打卡");

    private Integer code;
    private String desc;
    
    CheckStatusEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static CheckStatusEnum fromCode(Integer code) {
        return Arrays.stream(CheckStatusEnum.values())
            .filter(status -> status.getCode().equals(code))
            .findFirst()
            .orElse(null);
    }

}
