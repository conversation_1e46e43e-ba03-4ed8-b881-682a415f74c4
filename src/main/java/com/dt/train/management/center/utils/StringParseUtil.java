package com.dt.train.management.center.utils;

import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

public class StringParseUtil {
    /**
     * 解析分号分隔的部门ID字符串
     *
     * @param deptIdsStr 格式如 "100;200;300"
     * @return 去重且非空的部门ID列表（可能为空列表但不会为null）
     * @throws NumberFormatException 当包含非数字内容时抛出
     */
    public static List<Integer> semicolonSeparated(String deptIdsStr) {
        if (StringUtils.isBlank(deptIdsStr)){
            return Collections.emptyList();
        }
        return Arrays.stream(deptIdsStr.split(";"))
                .map(String::trim)
                .filter(s -> !s.isEmpty())
                .map(Integer::parseInt)
                .collect(Collectors.toList());
    }

    /**
     * 比较两个字符串是否相等
     * @param str1 字符串1
     * @param str2 字符串2
     * @return 相等返回true，否则返回false
     */
    public static boolean compareString(String str1, String str2) {
        if(StringUtils.isEmpty(str1)){
            str1 = null;
        }
        if(StringUtils.isEmpty(str2)){
            str2 = null;
        }
        return StringUtils.equals(str1, str2);
    }
}
