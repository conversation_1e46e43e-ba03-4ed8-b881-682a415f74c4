package com.dt.train.management.center.model.vo.config;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class UserSpecialDeptNameConfigVO {
    @ApiModelProperty(value = "部门id,分号分隔")
    private String trainDeptIds;
    @ApiModelProperty(value = "部门名称分号分隔")
    private String trainDeptName;
    @ApiModelProperty(value = "部门全名称：部门名称+部门ID")
    private String completeDeptName;
}
