package com.dt.train.management.center.service.dingding;

import com.alibaba.fastjson.JSON;
import com.dingtalk.api.DefaultDingTalkClient;
import com.dingtalk.api.DingTalkClient;
import com.dingtalk.api.request.OapiGettokenRequest;
import com.dingtalk.api.request.OapiMessageCorpconversationAsyncsendV2Request;
import com.dingtalk.api.response.OapiGettokenResponse;
import com.dingtalk.api.response.OapiMessageCorpconversationAsyncsendV2Response;
import com.dt.train.management.center.config.BusinessConfig;
import com.dt.train.management.center.handler.DingdingHandler;
import com.dt.train.management.center.mapper.TrainUserDOMapper;
import com.dt.train.management.center.model.dao.TrainUserDO;
import com.dt.train.management.center.model.dto.dingding.DingTalkSendDTO;
import com.dt.train.management.center.model.user.DingdingUserVO;
import com.google.common.collect.Lists;
import com.taobao.api.ApiException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;


import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service
public class DingTalkV1Service {

    private final BusinessConfig businessConfig;
    private final TrainUserDOMapper trainUserDOMapper;
    private final DingdingHandler dingdingHandler;

    public DingTalkV1Service(BusinessConfig businessConfig, TrainUserDOMapper trainUserDOMapper, DingdingHandler dingdingHandler) {
        this.businessConfig = businessConfig;
        this.trainUserDOMapper = trainUserDOMapper;
        this.dingdingHandler = dingdingHandler;
    }
    public void sendDingTalkMessage(List<DingTalkSendDTO> dingTalkSendDTOS) {
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(dingTalkSendDTOS)) {
            log.info("【班次id】【{}】发送钉钉通知,通知信息：{}", dingTalkSendDTOS.get(0).getCourseId(), JSON.toJSONString(dingTalkSendDTOS));
            for (DingTalkSendDTO dingTalkSendDTO : dingTalkSendDTOS) {
                this.sendCommonDingTalk(dingTalkSendDTO);
            }
        }
    }

    public void sendCommonDingTalk(DingTalkSendDTO sendDTO) {
        String token = getToken();
        List<String> userIdList = sendDTO.getUserIds();
        if (CollectionUtils.isEmpty(userIdList)) {
            log.info("userIdList is empty, not send dingTalk");
            return;
        }

        // 获取钉钉userId
        List<TrainUserDO> trainUserDOS = trainUserDOMapper.selectDingTalkUserIdsByUserIds(userIdList);
        if (CollectionUtils.isEmpty(trainUserDOS)) {
            log.info("trainUserDOS is empty, not send dingTalk");
            return;
        }
        // 如果有为-1的，则重新查询钉钉userId并更新
        for (TrainUserDO trainUserDO : trainUserDOS) {
            if (trainUserDO.getDingTalkUserId() == null || "-1".equals(trainUserDO.getDingTalkUserId())) {
                List<DingdingUserVO> dingdingUsers = dingdingHandler.getUserInfoByMobiles(Lists.newArrayList(trainUserDO.getPhone()));
                if (CollectionUtils.isNotEmpty(dingdingUsers) && dingdingUsers.get(0)!= null && dingdingUsers.get(0).getUserId()!= null) {
                    log.info("【手机号】【{}】查询到钉钉userId【{}】", trainUserDO.getPhone(), dingdingUsers.get(0).getUserId());
                    trainUserDO.setDingTalkUserId(dingdingUsers.get(0).getUserId());
                    trainUserDOMapper.updateByPrimaryKeySelective(trainUserDO);
                }
            }
        }
        List<String> dingTalkUserIdList = trainUserDOS.stream().map(TrainUserDO::getDingTalkUserId).collect(Collectors.toList());

        String userId = String.join(",", dingTalkUserIdList);
        DingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/topapi/message/corpconversation/asyncsend_v2");
        OapiMessageCorpconversationAsyncsendV2Request request = new OapiMessageCorpconversationAsyncsendV2Request();
        request.setAgentId(Long.valueOf(businessConfig.getDingTalkMessageAgentId()));
        request.setUseridList(userId);
        request.setToAllUser(false);

        OapiMessageCorpconversationAsyncsendV2Request.Msg msg = new OapiMessageCorpconversationAsyncsendV2Request.Msg();

        // 当有课程ID时使用 action_card 消息类型
        if (sendDTO.getCourseId() != null && StringUtils.isNotBlank(businessConfig.getDingtalkCourseDetailUrl())) {
            OapiMessageCorpconversationAsyncsendV2Request.ActionCard actionCard = new OapiMessageCorpconversationAsyncsendV2Request.ActionCard();
            actionCard.setBtnOrientation("0");
            actionCard.setTitle(sendDTO.getTitle());
            actionCard.setMarkdown(sendDTO.getMdContent());

            // 添加查看班次详情按钮
            OapiMessageCorpconversationAsyncsendV2Request.BtnJsonList jsonList = new OapiMessageCorpconversationAsyncsendV2Request.BtnJsonList();
            jsonList.setTitle("查看班次详情");
            jsonList.setActionUrl(String.format(businessConfig.getDingtalkCourseDetailUrl(), sendDTO.getCourseId()));
            actionCard.setBtnJsonList(Lists.newArrayList(jsonList));

            msg.setMsgtype("action_card");
            msg.setActionCard(actionCard);
        } else {
            // 当没有课程ID时使用 markdown 消息类型
            OapiMessageCorpconversationAsyncsendV2Request.Markdown markdown = new OapiMessageCorpconversationAsyncsendV2Request.Markdown();
            markdown.setTitle(sendDTO.getTitle());
            markdown.setText(sendDTO.getMdContent());
            msg.setMsgtype("markdown");
            msg.setMarkdown(markdown);
        }

        request.setMsg(msg);

        try {
            OapiMessageCorpconversationAsyncsendV2Response rsp = client.execute(request, token);
            log.info("sendDingTalk完成:{}", rsp.getBody());
        } catch (ApiException e) {
            log.error("sendDingTalk异常:", e);
        }
    }

    private String getToken() {
        try {
            DingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/gettoken");
            OapiGettokenRequest req = new OapiGettokenRequest();
            req.setAppkey(businessConfig.getDingTalkAppKey());
            req.setAppsecret(businessConfig.getDingTalkAppSecret());
            req.setHttpMethod("GET");
            OapiGettokenResponse rsp = client.execute(req);
            return rsp.getAccessToken();
        } catch (ApiException e) {
            log.error("DingTalkService-getToken", e);
        }
        return "";

    }


}
