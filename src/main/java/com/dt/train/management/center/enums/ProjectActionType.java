package com.dt.train.management.center.enums;

import lombok.Getter;

@Getter
public enum ProjectActionType {

    ONCE_CONCENTRATED(0, "一次集中", "L3ewSAw85"),
    SEGMENTED_IMPLEMENTATION(1, "分段实施", "y9C0e5bQk"),
    NONE(2, "无", "6wRzw7uua"),
    OTHER(3, "其他", "other");

    private final int code;

    private final String desc;

    private final String thirdDictCode;

    ProjectActionType(int code, String desc, String thirdDictCode) {
        this.code = code;
        this.desc = desc;
        this.thirdDictCode = thirdDictCode;
    }

    public static ProjectActionType getByThirdDictCode(String thirdDictCode) {
        for (ProjectActionType projectActionType : ProjectActionType.values()) {
            if (projectActionType.getThirdDictCode().equals(thirdDictCode)) {
                return projectActionType;
            }
        }
        return null;
    }

    public static ProjectActionType getByCode(Integer code) {
        for (ProjectActionType projectActionType : ProjectActionType.values()) {
            if (projectActionType.getCode() == code) {
                return projectActionType;
            }
        }
        return null;
    }


}
