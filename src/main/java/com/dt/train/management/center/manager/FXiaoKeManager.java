package com.dt.train.management.center.manager;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.dt.train.management.center.model.request.fxiaoke.*;
import com.dt.train.management.center.model.response.fxiaoke.FxiaokeUserQueryResponse;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import com.dt.train.management.center.config.FXiaoKeBusinessConfig;
import com.dt.train.management.center.constant.CacheConstant;
import com.dt.train.management.center.model.response.fxiaoke.FxiaokeDataObjectQueryResponse;
import com.dt.train.management.center.model.response.fxiaoke.FxiaokeTokenResponse;
import com.dt.train.management.center.utils.RedisUtil;

import lombok.extern.slf4j.Slf4j;

@Component
@Slf4j
public class FXiaoKeManager {

    private static final String TOKEN_URL = "/cgi/corpAccessToken/get/V2";
    private static final String DATA_OBJECT_QUERY_URL = "/cgi/crm/custom/v2/data/query";
    private static final String USER_BY_MOBILE_URL = "/cgi/user/getByMobile";
    private static final String USER_BY_UPD_TIME_URL = "/cgi/user/get/batchByUpdTime";

    private final FXiaoKeBusinessConfig fXiaoKeBusinessConfig;
    
    private final RestTemplate restTemplate;

    private final RedisUtil redisUtil;

    public FXiaoKeManager(FXiaoKeBusinessConfig fXiaoKeBusinessConfig, RestTemplate restTemplate, RedisUtil redisUtil) {
        this.fXiaoKeBusinessConfig = fXiaoKeBusinessConfig;
        this.restTemplate = restTemplate;
        this.redisUtil = redisUtil;
    }

    /**
     * 通过手机号查询用户信息（openUserId）
     *
     * @param mobile 手机号码
     * @return 用户信息响应
     */
    public FxiaokeUserQueryResponse getUserByMobile(String mobile) {
        // 复用token获取逻辑
        String token = getAccessToken();
        if (token == null) {
            log.error("获取纷享销客token失败，无法查询用户信息");
            return null;
        }

        // 复用请求参数基础配置
        FxiaokeUserQueryRequest request = new FxiaokeUserQueryRequest();
        request.setCorpAccessToken(token);
        request.setCorpId(fXiaoKeBusinessConfig.getFXiaoKeCorpId());
        request.setMobile(mobile);

        // 复用请求发送逻辑
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);

        try {
            FxiaokeUserQueryResponse response = restTemplate.postForObject(
                    fXiaoKeBusinessConfig.getFXiaoKeBaseUrl() + USER_BY_MOBILE_URL,
                    new HttpEntity<>(request, headers),
                    FxiaokeUserQueryResponse.class);

            if (response != null && response.getErrorCode() == 0) {
                log.info("通过手机号查询用户成功，手机号：{}", mobile);
                return response;
            }
            log.error("查询用户失败，错误码：{}，错误信息：{}",
                    response != null ? response.getErrorCode() : "null",
                    response != null ? response.getErrorMessage() : "null");
        } catch (Exception e) {
            log.error("手机号查询用户接口调用异常，手机号：{}", mobile, e);
        }
        return null;
    }
    /**
     * 查询全部用户信息
     *
     * @return 用户信息响应
     */
    public FxiaokeUserQueryResponse getAllUser() {
        // 复用token获取逻辑
        String token = getAccessToken();
        if (token == null) {
            log.error("获取纷享销客token失败，无法查询用户信息");
            return null;
        }

        // 复用请求参数基础配置
        FxiaokeUserBatchQueryRequest request = new FxiaokeUserBatchQueryRequest();
        request.setCorpAccessToken(token);
        request.setCorpId(fXiaoKeBusinessConfig.getFXiaoKeCorpId());
        request.setPageSize(500);
        request.setPageNumber(1);

        // 复用请求发送逻辑
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);

        try {
            FxiaokeUserQueryResponse response = restTemplate.postForObject(
                    fXiaoKeBusinessConfig.getFXiaoKeBaseUrl() + USER_BY_UPD_TIME_URL,
                    new HttpEntity<>(request, headers), FxiaokeUserQueryResponse.class);

            if (response != null && response.getErrorCode() == 0) {
                log.info("纷享销客查询全部用户成功，总数：{}",  response.getEmployees()==null? 0 :response.getEmployees().size());
                return response;
            }
            log.error("纷享销客查询全部用户失败，错误码：{}，错误信息：{}",
                    response != null ? response.getErrorCode() : "null",
                    response != null ? response.getErrorMessage() : "null");
        } catch (Exception e) {
            log.error("纷享销客查询全部用户接口调用异常", e);
        }
        return null;
    }
    
    /**
     * 获取纷享销客自定义对象项目列表
     * 
     * @param limit 每页记录数
     * @param offset 页码，从0开始
     * @param orders 排序条件，可为空
     * @return 自定义对象列表响应
     */
    public FxiaokeDataObjectQueryResponse getProjectList(Integer limit, Integer offset, List<FxiaokeDataQueryFilter> filters, List<FxiaokeDataQueryOrder> orders) {
        return getProjectList(limit, offset, filters, orders, fXiaoKeBusinessConfig.getFXiaoKeDataProjectId());
    }

    public FxiaokeDataObjectQueryResponse getProjectStartList(Integer limit, Integer offset, List<FxiaokeDataQueryFilter> filters, List<FxiaokeDataQueryOrder> orders) {
        return getProjectList(limit, offset, filters, orders, fXiaoKeBusinessConfig.getFXiaoKeDataProjectStartId());
    }


    /**
     * 获取纷享销客自定义对象项目列表
     * 
     * @param limit 每页记录数
     * @param offset 页码，从0开始
     * @param orders 排序条件，可为空
     * @return 自定义对象列表响应
     */
    public FxiaokeDataObjectQueryResponse getProjectList(Integer limit, Integer offset, List<FxiaokeDataQueryFilter> filters, List<FxiaokeDataQueryOrder> orders, String objectId) {
        // 获取token
        String token = getAccessToken();
        if (token == null) {
            log.error("获取纷享销客token失败，无法查询项目列表");
            return null;
        }
        if(orders == null) {
            orders = new ArrayList<>();
            FxiaokeDataQueryOrder order = new FxiaokeDataQueryOrder();
            order.setFieldName("_id");
            order.setAsc(true);
            orders.add(order);
        }
        
        // 构建请求参数
        FxiaokeDataObjectQueryRequest request = new FxiaokeDataObjectQueryRequest();
        request.setCorpAccessToken(token);
        request.setCorpId(fXiaoKeBusinessConfig.getFXiaoKeCorpId());
        request.setCurrentOpenUserId(fXiaoKeBusinessConfig.getFXiaoKeAdminOpenUserId());
        request.setData(new FxiaokeDataObjectQueryRequest.QueryData());
        request.getData().setDataObjectApiName(objectId);
        request.getData().setSearchQueryInfo(new FxiaokeDataObjectQueryRequest.SearchQueryInfo());
        request.getData().getSearchQueryInfo().setOffset(offset != null ? offset : 0);
        request.getData().getSearchQueryInfo().setLimit(limit != null ? limit : 20);
        request.getData().getSearchQueryInfo().setFilters(filters);
        request.getData().getSearchQueryInfo().setOrders(orders);
        
        // 发送请求
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        
        HttpEntity<FxiaokeDataObjectQueryRequest> httpEntity = new HttpEntity<>(request, headers);
        
        try {
            FxiaokeDataObjectQueryResponse response = restTemplate.postForObject(
                    fXiaoKeBusinessConfig.getFXiaoKeBaseUrl() + DATA_OBJECT_QUERY_URL, 
                    httpEntity, 
                    FxiaokeDataObjectQueryResponse.class);
            if(response != null && response.getErrorCode() == 0) {
                log.info("获取纷享销客项目列表成功，总记录数：{}", response != null ? response.getData().getTotal() : 0);
                return response;
            }
            log.error("获取纷享销客项目列表失败，响应结果为空,错误码：{},错误信息：{}"
            , response != null ? response.getErrorCode() : 0, response != null ? response.getErrorMessage() : "");
            return null;
        } catch (Exception e) {
            log.error("获取纷享销客项目列表失败", e);
            return null;
        }
    }
    
    /**
     * 获取纷享销客的CorpAccessToken
     * 正常情况下CorpAccessToken的有效期为7200秒，有效期内重复获取返回相同结果
     * 本方法增加了Redis缓存机制，避免频繁请求API
     * 
     * @return String 包含token信息的响应对象
     */
    public String getAccessToken() {
        // 先从缓存中获取token
        String cachedToken = redisUtil.get(CacheConstant.FXIAOKE_TOKEN_KEY);
        
        // 如果缓存中有token，直接返回
        if (cachedToken != null) {
            log.info("从缓存获取纷享销客token成功");
            return cachedToken;
        }
        
        FxiaokeTokenResponse response = getAccessTokenInternal();
        if (response != null) {
            redisUtil.set(CacheConstant.FXIAOKE_TOKEN_KEY, response.getCorpAccessToken(), response.getExpiresIn() - 200L);
            log.info("纷享销客token已缓存，有效期{}秒", response.getExpiresIn() - 200L);
            return response.getCorpAccessToken();
        }
        return null;
    }
    
    /**
     * 强制刷新纷享销客token
     * 
     * @return String 包含token信息的响应对象
     */
    public String refreshCorpAccessToken() {
        // 删除缓存中的token
        redisUtil.del(CacheConstant.FXIAOKE_TOKEN_KEY);
        log.info("已清除缓存中的纷享销客token");
        
        // 重新获取token
        return getAccessToken();
    }


    private FxiaokeTokenResponse getAccessTokenInternal() {
        // 缓存中没有，则请求API获取新token
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        
        Map<String, String> requestBody = new HashMap<>();
        requestBody.put("appId", fXiaoKeBusinessConfig.getFXiaoKeAppId());
        requestBody.put("appSecret", fXiaoKeBusinessConfig.getFXiaoKeAppSecret());
        requestBody.put("permanentCode", fXiaoKeBusinessConfig.getFXiaoKePermanentCode());
        
        HttpEntity<Map<String, String>> request = new HttpEntity<>(requestBody, headers);
        
        try {
            FxiaokeTokenResponse response = restTemplate.postForObject(fXiaoKeBusinessConfig.getFXiaoKeBaseUrl() + TOKEN_URL, request, FxiaokeTokenResponse.class);
            log.info("获取纷享销客token成功: {}", response);
            
            // 将token和corpId存入缓存
            if (response != null && response.getCorpAccessToken() != null && response.getCorpId() != null && response.getExpiresIn() != null) {
                return response;
                
            }
            
            log.error("获取纷享销客token失败，响应结果为空");
            return null;
        } catch (Exception e) {
            log.error("获取纷享销客token失败", e);
            throw e;
        }
    }

}