package com.dt.train.management.center.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.dt.train.management.center.model.dao.SysRolePermissionDO;

public interface SysRolePermissionDOMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(SysRolePermissionDO row);

    int insertSelective(SysRolePermissionDO row);

    SysRolePermissionDO selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(SysRolePermissionDO row);

    int updateByPrimaryKey(SysRolePermissionDO row);

    int deleteByRoleId(Integer roleId);

    int batchInsert(@Param("roleId") Integer roleId, @Param("permissionKeys") List<String> permissionKeys);
    
    List<String> getPermissionKeysByRoleId(Integer roleId);
}
