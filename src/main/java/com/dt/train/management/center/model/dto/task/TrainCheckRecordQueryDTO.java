package com.dt.train.management.center.model.dto.task;

import java.util.Date;
import java.util.List;

/**
 * 
 * 表名: train_check_record
 *
 * @mbg.generated
 */
@lombok.Data
public class TrainCheckRecordQueryDTO {
   
    /**
     * 字段描述: 项目ID
     *
     * 字段名: train_check_record.project_id
     *
     * @mbg.generated
     */
    private Integer projectId;

    /**
     * 字段描述: 班次ID
     *
     * 字段名: train_check_record.course_id
     *
     * @mbg.generated
     */
    private List<Integer> courseIds;

    /**
     * 字段描述: 任务ID
     *
     * 字段名: train_check_record.task_id
     *
     * @mbg.generated
     */
    private Integer taskId;

    /**
     * 字段描述: 考勤任务ID
     *
     * 字段名: train_check_record.check_task_id
     *
     * @mbg.generated
     */
    private Integer checkTaskId;

    /**
     * 字段描述: 用户ID
     *
     * 字段名: train_check_record.user_id
     *
     * @mbg.generated
     */
    private Long userId;

    /**
     * 字段描述: 打卡类型,1-上班打卡，2-下班打卡，3-现场签到
     *
     * 字段名: train_check_record.check_type
     *
     * @mbg.generated
     */
    private Integer checkType;

    /**
     * 字段描述: 打卡时间
     *
     * 字段名: train_check_record.check_time
     *
     * @mbg.generated
     */
    private Date startCheckTime;

    private Date endCheckTime;

    /**
     * 字段描述: 打卡状态,1-正常打卡，2-迟到打卡,3-早退打卡
     *
     * 字段名: train_check_record.check_status
     *
     * @mbg.generated
     */
    private Integer checkStatus;
}