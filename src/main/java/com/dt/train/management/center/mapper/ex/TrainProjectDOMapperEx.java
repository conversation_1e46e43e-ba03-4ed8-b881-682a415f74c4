package com.dt.train.management.center.mapper.ex;

import java.util.List;

import com.dt.train.management.center.mapper.TrainProjectDOMapper;
import com.dt.train.management.center.model.dao.TrainProjectDO;
import com.dt.train.management.center.model.dto.project.TrainProjectQueryDTO;
import com.dt.train.management.center.model.vo.project.ProjectStatusSummaryVO;
import org.apache.ibatis.annotations.Param;

public interface TrainProjectDOMapperEx extends TrainProjectDOMapper{

    TrainProjectDO selectByCrmId(String crmProjectId);

    List<TrainProjectDO>  selectByIds(@Param("ids") List<Integer> ids);

    long countByQuery(TrainProjectQueryDTO query);

    List<TrainProjectDO> selectByQuery(TrainProjectQueryDTO query);

    void processCompleteStatus();

    void processStartStatus();

    ProjectStatusSummaryVO getProjectStatusSummary(TrainProjectQueryDTO query);

    void updateIsRemote(Integer projectId);
    
    List<String> selectCrossAreaIds(@Param("excludeProjectAreaIds") List<String> excludeProjectAreaIds, @Param("userId") String userId);
}
