package com.dt.train.management.center.model.vo.user;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class TrainUserVOWithSummary extends TrainUserVO {

    @ApiModelProperty(value = "总排班班次数")
    private Long totalCourse;

    @ApiModelProperty(value = "执行中班次")
    private Long runningCourse;

    @ApiModelProperty(value = "待执行班次")
    private Long waitCourse;

    @ApiModelProperty(value = "已完成班次")
    private Long completedCourse;

    @ApiModelProperty(value = "总排班天数")
    private Float allocatedDay;

    @ApiModelProperty(value = "已带班天数")
    private Float pastDay;

    @ApiModelProperty(value = "待执行天数")
    private Float futureDay;
    
}
