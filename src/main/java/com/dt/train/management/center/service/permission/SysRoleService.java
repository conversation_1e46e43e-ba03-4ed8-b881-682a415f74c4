package com.dt.train.management.center.service.permission;

import java.util.List;

import com.dt.train.management.center.model.dao.SysRoleDO;
import com.dt.train.management.center.model.vo.permission.SysRoleVO;
import com.dt.train.management.center.model.vo.permission.SysRoleWithPermissionKeysVO;

public interface SysRoleService {
    /**
     * 创建角色
     * @param role 角色信息
     * @return 创建的角色ID
     */
    SysRoleVO createRole(SysRoleDO role);

    /**
     * 更新角色
     * @param role 角色信息
     * @return 是否更新成功
     */
    SysRoleVO updateRole(SysRoleDO role);

    /**
     * 删除角色
     * @param roleId 角色ID
     * @return 是否删除成功
     */
    void deleteRole(Integer roleId);

    /**
     * 获取所有角色列表
     * @return 角色列表
     */
    List<SysRoleVO> listAllRoles();

    /**
     * 根据ID获取角色
     * @param roleId 角色ID
     * @return 角色信息
     */
    SysRoleVO getRoleById(Integer roleId);

    /**
     * 为角色分配权限
     * @param roleId 角色ID
     * @param permissionKeys 权限Key列表
     * @return 是否分配成功
     */
    boolean assignPermissions(Integer roleId, List<String> permissionKeys);

    /**
     * 获取角色的权限树
     * @param roleId 角色ID
     * @return 角色权限树
     */
    SysRoleWithPermissionKeysVO getRolePermissions(Integer roleId);

    /**
     * 获取角色的权限Key列表
     * @param roleId 角色ID
     * @return 角色权限Key列表
     */
    List<String> getAssignedPermissionKeys(Integer roleId);
}
