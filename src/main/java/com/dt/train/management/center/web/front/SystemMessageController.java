package com.dt.train.management.center.web.front;

import java.util.List;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.dt.framework.business.constant.DtHeaders;
import com.dt.framework.business.dto.page.Pagination;
import com.dt.framework.business.dto.web.common.WebResult;
import com.dt.train.management.center.model.dto.message.SystemMessageQueryDTO;
import com.dt.train.management.center.model.vo.message.SystemMessageVO;
import com.dt.train.management.center.service.message.SystemMessageService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;

@RestController
@RequestMapping("/front/system/message")
@Api(tags = "系统消息")
public class SystemMessageController {

    private final SystemMessageService systemMessageService;

    public SystemMessageController(SystemMessageService systemMessageService) {
        this.systemMessageService = systemMessageService;
    }

    @ApiOperation("查询系统消息")
    @GetMapping("/page")
    public WebResult<Pagination<SystemMessageVO>> list(@RequestHeader(DtHeaders.USER_ID) Long userId,
            @ApiParam(value = "页码", defaultValue = "1") @RequestParam(defaultValue = "1") Integer pageIndex,
            @ApiParam(value = "每页条数", defaultValue = "10") @RequestParam(defaultValue = "10") Integer pageSize) {
        SystemMessageQueryDTO queryDTO = new SystemMessageQueryDTO();
        queryDTO.setUserId(userId);
        queryDTO.setPageIndex(pageIndex);
        queryDTO.setPageSize(pageSize);
        return WebResult.successData(systemMessageService.queryPage(queryDTO));
    }

    @ApiOperation("查询今日未读消息数量")
    @GetMapping("/unreadMessageCountToday")
    public WebResult<Integer> unreadMessageCount(@RequestHeader(DtHeaders.USER_ID) Long userId) {
        return WebResult.successData(systemMessageService.unreadMessageCountToday(userId));
    }

    @ApiOperation("查询所有未读消息数量")
    @GetMapping("/unreadMessageCountAll")
    public WebResult<Integer> unreadMessageCountAll(@RequestHeader(DtHeaders.USER_ID) Long userId) {
        return WebResult.successData(systemMessageService.unreadMessageCountAll(userId));
    }

    @ApiOperation("全部已读")
    @GetMapping("/readAll")
    public WebResult<Void> readAll(@RequestHeader(DtHeaders.USER_ID) Long userId) {
        systemMessageService.markAllAsRead(userId);
        return WebResult.success();
    }

    @ApiOperation("单条已读")
    @GetMapping("/read")
    public WebResult<Void> read(@RequestHeader(DtHeaders.USER_ID) Long userId, @RequestParam Long messageId) {
        systemMessageService.markAsRead(messageId);
        return WebResult.success();
    }

    @ApiOperation("批量删除消息")
    @PostMapping("/deleteBatch")
    public WebResult<Void> deleteBatch(@RequestHeader(DtHeaders.USER_ID) Long userId, @RequestParam List<Long> messageIds) {
        systemMessageService.deleteBatch(messageIds);
        return WebResult.success();
    }

}
