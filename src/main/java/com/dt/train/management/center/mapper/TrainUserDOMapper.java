package com.dt.train.management.center.mapper;

import com.dt.train.management.center.model.dto.user.TrainUserQueryDTO;
import org.apache.ibatis.annotations.Param;

import com.dt.train.management.center.model.dao.TrainUserDO;

import java.util.List;

public interface TrainUserDOMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(TrainUserDO row);

    int insertSelective(TrainUserDO row);

    TrainUserDO selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(TrainUserDO row);

    int updateByPrimaryKey(TrainUserDO row);

    List<TrainUserDO> selectByPhones(@Param("phoneList") List<String> phoneList);

    List<TrainUserDO> selectAllByPhones(@Param("phoneList") List<String> phoneList);

    List<TrainUserDO> selectValidUsers();

    TrainUserDO selectByUserId(@Param("userId") Long userId);

    long countByQuery(TrainUserQueryDTO query);

    List<TrainUserDO> selectByQuery(TrainUserQueryDTO query);

    TrainUserDO selectByCrmUserId(String crmUserId);

    Integer countUsersByRoleId(Integer roleId);

    List<TrainUserDO> selectDingTalkUserIdsByUserIds(@Param("userIdList") List<String> userIdList);

    Long selectMinUserId();

    /**
     * 查询指定部门下兼职人员数量
     * @param deptId 部门ID
     * @return 兼职人员数量
     */
    Integer countPartTimeUsersByDeptId(@Param("deptId") String deptId);
}
