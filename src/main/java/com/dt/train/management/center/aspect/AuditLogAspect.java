package com.dt.train.management.center.aspect;

import java.lang.reflect.Method;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;

import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.StandardReflectionParameterNameDiscoverer;
import org.springframework.expression.EvaluationContext;
import org.springframework.expression.Expression;
import org.springframework.expression.ExpressionParser;
import org.springframework.expression.ParserContext;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.expression.spel.support.StandardEvaluationContext;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import com.dt.train.management.center.annotation.AuditLog;
import com.dt.train.management.center.config.UserRequestContextHolder;
import com.dt.train.management.center.enums.AuditLogEnum;
import com.dt.train.management.center.model.dto.log.LogRecord;
import com.dt.train.management.center.service.log.AuditLogService;
import com.dt.train.management.center.utils.DateFormatUtils;
import com.fasterxml.jackson.databind.ObjectMapper;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@Aspect
@Component
public class AuditLogAspect {

    @Autowired
    private AuditLogService auditLogService;

    @Autowired
    private ObjectMapper objectMapper;

    private final ExpressionParser parser = new SpelExpressionParser();
    private final StandardReflectionParameterNameDiscoverer parameterNameDiscoverer = new StandardReflectionParameterNameDiscoverer();

    // 定义自定义的ParserContext，用于处理 #{...} 格式的模板表达式
    private static final ParserContext AuditLogParserContext = new ParserContext() {
        @Override
        public boolean isTemplate() {
            return true;
        }

        @Override
        public String getExpressionPrefix() {
            return "#{"; // SpEL表达式前缀
        }

        @Override
        public String getExpressionSuffix() {
            return "}"; // SpEL表达式后缀
        }
    };

    @Around("@annotation(auditLogAnnotation)")
    public Object logAudit(ProceedingJoinPoint joinPoint, AuditLog auditLogAnnotation) throws Throwable {
        LocalDateTime startTime = LocalDateTime.now();
        Object result = null;
        Throwable exceptionThrown = null;
        String status = "成功";

        try {
            result = joinPoint.proceed();
            return result;
        } catch (Throwable e) {
            exceptionThrown = e;
            status = "失败";
            throw e;
        } finally {
            try {
                MethodSignature signature = (MethodSignature) joinPoint.getSignature();
                Method method = signature.getMethod();
                Object[] args = joinPoint.getArgs();
                String[] paramNames = parameterNameDiscoverer.getParameterNames(method);

                LogRecord logRecord = new LogRecord();
                logRecord.setTimestamp(startTime);
                logRecord.setOperatorId(UserRequestContextHolder.getRequestUserId());
                logRecord.setOperatorName(UserRequestContextHolder.getRequestUserName());
                logRecord.setMethodName(method.getDeclaringClass().getName() + "." + method.getName());
                logRecord.setStatus(status);

                // 从注解中获取 AuditLogEnum 实例
                AuditLogEnum operationEnum = auditLogAnnotation.action();
                String businessModule = operationEnum.getBusinessModule().getCode();
                String action = operationEnum.getCode(); // 操作描述
                String businessIdTemplate = operationEnum.getBusinessIdTemplate(); // 业务ID模板
                String targetUserIdTemplate = operationEnum.getTargetUserIdTemplate(); // 目标用户ID模板
                String contentTemplate = operationEnum.getContentTemplate(); // 日志内容模板

                logRecord.setBusinessModule(businessModule);

                // 创建SpEL上下文
                StandardEvaluationContext context = new StandardEvaluationContext();
                if (paramNames != null) {
                    for (int i = 0; i < paramNames.length; i++) {
                        context.setVariable(paramNames[i], args[i]);
                    }
                }
                context.setVariable("_result", result);
                if (RequestContextHolder.getRequestAttributes() != null) {
                    HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
                    context.setVariable("_request", request);
                    logRecord.setIpAddress(request.getRemoteAddr());
                }

                context.registerFunction("formatDate", DateFormatUtils.class.getDeclaredMethod("format", Date.class, String.class));

                // 解析SpEL表达式
                String finalBusinessId = parseSpelExpression(businessIdTemplate, context);
                String finalTargetUserId = parseSpelExpression(targetUserIdTemplate, context);
                String finalContent = parseSpelExpression(contentTemplate, context);

                logRecord.setAction(action); // 记录解析后的操作描述
                logRecord.setContent(finalContent); // 记录解析后的日志内容
                logRecord.setBusinessId(finalBusinessId); // 记录解析后的业务ID
                logRecord.setTargetUserId(finalTargetUserId); // 记录解析后的目标用户ID

                if (auditLogAnnotation.recordParameters() && args != null && args.length > 0) {
                    try {
                        Map<String, Object> paramsMap = new HashMap<>();
                        if (paramNames != null) {
                            for (int i = 0; i < paramNames.length; i++) {
                                paramsMap.put(paramNames[i], args[i]);
                            }
                        } else {
                            for (int i = 0; i < args.length; i++) {
                                paramsMap.put("arg" + i, args[i]);
                            }
                        }
                        logRecord.setParameters(objectMapper.writeValueAsString(paramsMap));
                    } catch (Exception e) {
                        logRecord.setParameters("参数序列化失败: " + e.getMessage());
                    }
                }
                
                if (exceptionThrown != null) {
                    logRecord.setErrorDetails(exceptionThrown.getClass().getName() + ": " + exceptionThrown.getMessage());
                }

                auditLogService.saveLog(logRecord);
            } catch (Exception e) {
                System.err.println("审计日志记录失败: " + e.getMessage());
                e.printStackTrace();
            }
        }
    }

    private String parseSpelExpression(String expressionString, EvaluationContext context) {
        if (expressionString == null || expressionString.trim().isEmpty()) {
            return "";
        }
        try {
            // 使用自定义的ParserContext来解析模板字符串
            Expression expression = parser.parseExpression(expressionString, AuditLogParserContext);
            return expression.getValue(context, String.class);
        } catch (Exception e) {
            System.err.println("SpEL表达式解析失败: " + expressionString + "，错误: " + e.getMessage());
            // 在解析失败时，可以考虑返回原始模板或一个更明确的错误提示
            return expressionString; // 或者返回 "SpEL解析错误: " + expressionString
        }
    }
}