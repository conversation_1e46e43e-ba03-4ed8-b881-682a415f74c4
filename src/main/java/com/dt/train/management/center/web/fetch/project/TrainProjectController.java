package com.dt.train.management.center.web.fetch.project;

import java.util.List;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.dt.framework.business.constant.DtHeaders;
import com.dt.framework.business.dto.page.Pagination;
import com.dt.framework.business.dto.web.common.WebResult;
import com.dt.train.management.center.enums.ProjectModeEnum;
import com.dt.train.management.center.manager.ProjectSyncManager;
import com.dt.train.management.center.model.dto.project.TrainProjectQueryDTO;
import com.dt.train.management.center.model.vo.project.ProjectStatusSummaryVO;
import com.dt.train.management.center.model.vo.project.TrainProjectVO;
import com.dt.train.management.center.model.vo.project.TrainProjectWithCourseVO;
import com.dt.train.management.center.service.cache.DeptCacheService;
import com.dt.train.management.center.service.project.TrainProjectService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;

@RestController
@RequestMapping("/fetch/project")
@Api(tags = "项目获取")
@Slf4j
public class TrainProjectController {

    private final TrainProjectService trainProjectService;

    private final ProjectSyncManager projectSyncManager;

    private final DeptCacheService deptCacheService;

    public TrainProjectController(TrainProjectService trainProjectService, ProjectSyncManager projectSyncManager
        , DeptCacheService deptCacheService) {
        this.trainProjectService = trainProjectService;
        this.projectSyncManager = projectSyncManager;
        this.deptCacheService = deptCacheService;
    }

    @ApiOperation("获取项目列表 - 不带班次信息")
    @GetMapping("/pure")
    public WebResult<Pagination<TrainProjectVO>> listNoCourse(TrainProjectQueryDTO query, @RequestHeader(DtHeaders.USER_ID) Long userId) {
        log.info("获取项目列表-不带班次:{}", query);
        if(query.getProjectMode() != null){
            query.setProjectModeDesc(ProjectModeEnum.getByCode(query.getProjectMode()).getDesc());
        }
        buildChildrenAreaIds(query);
        Pagination<TrainProjectVO> pagination = trainProjectService.page(query);
        return WebResult.successData(pagination);
    }

    @GetMapping
    @ApiOperation("获取项目列表 - 带班次信息")
    public WebResult<Pagination<TrainProjectWithCourseVO>> list(TrainProjectQueryDTO query, @RequestHeader(DtHeaders.USER_ID) Long userId) {
        log.info("获取项目列表-带班次信息:{}", query);
        if(query.getProjectMode() != null){
            query.setProjectModeDesc(ProjectModeEnum.getByCode(query.getProjectMode()).getDesc());
        }
        buildChildrenAreaIds(query);
        Pagination<TrainProjectWithCourseVO> pagination = trainProjectService.pageWithCourse(query);
        return WebResult.successData(pagination);
    }

    @GetMapping("/cross")
    @ApiOperation("获取跨区支持项目列表 - 带班次信息")
    public WebResult<Pagination<TrainProjectWithCourseVO>> listCrossArea(TrainProjectQueryDTO query
        , @RequestHeader(DtHeaders.USER_ID) Long userId) {
        log.info("获取项目列表-带班次信息:{}", query);
        if(query.getProjectMode() != null){
            query.setProjectModeDesc(ProjectModeEnum.getByCode(query.getProjectMode()).getDesc());
        }
        buildExcludeChildrenAreaIds(query);
        buildChildrenAreaIds(query);
        Pagination<TrainProjectWithCourseVO> pagination = trainProjectService.pageWithCourse(query);
        return WebResult.successData(pagination);
    }

    @ApiOperation("获取项目详情")
    @GetMapping("/detail")
    public WebResult<TrainProjectWithCourseVO> detail(@ApiParam("项目ID") @RequestParam(required = true) Integer projectId) {
        return WebResult.successData(trainProjectService.getProjectWithCourse(projectId));
    }
    
    @ApiOperation("获取项目状态")
    @GetMapping("/status/summary")
    public WebResult<ProjectStatusSummaryVO> getProjectStatusSummary(TrainProjectQueryDTO query) {
        buildChildrenAreaIds(query);
        return WebResult.successData(trainProjectService.getProjectStatusSummary(query));
    }

    @ApiOperation("获取跨区支持项目状态")
    @GetMapping("/status/summary/cross")
    public WebResult<ProjectStatusSummaryVO> getProjectStatusSummaryCross(TrainProjectQueryDTO query) {
        buildExcludeChildrenAreaIds(query);
        buildChildrenAreaIds(query);
        return WebResult.successData(trainProjectService.getProjectStatusSummary(query));
    }

    @ApiOperation("获取跨区支持项目涉及区域ID")
    @GetMapping("/cross/area/ids")
    public WebResult<List<String>> getCrossAreaIds(@ApiParam("当前部门") @RequestParam String currentDeptId,
                                                    @ApiParam("参与用户") @RequestParam(required = false) String courseRelationUserId) {
        List<Integer> allChildrenDeptIds = deptCacheService.getAllChildrenDeptIds(Integer.valueOf(currentDeptId));
        List<String> excludeProjectAreaIds = allChildrenDeptIds.stream()
            .map(String::valueOf)
            .collect(Collectors.toList());
        return WebResult.successData(trainProjectService.getCrossAreaIds(excludeProjectAreaIds, courseRelationUserId));
    }

    @ApiOperation("获取最后同步CRM项目的时间")
    @GetMapping("/last/sync/crm/project/time")
    public WebResult<Long> getLastSyncCrmProjectTime() {
        return WebResult.successData(projectSyncManager.getLastSyncCrmProjectTime());
    }

    private void buildChildrenAreaIds(TrainProjectQueryDTO query) {
        if (StringUtils.isBlank(query.getProjectAreaId())) {
            return;
        }
        List<Integer> allChildrenDeptIds = deptCacheService.getAllChildrenDeptIds(Integer.valueOf(query.getProjectAreaId()));
        if(CollectionUtils.isNotEmpty(query.getExcludeProjectAreaIds())){
            allChildrenDeptIds = allChildrenDeptIds.stream()
                .filter(id -> !query.getExcludeProjectAreaIds().contains(String.valueOf(id)))
                .collect(Collectors.toList());
        }
        List<String> allDeptIds = allChildrenDeptIds.stream().map(String::valueOf).collect(Collectors.toList());
        query.setProjectAreaIds(allDeptIds);
        query.setProjectAreaId(null);
    }

    private void buildExcludeChildrenAreaIds(TrainProjectQueryDTO query) {
        if (StringUtils.isBlank(query.getCurrentDeptId())) {
            return;
        }
        List<Integer> allChildrenDeptIds = deptCacheService.getAllChildrenDeptIds(Integer.valueOf(query.getCurrentDeptId()));
        List<String> allDeptIds = allChildrenDeptIds.stream().map(String::valueOf).collect(Collectors.toList());
        query.setExcludeProjectAreaIds(allDeptIds);
    }
    
}
