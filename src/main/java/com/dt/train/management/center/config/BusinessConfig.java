package com.dt.train.management.center.config;

import com.ctrip.framework.apollo.spring.annotation.ApolloJsonValue;
import com.dt.train.management.center.model.vo.config.CrmOrgProvinceDeptMappingConfigVO;
import lombok.Data;
import lombok.ToString;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;
import org.springframework.validation.annotation.Validated;

import java.util.List;
import java.util.Map;

@Data
@Configuration
@Validated
@RefreshScope
@ToString
public class BusinessConfig {
    @Value("${dingTalk.appKey:dingyuyq15qyelni4jdq}")
    private String dingTalkAppKey;

    @Value("${dingTalk.appSecret:Lxvk7sTxO20fmWb3geGtqQix5-mr6CCfCkmiOcjCQRhTBjdahT3gfEvUn2sQF-TL}")
    private String dingTalkAppSecret;

    @Value("#{${crm.orz.dept.mapping}}")
    private Map<String,Integer> crmOrgOneLevelDeptMapping;

    @ApolloJsonValue("${crm.orz.dept.child.mapping}")
    private List<CrmOrgProvinceDeptMappingConfigVO> crmOrgMappingConfigVO;

    @Value("${check.in.time.end.second:36060}")
    private Integer checkInTimeEndSecond;

    @Value("${check.in.time.normal.second:32460}")
    private Integer checkInTimeNormalSecond;

    @Value("${check.out.time.normal.second:64800}")
    private Integer checkOutTimeNormalSecond;

    /**
     * 现场签到最晚提醒时间，单位：秒
     */
    @Value("${sign.on.site.alert.second:60000}")
    private Integer signOnSiteAlertSecond;

    @Value("${log.sql.enable:false}")
    private Boolean logSqlEnable;

    @ApolloJsonValue("${train.user.role.list:[]}")
    private List<String> trainUserRoleList;


    @Value("#{${dept.color.mapping}}")
    private Map<String,String> deptColorMapping;

    @Value("${default.oss.platform:2}")
    private Integer defaultOssPlatform;

    @Value("${dingTalk.message.agentId:3899133372}")
    private String dingTalkMessageAgentId;
    @Value("${dingtalk.course.detail.url:}")
    private String dingtalkCourseDetailUrl;

    @ApolloJsonValue("${dept.admin.list:[10000,20000]}")
    private List<String> deptAdminList;

    @Value("${ding.ai.stream.app.appKey:dinghzn85nengkugegbi}")
    private String dingAiStreamAppClientId;

    @Value("${ding.ai.stream.app.appSecret:cMXaFVofEZdETOZvR8hwFfVOHrLZ8kc_WOyDuuwuPwt5TbDjJErHyHcuICrX1kMY}")
    private String dingAiStreamAppClientSecret;

}
