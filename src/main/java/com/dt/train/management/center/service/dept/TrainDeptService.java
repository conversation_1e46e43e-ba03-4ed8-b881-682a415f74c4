package com.dt.train.management.center.service.dept;

import com.dt.train.management.center.model.dept.UserDeptVO;
import com.dt.train.management.center.model.vo.dept.TrainDeptVO;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public interface TrainDeptService {
    /**
     * 根据用户id获取部门树
     *
     * @param userId 用户id
     */
    List<UserDeptVO> getByUserId(Long userId,Boolean isAll);

    /**
     * 根据CRM区域组织架构id和省份id获取部门信息
     *
     * @param crmOrgId    CRM区域组织架构id
     * @param crmProvinceId 省份id
     */
    TrainDeptVO getDeptByCrmOrgIdAndCrmProvinceId(String crmOrgId,String crmProvinceId);

    TrainDeptVO getDeptVoFromDbById(Integer deptId);

    List<UserDeptVO> getUserById(Integer deptId, String userName);
}
