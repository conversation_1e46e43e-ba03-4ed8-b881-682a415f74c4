package com.dt.train.management.center.model.vo.project;

import java.util.List;

import com.dt.train.management.center.model.vo.check.TrainCheckTaskVO;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class TrainCourseVOWithCheck extends TrainCourseVO {
    
    @ApiModelProperty("打卡列表")
    private List<TrainCheckTaskVO> checkTasks;

    @ApiModelProperty("应打卡人天")
    private Integer shouldCheckDays;

    @ApiModelProperty("正常打卡人天")
    private Integer normalCheckedDays;

    @ApiModelProperty("异常打卡人天")
    private Integer abnormalCheckedDays;

    @ApiModelProperty(value = "已带班人天数")
    private Float pastDay;

    @ApiModelProperty(value = "待带班人天数")
    private Float futureDay;
}
