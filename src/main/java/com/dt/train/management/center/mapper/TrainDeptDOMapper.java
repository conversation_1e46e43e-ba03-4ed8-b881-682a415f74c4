package com.dt.train.management.center.mapper;

import org.apache.ibatis.annotations.Param;

import com.dt.train.management.center.model.dao.TrainDeptDO;

import java.util.List;

public interface TrainDeptDOMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(TrainDeptDO row);

    int insertSelective(TrainDeptDO row);

    TrainDeptDO selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(TrainDeptDO row);

    int updateByPrimaryKey(TrainDeptDO row);

    /**
     * 根据部门名称列表查询部门信息
     * @param deptNames 部门名称列表
     * @return 匹配的部门列表
     */
    List<TrainDeptDO> selectByDeptNames(@Param("deptNames") List<String> deptNames);
    /**
     * 根据部门ID列表查询部门信息
     * @param deptIds 部门ID列表
     * @return 匹配的部门列表
     */
    List<TrainDeptDO> selectByDeptIds(@Param("deptIds") List<Integer> deptIds);

    /**
     * 查询当前部门及子部门信息
     * @param parentDeptIds 父部门ID列表
     * @return 匹配的部门列表
     */
    List<TrainDeptDO> selectChildrenByParentIds(@Param("parentDeptIds") List<Integer> parentDeptIds);
    /**
     * 查询所有部门信息
     * @return 匹配的部门列表
     */
    List<TrainDeptDO> selectAllDepts();
}