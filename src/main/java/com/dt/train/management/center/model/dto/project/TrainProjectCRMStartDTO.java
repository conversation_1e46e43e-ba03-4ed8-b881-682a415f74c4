package com.dt.train.management.center.model.dto.project;

import java.util.List;

import lombok.Data;
@Data
public class TrainProjectCRMStartDTO {

    /**
     * 字段描述: CRM项目ID
     *
     * 字段名: train_project.crm_project_id
     *
     */
    private String crmProjectId;


    /**
     * 字段描述: 执行类型，0-一次集中，1-分段实施
     *
     * 字段名: train_project.action_type
     *
     */
    private String actionType;

    private String isRemote;


    /**
     * 字段描述: 面授总天数
     *
     * 字段名: train_project.teach_days
     *
     * @mbg.generated
     */
    private Float teachDays;

    /**
     * 字段描述: 面授班次
     *
     * 字段名: train_project.teach_course_count
     *
     * @mbg.generated
     */
    private Integer teachCourseCount;

    /**
     * 字段描述: 面授备注说明
     *
     * 字段名: train_project.teach_remark
     *
     * @mbg.generated
     */
    private String teachRemark;

    /**
     * 字段描述: 项目协助人,多个人;分隔
     *
     * 字段名: train_project.project_helper_id
     *
     * @mbg.generated
     */
    private List<String> projectHelperId;

    private List<String> projectHelperName;

    /**
     * 字段描述: 学支编辑,多个人;分隔
     *
     * 字段名: train_project.live_editor_id
     *
     * @mbg.generated
     */
    private List<String> liveEditorId;

    private List<String> liveEditorName;

}
