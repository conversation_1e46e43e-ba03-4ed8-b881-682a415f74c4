package com.dt.train.management.center.model.vo.dept;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class DeptCacheVO {
    @ApiModelProperty(value = "部门id")
    private Integer deptId;
    @ApiModelProperty(value = "父部门id")
    private Integer parentDeptId;
    @ApiModelProperty(value = "部门名称分号分隔")
    private String trainDeptName;
    @ApiModelProperty(value = "部门全名称：父部门名称+部门名称")
    private String completeDeptName;
}
