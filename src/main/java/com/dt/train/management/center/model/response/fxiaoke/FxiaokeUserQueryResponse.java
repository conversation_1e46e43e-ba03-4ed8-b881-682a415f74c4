package com.dt.train.management.center.model.response.fxiaoke;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

@Data
public class FxiaokeUserQueryResponse {
    @JsonProperty("empList")
    private List<Employee> empList;

    @JsonProperty("employees")
    private List<Employee> employees;


    @JsonProperty("errorCode")
    private Integer errorCode;

    @JsonProperty("errorMessage")
    private String errorMessage;

    @JsonProperty("errorDescription")
    private String errorDescription;

    @JsonProperty("traceId")
    private String traceId;

    @Data
    public static class Employee {
        @JsonProperty("enterpriseId")
        private Integer enterpriseId;

        @JsonProperty("openUserId")
        private String openUserId;

        @JsonProperty("account")
        private String account;

        @JsonProperty("fullName")
        private String fullName;

        @JsonProperty("name")
        private String name;

        @JsonProperty("status")
        private String status;

        @JsonProperty("mobile")
        private String mobile;

        @JsonProperty("telephone")
        private String telephone;

        @JsonProperty("role")
        private String role;

        @JsonProperty("post")
        private String post;

        @JsonProperty("qq")
        private String qq;

        @JsonProperty("email")
        private String email;

        @JsonProperty("gender")
        private String gender;

        @JsonProperty("profileImage")
        private String profileImage;

        @JsonProperty("description")
        private String description;

        @JsonProperty("weixin")
        private String weixin;

        @JsonProperty("msn")
        private String msn;

        @JsonProperty("extensionNumber")
        private String extensionNumber;

        @JsonProperty("mobileSetting")
        private MobileSetting mobileSetting;

        @JsonProperty("workingState")
        private String workingState;

        @JsonProperty("isActive")
        private Boolean isActive;

        @JsonProperty("mainDepartmentIds")
        private List<Integer> mainDepartmentIds;

        @JsonProperty("departmentIds")
        private List<Integer> departmentIds;

        @JsonProperty("departmentAsteriskIds")
        private List<Integer> departmentAsteriskIds;

        @JsonProperty("employeeAsteriskIds")
        private List<Integer> employeeAsteriskIds;

        @JsonProperty("birthDate")
        private String birthDate;

        @JsonProperty("hireDate")
        private String hireDate;

        @JsonProperty("empNum")
        private String empNum;

        @JsonProperty("startWorkDate")
        private String startWorkDate;

        @JsonProperty("stopTime")
        private Long stopTime;

        @JsonProperty("createTime")
        private Long createTime;

        @JsonProperty("updateTime")
        private Long updateTime;

        @JsonProperty("nameSpell")
        private String nameSpell;

        @JsonProperty("nameOrder")
        private String nameOrder;
    }

    @Data
    public static class MobileSetting {
        @JsonProperty("mobileStatus")
        private String mobileStatus;

        @JsonProperty("departmentIds")
        private List<Integer> departmentIds;

        @JsonProperty("employeeIds")
        private List<Integer> employeeIds;
    }
}
