package com.dt.train.management.center.config;

import com.dt.train.management.center.interceptor.CommonInterceptor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 6/2/21 2:56 PM
 */
@Slf4j
public class InterceptorMvcConfig implements WebMvcConfigurer {

    @Autowired
    private CommonInterceptor commonInterceptor;


    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(commonInterceptor).addPathPatterns("/**")
            .excludePathPatterns("/feign/**", "/user/getUserRoles", "/user/getByUserId", "/dict/mapping");
        log.info("==================InterceptorMvcConfig  addInterceptors =============");
    }
}
