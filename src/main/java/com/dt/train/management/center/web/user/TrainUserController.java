package com.dt.train.management.center.web.user;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.dt.framework.business.constant.BusinessCode;
import com.dt.framework.business.constant.DtHeaders;
import com.dt.framework.business.dto.page.Pagination;
import com.dt.framework.business.dto.web.common.WebResult;
import com.dt.framework.core.exception.BusinessException;
import com.dt.train.management.center.config.BusinessConfig;
import com.dt.train.management.center.enums.ManagementExceptionEnum;
import com.dt.train.management.center.manager.ScheduleManager;
import com.dt.train.management.center.manager.TrainUserManager;
import com.dt.train.management.center.model.common.HalfDayTime;
import com.dt.train.management.center.model.dto.project.CourseUserRelationQueryDTO;
import com.dt.train.management.center.model.dto.user.TrainUserQueryDTO;
import com.dt.train.management.center.model.dto.user.TrainUserWithQueryDTO;
import com.dt.train.management.center.model.vo.project.TrainCourseUserRelationVOWithCourse;
import com.dt.train.management.center.model.vo.user.TrainUserVO;
import com.dt.train.management.center.model.vo.user.TrainUserVOWithCourse;
import com.dt.train.management.center.model.vo.user.TrainUserVOWithPermission;
import com.dt.train.management.center.model.vo.user.TrainUserVOWithSummary;
import com.dt.train.management.center.service.permission.SysRoleService;
import com.dt.train.management.center.service.project.TrainCourseUserRelationService;
import com.dt.train.management.center.service.user.TrainUserService;
import com.dt.train.management.center.utils.ExceptionUtil;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

@RestController
@RequestMapping("/user")
@Api(tags = "用户管理")
@Slf4j
public class TrainUserController {
    private final TrainUserService trainUserService;

    private final TrainCourseUserRelationService trainCourseUserRelationService;

    private final BusinessConfig businessConfig;

    private final ScheduleManager scheduleManager;

    private final SysRoleService sysRoleService;

    private final TrainUserManager trainUserManager;

    public TrainUserController(TrainUserService trainUserService, TrainCourseUserRelationService trainCourseUserRelationService
    , BusinessConfig businessConfig, ScheduleManager scheduleManager, SysRoleService sysRoleService, TrainUserManager trainUserManager) {
        this.trainUserService = trainUserService;
        this.trainCourseUserRelationService = trainCourseUserRelationService;
        this.businessConfig = businessConfig;
        this.scheduleManager = scheduleManager;
        this.sysRoleService = sysRoleService;
        this.trainUserManager = trainUserManager;
    }
    
    @GetMapping("/getUserRoles")
    @ApiOperation("获取用户角色列表")
    public WebResult<List<String>> getByUserId() {
        List<String> trainUserRoleList = businessConfig.getTrainUserRoleList();
        return WebResult.successData(trainUserRoleList);
    }

    @GetMapping("/getByUserId")
    @ApiOperation("获取当前登录用户信息")
    public WebResult<TrainUserVOWithPermission> getByUserId(@RequestHeader(DtHeaders.USER_ID) Long userId) {
        TrainUserVO trainUserVO = trainUserService.getUserInfoByUserId(userId);
        if(trainUserVO == null){
            trainUserVO = trainUserManager.syncAndUpdateUserId(userId);
        }
        if(trainUserVO == null){
            throw new BusinessException(BusinessCode.NO_PERMISSION_CODE, BusinessCode.NO_PERMISSION_MESSAGE);
        }
        TrainUserVOWithPermission trainUserVOWithPermission = new TrainUserVOWithPermission();
        BeanUtils.copyProperties(trainUserVO, trainUserVOWithPermission);
        trainUserVOWithPermission.setPermissionKeys(sysRoleService.getAssignedPermissionKeys(trainUserVO.getRoleId()));
        return WebResult.successData(trainUserVOWithPermission);
    }

    @GetMapping
    @ApiOperation("获取用户分页列表")
    public WebResult<Pagination<TrainUserVO>> list(TrainUserQueryDTO query) {
        log.info("获取用户分页列表:{}", query);
        Pagination<TrainUserVO> pagination = trainUserService.pageQuery(query);
        return WebResult.successData(pagination);
    }

    @GetMapping("/withCourse")
    @ApiOperation("获取用户分页列表 - 带班次")
    public WebResult<Pagination<TrainUserVOWithCourse>> listWithCourse(TrainUserWithQueryDTO query) {
        log.info("获取用户分页列表:{}", query);
        Pagination<TrainUserVO> pagination = trainUserService.pageQuery(query);
        if(pagination.getTotal() == 0){
            return WebResult.successData(new Pagination<>());
        }
        List<Long> userIds = pagination.getRows().stream().map(TrainUserVO::getUserId).collect(Collectors.toList());
        CourseUserRelationQueryDTO relationQuery = new CourseUserRelationQueryDTO();
        relationQuery.setUserIds(userIds);
        if(query.getStartTime() != null && query.getStartHalfDay() != null){
            relationQuery.setStartDate(new HalfDayTime(query.getStartTime(), query.getStartHalfDay()).toFullDate());
        }
        if(query.getEndTime() != null && query.getEndHalfDay() != null){
            relationQuery.setEndDate(new HalfDayTime(query.getEndTime(), query.getEndHalfDay()).toFullDate());
        }
        List<TrainCourseUserRelationVOWithCourse> relationUsers = trainCourseUserRelationService.listWithCourseByQuery(relationQuery);
        if(query.getCourseId() != null){
            relationUsers = relationUsers.stream().filter(relation -> relation.getCourseId() != query.getCourseId()).collect(Collectors.toList());
        }
        Map<Long, List<TrainCourseUserRelationVOWithCourse>> relationUsersMap = relationUsers.stream().collect(Collectors.groupingBy(TrainCourseUserRelationVOWithCourse::getUserId));
        Pagination<TrainUserVOWithCourse> result = new Pagination<>();
        result.setPageIndex(pagination.getPageIndex());
        result.setPageSize(pagination.getPageSize());
        result.setTotal(pagination.getTotal());
        result.setRows(pagination.getRows().stream().map(user -> {
            TrainUserVOWithCourse userWithCourse = new TrainUserVOWithCourse();
            BeanUtils.copyProperties(user, userWithCourse);
            userWithCourse.setRelationList(relationUsersMap.get(user.getUserId()));
            return userWithCourse;
        }).collect(Collectors.toList()));
        return WebResult.successData(result);
    }

    @GetMapping("/withSummary")
    @ApiOperation("获取用户分页列表 - 带总结")
    public WebResult<Pagination<TrainUserVOWithSummary>> listWithSummary(TrainUserWithQueryDTO query) {
        Pagination<TrainUserVO> pagination = trainUserService.pageQuery(query);
        if(pagination.getTotal() == 0){
            return WebResult.successData(new Pagination<>());
        }
        List<TrainUserVOWithSummary> userWithSummaryList = scheduleManager.getUserScheduleSummary(pagination.getRows(), query);
        Pagination<TrainUserVOWithSummary> result = new Pagination<>();
        result.setPageIndex(pagination.getPageIndex());
        result.setPageSize(pagination.getPageSize());
        result.setTotal(pagination.getTotal());
        result.setRows(userWithSummaryList);
        return WebResult.successData(result);
    }




}

