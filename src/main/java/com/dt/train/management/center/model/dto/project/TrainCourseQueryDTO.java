package com.dt.train.management.center.model.dto.project;

import java.util.Date;
import java.util.List;

import org.springframework.format.annotation.DateTimeFormat;

import com.dt.framework.business.util.page.PaginationParams;
import com.dt.train.management.center.enums.SortEnum;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class TrainCourseQueryDTO implements PaginationParams{

    /**
     * 字段描述: 项目ID
     *
     * 字段名: train_course.project_id
     *
     */
    @ApiModelProperty(value = "项目ID")
    private Integer projectId;

    /**
     * 字段描述: 项目名称
     *
     * 字段名: train_course.project_name
     *
     */
    @ApiModelProperty(value = "项目名称")
    private String projectName;

    /**
     * 字段描述: 项目编码
     *
     * 字段名: train_course.project_code
     *
     */
    @ApiModelProperty(value = "项目编码")
    private String projectCode;

    /**
     * 字段描述: 班次名称
     *
     * 字段名: train_course.course_name
     *
     */
    @ApiModelProperty(value = "班次名称")
    private String courseName;

    /**
     * 字段描述: 班次状态,0-未开始，1-进行中，2-已完成
     *
     * 字段名: train_course.course_status
     *
     */
    @ApiModelProperty(value = "班次状态,0-未开始，1-进行中，2-已完成")
    private Integer courseStatus;

    /**
     * 字段描述: 班次类型,train_course_type
     *
     * 字段名: train_course.course_type
     *
     */
    @ApiModelProperty(value = "班次类型")
    private Integer courseType;

    @ApiModelProperty(value = "班次形式,1-面授，2-远程直播，3-现场直播")
    private Integer courseForm;

    /**
     * 字段描述: 服务对象
     *
     * 字段名: train_course.service_object
     *
     */
    @ApiModelProperty(value = "服务对象")
    private String serviceObject;

    /**
     * 字段描述: 是否异地，0-否，1-是
     *
     * 字段名: train_course.is_remote
     *
     */
    @ApiModelProperty(value = "是否异地，0-否，1-是")
    private Integer isRemote;

    /**
     * 字段描述: 班次执行区域
     *
     * 字段名: train_course.course_area
     *
     */
    @ApiModelProperty(value = "班次执行区域")
    private String courseArea;

    /**
     * 字段描述: 班次执行区域id
     *
     * 字段名: train_course.course_area_id
     *
     */
    @ApiModelProperty(value = "班次执行区域id")
    private Integer courseAreaId;


    /**
     * 所属区域组织及子区域组织
     */
    @ApiModelProperty(value = "所属区域组织及子区域组织")
    private List<Integer> courseAreaIds;

    /**
     * 字段描述: 省份id
     *
     * 字段名: train_course.province_id
     *
     */
    @ApiModelProperty(value = "省份id")
    private String provinceId;

    /**
     * 字段描述: 城市id
     *
     * 字段名: train_course.city_id
     *
     */
    @ApiModelProperty(value = "城市id")
    private String cityId;

    /**
     * 字段描述: 地区id
     *
     * 字段名: train_course.area_id
     *
     */
    @ApiModelProperty(value = "地区id")
    private String areaId;

    /**
     * 字段描述: 是否填写计划说明，0-否，1-是
     *
     * 字段名: train_course.has_plan_desc
     *
     */
    @ApiModelProperty(value = "是否填写计划说明，0-否，1-是")
    private Boolean hasPlanDesc;

    /**
     * 字段描述: 是否填写执行进展说明，0-否，1-是
     *
     * 字段名: train_course.has_execution_progress
     *
     */
    @ApiModelProperty(value = "是否填写执行进展说明，0-否，1-是")
    private Boolean hasExecutionProgress;

    /**
     * 字段描述: 计划开始时间
     *
     * 字段名: train_course.plan_start_date
     *
     */
    @ApiModelProperty(value = "计划开始时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date planStartDate;

    /**
     * 字段描述: 计划结束时间
     *
     * 字段名: train_course.plan_end_date
     *
     */
    @ApiModelProperty(value = "计划结束时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date planEndDate;

    /**
     * 字段描述: 实际开始时间
     *
     * 字段名: train_course.real_start_date
     *
     */
    @ApiModelProperty(value = "实际开始时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date realStartDate;

    /**
     * 字段描述: 实际结束时间
     *
     * 字段名: train_course.real_end_date
     *
     */
    @ApiModelProperty(value = "实际结束时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date realEndDate;

    @ApiModelProperty(value = "执行人id")
    private Long executeUserId;

    @ApiModelProperty(value = "执行人姓名")
    private String executeUserName;

    @ApiModelProperty(value = "支援人id")
    private Long supportUserId;

    @ApiModelProperty(value = "支援人姓名")
    private String supportUserName;

    @ApiModelProperty(value = "执行人/支援人id")
    private List<Long> courseRelationUserIds;

    @ApiModelProperty(value = "执行人/支援人姓名")
    private String courseRelationUserName;

    @ApiModelProperty(value = "页码")
    private Integer pageIndex = 0;

    @ApiModelProperty(value = "每页条数")
    private Integer pageSize = 10;

    @ApiModelProperty(value = "排序字段, 实际执行开始时间:real_start_date, 创建时间:gmt_create ")
    private String orderBy = "real_start_date";

    @ApiModelProperty(value = "排序方式")
    private SortEnum sortBy = SortEnum.DESC;

    @ApiModelProperty(value = "当前组织id") 
    private Integer currentDeptId;

    /**
     * 排除项目区域
     */
    @ApiModelProperty(value = "排除项目区域", hidden=true)
    private List<Integer> excludeCourseAreaIds;

    @ApiModelProperty(value = "开始时间(用于查询指定时间范围开始的班次)")
    private Date startTime;

    @ApiModelProperty(value = "结束时间(用于查询指定时间范围开始的班次)")
    private Date endTime;
}
