package com.dt.train.management.center.model.vo.config;

import com.alibaba.fastjson.JSONObject;

import io.swagger.annotations.ApiModelProperty;

/**
 * 
 * 表名: smart_config
 *
 */
@lombok.Data
public class SmartConfigVO {

    /**
     * 字段描述: 配置键
     *
     * 字段名: smart_config.config_key
     *
     */
    @ApiModelProperty("配置键")
    private String configKey;

    /**
     * 字段描述: 配置键
     *
     * 字段名: smart_config.second_key
     *
     */
    @ApiModelProperty("配置二级键")
    private String secondKey;

    /**
     * 字段描述: 排序
     *
     * 字段名: smart_config.config_order
     *
     */
    @ApiModelProperty("排序")
    private Integer configOrder;

    /**
     * 字段描述: 扩展信息
     *
     * 字段名: smart_config.ext_info
     *
     */
    @ApiModelProperty("扩展信息")
    private String extInfo;

    /**
     * 字段描述: 配置类型 0 single, 1 chunk
     *
     * 字段名: smart_config.config_type
     *
     */
    @ApiModelProperty(value = "配置类型", hidden = true)
    private Integer configType;

    /**
     * 字段描述: 配置值
     *
     * 字段名: smart_config.config_value
     *
     */
    @ApiModelProperty("配置值")
    private JSONObject configValue;
}