package com.dt.train.management.center.model.common;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import com.dt.train.management.center.enums.HalfDayEnum;
import com.dt.train.management.center.utils.DateTimeUtils;
import com.fasterxml.jackson.annotation.JsonFormat;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

/**
 * 半天维度时间
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class HalfDayTime {

    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate date;

    private HalfDayEnum halfDay;

    public HalfDayTime(Date date, HalfDayEnum halfDay) {
        if (date == null) {
            this.date = null;
            this.halfDay = null;
            return;
        }

        this.date = date.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        this.halfDay = halfDay;
    }

    public HalfDayTime(Date date) {
        if (date == null) {
            this.date = null;
            this.halfDay = null;
            return;
        }

        this.date = date.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        this.halfDay = date.getHours() >= 12 ? HalfDayEnum.PM : HalfDayEnum.AM;
    }

    /**
     * 获取完整日期
     * 
     * @return
     */
    public Date toFullDate() {
        if (date == null) {
            return null;
        }

        ZoneId zoneId = ZoneId.systemDefault();

        LocalDateTime startOfDay = date.atStartOfDay();
        LocalDateTime noon = date.atTime(LocalTime.NOON);
        if (halfDay == HalfDayEnum.PM) {
            return Date.from(noon.atZone(zoneId).toInstant());
        }
        return Date.from(startOfDay.atZone(zoneId).toInstant());
    }

    /**
     * 计算两个半天维度时间之间的天数
     * 
     * @param other
     * @return
     */
    public float daysBetweenWithHalf(HalfDayTime other) {
        if (this.date == null || other.date == null || this.halfDay == null || other.halfDay == null) {
            throw new IllegalArgumentException("日期和半天不能为空");
        }
        int dayDiff = (int) (this.date.toEpochDay() - other.date.toEpochDay());
        int halfDayThis = this.halfDay == HalfDayEnum.AM ? 0 : 1;
        int halfDayOther = other.halfDay == HalfDayEnum.AM ? 0 : 1;
        return Math.abs(dayDiff * 2 + (halfDayThis - halfDayOther)) / 2.0f + 0.5f;
    }

    /**
     * 判断当前时间是否在其他时间之后
     * @param other
     * @return
     */
    public boolean isAfter(HalfDayTime other) {
        if (this.date == null || other.date == null || this.halfDay == null || other.halfDay == null) {
            throw new IllegalArgumentException("日期和半天不能为空");
        }
        return this.date.isAfter(other.date) || (this.date.isEqual(other.date) && this.halfDay == HalfDayEnum.PM && other.halfDay == HalfDayEnum.AM);
    }

    /**
     * 判断当前时间是否在其他时间之后(包含等于)
     * @param other
     * @return
     */
    public boolean isAfterEqual(HalfDayTime other) {
        return !isBefore(other);
    }

    /**
     * 判断当前时间是否在其他时间之前
     * @param other
     * @return
     */
    public boolean isBefore(HalfDayTime other) {
        return other.isAfter(this);
    }

    /**
     * 判断当前时间是否在其他时间之前(包含等于)
     * @param other
     * @return
     */
    public boolean isBeforeEqual(HalfDayTime other) {
        return !isAfter(other);
    }

    /**
     * 获取下一个半天
     * @return
     */
    public HalfDayTime next() {
        if (this.halfDay == HalfDayEnum.AM) {
            return new HalfDayTime(this.date, HalfDayEnum.PM);
        }
        return new HalfDayTime(this.date.plusDays(1), HalfDayEnum.AM);
    }

    /**
     * 获取下一个完整日期
     * @return
     */
    public HalfDayTime nextDay() {
        return new HalfDayTime(this.date.plusDays(1), this.halfDay);
    }

    /**
     * 获取上一个半天
     * @return
     */
    public HalfDayTime previous() {
        if (this.halfDay == HalfDayEnum.PM) {
            return new HalfDayTime(this.date, HalfDayEnum.AM);
        }
        return new HalfDayTime(this.date.minusDays(1), HalfDayEnum.PM);
    }

    /**
     * 获取当前时间所在天的开始时间
     * @return
     */
    public Date startOfDay() {
        return DateTimeUtils.getStartOfDay(this.date);
    }

    /**
     * 获取当前时间所在天的结束时间
     * @return
     */
    public Date endOfDay() {
        return DateTimeUtils.getEndOfDay(this.date);
    }

    /**
     * 获取当前时间
     * @return
     */
    public static HalfDayTime now() {
        return new HalfDayTime(new Date());
    }

    /**
     * 判断两个半天维度时间是否相等
     * @param obj
     * @return
     */
    @Override
    public boolean equals(Object obj) {
        if (obj == null) {
            return false;
        }
        if (obj instanceof HalfDayTime) {
            HalfDayTime other = (HalfDayTime) obj;
            return this.date.equals(other.date) && this.halfDay == other.halfDay;
        }
        return false;
    }

    @Override
    public int hashCode() {
        return Objects.hash(date, halfDay);
    }

    /**
     * 返回半天维度时间的字符串表示, eg: "2025/07/02 上午"
     * @return
     */
    @Override
    public String toString() {
        if (date == null || halfDay == null) {
            return "";
        }
        // 格式化为 yyyy/MM/dd
        String dateStr = date.format(DateTimeFormatter.ofPattern("yyyy/MM/dd"));
        return dateStr + " " + halfDay.getDisplayName();
    }

    /**
     * 将多个时段的时间范围拼接成指定格式的字符串
     * 格式示例：2025/3/10 上午-2025/3/10 下午;2025/3/11 上午-2025/3/11 下午
     *
     * @param timeRanges 时段列表，每个元素是一个包含开始和结束时间的数组
     * @return 拼接后的字符串
     */
    public static String formatTimeRangesSafely(List<HalfDayTime[]> timeRanges) {
        if (CollectionUtils.isEmpty(timeRanges)) {
            return "";
        }

        DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern("yyyy/M/d");

        return timeRanges.stream()
                .filter(range -> range != null && range.length == 2)
                .map(range -> {
                    String startStr = formatHalfDayTime(range[0], dateFormatter);
                    String endStr = formatHalfDayTime(range[1], dateFormatter);
                    return StringUtils.isNoneBlank(startStr, endStr)
                            ? startStr + "-" + endStr
                            : "";
                })
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.joining(";"));
    }

    private static String formatHalfDayTime(HalfDayTime time, DateTimeFormatter dateFormatter) {
        if (time == null || time.getDate() == null || time.getHalfDay() == null) {
            return "";
        }
        return time.getDate().format(dateFormatter) + " " + time.getHalfDay().getDisplayName();
    }

    public boolean isValid(){
        return this.date != null && this.halfDay != null;
    }
    
}
