package com.dt.train.management.center.model.vo.permission;

import java.util.List;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class SysPermissionTreeVO {

    @ApiModelProperty(value = "权限ID")
    private Integer id;

    @ApiModelProperty(value = "父权限ID")
    private Integer parentId;

    @ApiModelProperty(value = "权限名称")
    private String permissionName;

    @ApiModelProperty(value = "权限类型（MENU目录 PAGE页面 FUNCTION功能）")
    private String permissionType;

    @ApiModelProperty(value = "权限标识")
    private String permissionKey;

    @ApiModelProperty(value = "权限路径")
    private String pPath;

    @ApiModelProperty(value = "权限图标")
    private String icon;

    @ApiModelProperty(value = "权限状态（0正常 1停用 -1删除）")
    private Integer sortOrder;

    @ApiModelProperty(value = "是否可选（0不可选 1可选）")
    private Integer canSelect;

    @ApiModelProperty(value = "备注信息")
    private String remark;

    @ApiModelProperty(value = "子权限列表")
    private List<SysPermissionTreeVO> children;

    @ApiModelProperty(value = "选中状态（0未选中 1选中）")
    private Integer selected = 0;
}
