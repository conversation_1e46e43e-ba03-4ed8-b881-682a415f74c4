package com.dt.train.management.center.service.project;

import cn.hutool.core.date.DateUtil;
import com.alibaba.excel.util.DateUtils;
import com.dt.framework.business.dto.page.Pagination;
import com.dt.train.management.center.config.UserRequestContextHolder;
import com.dt.train.management.center.enums.*;
import com.dt.train.management.center.mapper.ex.TrainCourseDOMapperEx;
import com.dt.train.management.center.mapper.ex.TrainProjectDOMapperEx;
import com.dt.train.management.center.model.common.HalfDayTime;
import com.dt.train.management.center.model.common.HalfDayTimeWithUser;
import com.dt.train.management.center.model.dao.TrainCourseDO;
import com.dt.train.management.center.model.dao.TrainCourseDOWithBLOBs;
import com.dt.train.management.center.model.dao.TrainProjectDO;
import com.dt.train.management.center.model.dto.dingding.DingTalkSendDTO;
import com.dt.train.management.center.model.dto.message.SystemMessageDTO;
import com.dt.train.management.center.model.dto.project.*;
import com.dt.train.management.center.model.dto.task.TrainCheckTaskDTO;
import com.dt.train.management.center.model.dto.task.TrainCheckTaskQueryDTO;
import com.dt.train.management.center.model.dto.task.TrainTaskDTO;
import com.dt.train.management.center.model.dto.user.TrainUserQueryDTO;
import com.dt.train.management.center.model.vo.dict.DictMappingVO;
import com.dt.train.management.center.model.vo.project.ProjectStatusSummaryVO;
import com.dt.train.management.center.model.vo.project.TrainCourseVO;
import com.dt.train.management.center.model.vo.user.TrainUserVO;
import com.dt.train.management.center.service.dict.DictMappingService;
import com.dt.train.management.center.service.dingding.DingTalkV1Service;
import com.dt.train.management.center.service.message.SystemMessageService;
import com.dt.train.management.center.service.task.TrainCheckTaskService;
import com.dt.train.management.center.service.task.TrainTaskService;
import com.dt.train.management.center.service.user.TrainUserService;
import com.dt.train.management.center.utils.ExceptionUtil;
import com.dt.train.management.center.utils.StringParseUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Service
@Slf4j
public class TrainCourseService {

    private final TrainCourseDOMapperEx trainCourseDOMapperEx;

    private final TrainProjectDOMapperEx trainProjectMapperEx;

    private final TrainCourseUserRelationService trainCourseUserRelationService;

    private final TrainCourseAreaRelationService trainCourseAreaRelationService;

    private final TrainTaskService trainTaskService;

    private final DictMappingService dictMappingService;

    private final TrainCheckTaskService trainCheckTaskService;

    private final TrainUserService trainUserService;

    private Map<Integer, List<TrainCourseAreaRelationDTO>> trainCourseAreaRelationMap;

    private final DingTalkV1Service dingTalkV1Service;

    private final SystemMessageService systemMessageService;


    public TrainCourseService(TrainCourseDOMapperEx trainCourseDOMapperEx,
                            TrainProjectDOMapperEx trainProjectMapperEx,
            TrainCourseUserRelationService trainCourseUserRelationService,
            TrainCourseAreaRelationService trainCourseAreaRelationService,
            TrainTaskService trainTaskService,
            DictMappingService dictMappingService,
            TrainCheckTaskService trainCheckTaskService,
            TrainUserService trainUserService,
            DingTalkV1Service dingTalkV1Service,
            SystemMessageService systemMessageService) {
        this.trainCourseDOMapperEx = trainCourseDOMapperEx;
        this.trainProjectMapperEx = trainProjectMapperEx;
        this.trainCourseUserRelationService = trainCourseUserRelationService;
        this.trainCourseAreaRelationService = trainCourseAreaRelationService;
        this.trainTaskService = trainTaskService;
        this.dictMappingService = dictMappingService;
        this.trainCheckTaskService = trainCheckTaskService;
        this.trainUserService = trainUserService;
        this.dingTalkV1Service = dingTalkV1Service;
        this.systemMessageService = systemMessageService;
    }

    /**
     * 保存或更新班次
     * 
     * @param trainCourseDTO 班次DTO
     */
    @Transactional(rollbackFor = Exception.class)
    public TrainCourseVO saveOrUpdateTrainCourse(TrainCourseSaveOrUpdateDTO trainCourseDTO) {
        TrainCourseDOWithBLOBs trainCourseDO = saveDTO2DO(trainCourseDTO);
        if (trainCourseDTO.getId() == null) {
            trainCourseDO.setCreatedBy(UserRequestContextHolder.getRequestUserId());
            trainCourseDO.setUpdatedBy(UserRequestContextHolder.getRequestUserId());
            trainCourseDOMapperEx.insertSelective(trainCourseDO);
            try {
                sendMsg(trainCourseDTO,trainCourseDO.getId());
            } catch (Exception e) {
                log.info("班次新增发送人员分配消息异常", e);
            }
        } else {
            try {
                compareAndSendMsg(trainCourseDTO);
            } catch (Exception e) {
                log.info("班次更新发送消息异常", e);
            }
            trainCourseDO.setUpdatedBy(UserRequestContextHolder.getRequestUserId());
            trainCourseDOMapperEx.updateByPrimaryKeySelective(trainCourseDO);
        }
        trainCourseUserRelationService.saveOrUpdateExecuteUser(trainCourseDO.getProjectId(), trainCourseDO.getId(),
                trainCourseDTO.getExecuteUsers());
        trainCourseUserRelationService.saveOrUpdateSupportUser(trainCourseDO.getProjectId(), trainCourseDO.getId(),
                trainCourseDTO.getSupportUsers());
        processAllAreaList(trainCourseDTO, trainCourseDO);
        if(trainCourseDTO.getCourseForm() == CourseFormEnum.FACE_TO_FACE.getCode() 
            || trainCourseDTO.getCourseForm() == CourseFormEnum.LIVE_ON_SITE.getCode()){
            TrainTaskDTO trainTaskDTO = new TrainTaskDTO();
            trainTaskDTO.setCourseId(trainCourseDO.getId());
            trainTaskDTO.setProjectId(trainCourseDO.getProjectId());
            trainTaskDTO.setTaskType(TrainTaskTypeEnum.CHECK_IN.getCode());
            trainTaskDTO.setTaskName(TrainTaskTypeEnum.CHECK_IN.getName());
            trainTaskDTO.setTaskDesc(TrainTaskTypeEnum.CHECK_IN.getDesc());
            trainTaskService.saveOrUpdateTrainTask(trainTaskDTO);
        }else if(trainCourseDTO.getId() != null){
            trainTaskService.deleteByCourseId(trainCourseDTO.getId());
        }
        return do2vo(trainCourseDO);
    }


    private void sendMsg(TrainCourseSaveOrUpdateDTO trainCourseDTO, Integer courseId) {
        List<DingTalkSendDTO> dingTalkSendDTOS = new ArrayList<>();
        List<SystemMessageDTO> systemMessageDTOS = new ArrayList<>();

        // 班次执行地点
        String newAreaStr = getCourseSiteListStr(trainCourseDTO.getCourseAreaList(), null);
        // 格式化班次实际执行日期
        String courseExecutionPeriod = HalfDayTime.formatTimeRangesSafely(
                Collections.singletonList(new HalfDayTime[]{trainCourseDTO.getRealStartDate(), trainCourseDTO.getRealEndDate()})
        );

        // 处理执行人（按用户分组，合并时段）
        if (!CollectionUtils.isEmpty(trainCourseDTO.getExecuteUsers())) {
            buildExecuteUserMsg(trainCourseDTO, courseId, courseExecutionPeriod, newAreaStr, dingTalkSendDTOS, systemMessageDTOS);
        }

        // 处理支援人（按用户分组，合并时段）
        if (!CollectionUtils.isEmpty(trainCourseDTO.getSupportUsers())) {
            buildSupportUserMsg(trainCourseDTO, courseId, courseExecutionPeriod, newAreaStr, dingTalkSendDTOS, systemMessageDTOS);
        }
        // 发送消息
        saveAndSendMessages(systemMessageDTOS, dingTalkSendDTOS);
    }

    private void buildSupportUserMsg(TrainCourseSaveOrUpdateDTO trainCourseDTO, Integer courseId, String courseExecutionPeriod, String newAreaStr, List<DingTalkSendDTO> dingTalkSendDTOS, List<SystemMessageDTO> systemMessageDTOS) {
        // 按用户ID分组，收集所有时段
        Map<Long, List<HalfDayTime[]>> supportUserTimeSlots = getUserTimeSlots(trainCourseDTO.getSupportUsers(),null);

        for (Map.Entry<Long, List<HalfDayTime[]>> entry : supportUserTimeSlots.entrySet()) {
            Long userId = entry.getKey();
            List<HalfDayTime[]> timeSlots = entry.getValue();

            // 格式化所有时段
            String timeSlotsStr = HalfDayTime.formatTimeRangesSafely(timeSlots);

            // 构建钉钉消息内容
            String addSupportUserContent = "<font color=\"#FF0000\">【重要通知-人员分配提醒】</font>" + "\n\n" +
                    "您被分配为班次的支援人，请关注班次信息" + "\n\n" +
                    "班次名称：" + trainCourseDTO.getCourseName() + "\n\n" +
                    "所属项目：" + trainCourseDTO.getProjectCode() + " " + trainCourseDTO.getProjectName() + "\n\n" +
                    "班次实际执行日期：" + courseExecutionPeriod + "\n\n" +
                    "执行地点：" + formatArea(newAreaStr) + "\n\n" +
                    "支援时段：" + timeSlotsStr + "\n\n" +
                    "通知时间：" + DateUtils.format(new Date(), "yyyy/MM/dd HH:mm:ss") + "\n\n";

            DingTalkSendDTO dingTalkSendDTO = new DingTalkSendDTO();
            dingTalkSendDTO.setCourseId(courseId);
            dingTalkSendDTO.setTitle("【重要通知-人员分配提醒】");
            dingTalkSendDTO.setUserIds(Collections.singletonList(userId.toString()));
            dingTalkSendDTO.setMdContent(addSupportUserContent);
            dingTalkSendDTOS.add(dingTalkSendDTO);

            // 构建系统消息体
            String messageTitle = "你被分配为班次<span style=\"color: #007cff; cursor: pointer;\">《" + trainCourseDTO.getCourseName() + "》</span>的支援人";
            String messageContent = "你被分配为班次《" + trainCourseDTO.getCourseName() + "》的支援人，请关注班次信息，按时执行。\n" + "支援时段：" + timeSlotsStr;
            SystemMessageDTO systemMessageDTO = getSystemMessageDTO(userId.toString(), Long.valueOf(courseId), messageTitle, messageContent, MessageEnum.COURSE_ASSIGN_PERSON.getCode());
            systemMessageDTOS.add(systemMessageDTO);
        }
    }

    private void buildExecuteUserMsg(TrainCourseSaveOrUpdateDTO trainCourseDTO, Integer courseId, String courseExecutionPeriod, String newAreaStr, List<DingTalkSendDTO> dingTalkSendDTOS, List<SystemMessageDTO> systemMessageDTOS) {
        // 按用户ID分组，收集所有时段
        Map<Long, List<HalfDayTime[]>> executeUserTimeSlots = getUserTimeSlots(trainCourseDTO.getExecuteUsers(),null);

        for (Map.Entry<Long, List<HalfDayTime[]>> entry : executeUserTimeSlots.entrySet()) {
            Long userId = entry.getKey();
            List<HalfDayTime[]> timeSlots = entry.getValue();

            // 格式化所有时段
            String timeSlotsStr = HalfDayTime.formatTimeRangesSafely(timeSlots);

            // 构建钉钉消息内容
            String addExecuteUserContent = "<font color=\"#FF0000\">【重要通知-人员分配提醒】</font>" + "\n\n" +
                    "您被分配为班次的执行人，请关注班次信息" + "\n\n" +
                    "班次名称：" + trainCourseDTO.getCourseName() + "\n\n" +
                    "所属项目：" + trainCourseDTO.getProjectCode() + " " + trainCourseDTO.getProjectName() + "\n\n" +
                    "班次实际执行日期：" + courseExecutionPeriod + "\n\n" +
                    "执行地点：" + formatArea(newAreaStr) + "\n\n" +
                    "执行时段：" + timeSlotsStr + "\n\n" +
                    "通知时间：" + DateUtils.format(new Date(), "yyyy/MM/dd HH:mm:ss") + "\n\n";

            DingTalkSendDTO dingTalkSendDTO = new DingTalkSendDTO();
            dingTalkSendDTO.setCourseId(courseId);
            dingTalkSendDTO.setTitle("【重要通知-人员分配提醒】");
            dingTalkSendDTO.setUserIds(Collections.singletonList(userId.toString()));
            dingTalkSendDTO.setMdContent(addExecuteUserContent);
            dingTalkSendDTOS.add(dingTalkSendDTO);

            // 构建系统消息体
            String messageTitle = "你被分配为班次<span style=\"color: #007cff; cursor: pointer;\">《" + trainCourseDTO.getCourseName() + "》</span>的执行人";
            String messageContent = "你被分配为班次《" + trainCourseDTO.getCourseName() + "》的执行人，请关注班次信息，按时执行。" ;

            SystemMessageDTO systemMessageDTO = getSystemMessageDTO(userId.toString(), Long.valueOf(courseId), messageTitle, messageContent, MessageEnum.COURSE_ASSIGN_PERSON.getCode());
            systemMessageDTOS.add(systemMessageDTO);
        }
    }

    private static @NotNull Map<Long, List<HalfDayTime[]>> getUserTimeSlots(List<TrainCourseUserRelationSaveOrUpdateDTO> userRelationSaveOrUpdateDTOS, List<TrainCourseUserRelationDTO> userRelationDTOS) {
        // 处理第一个列表（SaveOrUpdateDTO）
        Map<Long, List<HalfDayTime[]>> map1 = new HashMap<>();
        if (userRelationSaveOrUpdateDTOS != null && !userRelationSaveOrUpdateDTOS.isEmpty()) {
            map1 = userRelationSaveOrUpdateDTOS.stream()
                    .collect(Collectors.groupingBy(
                            TrainCourseUserRelationSaveOrUpdateDTO::getUserId,
                            Collectors.mapping(dto -> new HalfDayTime[]{dto.getStartDate(), dto.getEndDate()},
                                    Collectors.toList())
                    ));
        }
        // 处理第二个列表（RelationDTO）
        Map<Long, List<HalfDayTime[]>> map2 = new HashMap<>();
        if (userRelationDTOS != null && !userRelationDTOS.isEmpty()) {
            map2 = userRelationDTOS.stream()
                    .collect(Collectors.groupingBy(
                            TrainCourseUserRelationDTO::getUserId,
                            Collectors.mapping(dto -> new HalfDayTime[]{dto.getStartDate(), dto.getEndDate()},
                                    Collectors.toList())
                    ));
        }
        // 合并两个结果集
        Map<Long, List<HalfDayTime[]>> mergedMap = new HashMap<>();
        // 添加 map1 的所有数据
        map1.forEach((userId, slots) -> mergedMap.computeIfAbsent(userId, k -> new ArrayList<>()).addAll(slots));
        // 添加 map2 的所有数据
        map2.forEach((userId, slots) -> mergedMap.computeIfAbsent(userId, k -> new ArrayList<>()).addAll(slots));
        return mergedMap;
    }

    private void saveAndSendMessages(List<SystemMessageDTO> systemMessages, List<DingTalkSendDTO> dingTalkMessages) {
        if (!systemMessages.isEmpty()) {
            systemMessageService.saveBatch(systemMessages);
        }
        if (!dingTalkMessages.isEmpty()) {
            dingTalkV1Service.sendDingTalkMessage(dingTalkMessages);
        }
    }

    private void compareAndSendMsg(TrainCourseSaveOrUpdateDTO trainCourseDTO) {
        List<DingTalkSendDTO> dingTalkSendDTOS = new ArrayList<>();
        List<SystemMessageDTO> systemMessageDTOS = new ArrayList<>();
        // 获取初始参数
        TrainCourseVO oldTrainCourseVO = getById(trainCourseDTO.getId());

        // 处理四个列表（各自独立去重和时间段拼接）
        List<TrainCourseUserRelationDTO> newExecuteUsers = processUserList(saveDTO2DTO(trainCourseDTO.getExecuteUsers()));
        List<TrainCourseUserRelationDTO> newSupportUsers = processUserList(saveDTO2DTO(trainCourseDTO.getSupportUsers()));
        List<TrainCourseUserRelationDTO> oldExecuteUsers = processUserList(oldTrainCourseVO.getExecuteUsers());
        List<TrainCourseUserRelationDTO> oldSupportUsers = processUserList(oldTrainCourseVO.getSupportUsers());
        Map<Long, TrainCourseUserRelationDTO> newExecuteUserMap = toUserMap(newExecuteUsers);
        Map<Long, TrainCourseUserRelationDTO> newSupportUserMap = toUserMap(newSupportUsers);
        Map<Long, TrainCourseUserRelationDTO> oldExecuteUserMap = toUserMap(oldExecuteUsers);
        Map<Long, TrainCourseUserRelationDTO> oldSupportUserMap = toUserMap(oldSupportUsers);

        // 新旧班次执行人和支援人合并去重
        Map<String, TrainCourseUserRelationDTO> oldUsersMap = new LinkedHashMap<>();
        Map<String, TrainCourseUserRelationDTO> newUsersMap = new LinkedHashMap<>();
        List<TrainCourseUserRelationDTO> mergedOldUsers = new ArrayList<>();
        List<TrainCourseUserRelationDTO> mergedNewUsers = new ArrayList<>();
        mergedOldUsers.addAll(oldExecuteUsers);
        mergedOldUsers.addAll(oldSupportUsers);
        mergedNewUsers.addAll(newExecuteUsers);
        mergedNewUsers.addAll(newSupportUsers);
        buildOldAndNewMap(mergedOldUsers, mergedNewUsers, oldUsersMap, newUsersMap);
        // 最新班次执行日期
        String newPeriod = HalfDayTime.formatTimeRangesSafely(Collections.singletonList(new HalfDayTime[]{trainCourseDTO.getRealStartDate(), trainCourseDTO.getRealEndDate()}));
        trainCourseDTO.setRealDateStr(newPeriod);
        // 本次新增执行人
        List<TrainCourseUserRelationDTO> newAddedExecuteUsers = getChangedUsers(newExecuteUsers, oldExecuteUserMap);
        // 本次新增支援人(非必填注意判空)
        List<TrainCourseUserRelationDTO> newAddedSupportUsers = getChangedUsers(newSupportUsers, oldSupportUserMap);
        // 本次移除执行人
        List<TrainCourseUserRelationDTO> removedExecuteUsers = getChangedUsers(oldExecuteUsers, newExecuteUserMap);
        // 本次移除支援人
        List<TrainCourseUserRelationDTO> removedSupportUsers = getChangedUsers(oldSupportUsers, newSupportUserMap);

        // 构建班次变更信息
        List<String> courseChanges = new ArrayList<>();
        String oldAreaStr = getCourseSiteListStr(null, oldTrainCourseVO.getCourseAreaList());
        String newAreaStr = getCourseSiteListStr(trainCourseDTO.getCourseAreaList(), null);
        buildCourseChanges(trainCourseDTO, oldTrainCourseVO, courseChanges, oldAreaStr, newAreaStr);

        // 检查用户分配时段变更，获取变更的userId
        Set<String> changedUserIds = new HashSet<>();
        changedUserIds.addAll(findUserTimeChanges(oldExecuteUsers, newExecuteUsers));
        changedUserIds.addAll(findUserTimeChanges(oldSupportUsers, newSupportUsers));

        // 获取保留用户 keptUserIds
        Set<String> keptExecuteUserIds = findKeptUserIds(oldExecuteUsers, newExecuteUsers);
        Set<String> keptSupportUserIds = findKeptUserIds(oldSupportUsers, newSupportUsers);
        Set<String> keptAndNotChangeUserIdsUserIds = new HashSet<>(keptExecuteUserIds);
        keptAndNotChangeUserIdsUserIds.addAll(keptSupportUserIds);
        Set<String> keptUserIds = new HashSet<>(keptAndNotChangeUserIdsUserIds);

        // 留下 保留+执行时间无变化 的用户
        keptAndNotChangeUserIdsUserIds.removeAll(changedUserIds);

        // 未变更时段的用户：只通知班次的变更信息
        if (!keptAndNotChangeUserIdsUserIds.isEmpty() && !courseChanges.isEmpty()) {
            buildKeptAndNotChangeUserMsg(trainCourseDTO, courseChanges, keptAndNotChangeUserIdsUserIds, dingTalkSendDTOS, systemMessageDTOS);
        }

        // 变更时段的用户：通知班次的变更信息+时段变更信息
        if (!changedUserIds.isEmpty()) {
            buildChangedUserMsg(trainCourseDTO, changedUserIds, courseChanges, oldUsersMap, newUsersMap, dingTalkSendDTOS, systemMessageDTOS);
        }

        // 保留的执行人+支援人: 人员变更通知
        if(!keptUserIds.isEmpty()){
            buildKeptUserCourseChangesMsg(trainCourseDTO, newAddedExecuteUsers, newAddedSupportUsers, removedExecuteUsers, removedSupportUsers, keptUserIds, dingTalkSendDTOS, systemMessageDTOS);
        }

        // 新增执行人通知
        if (!newAddedExecuteUsers.isEmpty()) {
            buidAddExecuteUserMsg(trainCourseDTO, newAddedExecuteUsers, newAreaStr, dingTalkSendDTOS, systemMessageDTOS);
        }

        // 新增支援人通知
        if (!newAddedSupportUsers.isEmpty()) {
            buildAddSupportUserMsg(trainCourseDTO, newAddedSupportUsers, newAreaStr, dingTalkSendDTOS, systemMessageDTOS);
        }

        // 移除执行人通知
        if (!removedExecuteUsers.isEmpty()) {
            buildRemoveExecuteUserMsg(trainCourseDTO, removedExecuteUsers, dingTalkSendDTOS, systemMessageDTOS);
        }

        // 移除支援人通知
        if (!removedSupportUsers.isEmpty()) {
            buildRemoveSupportUserMsg(trainCourseDTO, removedSupportUsers, dingTalkSendDTOS, systemMessageDTOS);
        }

        //  统一消息处理
        saveAndSendMessages(systemMessageDTOS, dingTalkSendDTOS);
    }

    /**
     * 处理单个列表：按用户ID去重，合并时间段并格式化
     * @param userList 要处理的用户列表
     * @return 处理后的用户列表（已去重）
     */
    private List<TrainCourseUserRelationDTO> processUserList(List<TrainCourseUserRelationDTO> userList) {
        if (CollectionUtils.isEmpty(userList)) {
            return new ArrayList<>();
        }

        // 按用户ID分组，收集所有时间段
        Map<Long, List<HalfDayTime[]>> userTimeSlots = getUserTimeSlots(null, userList);

        // 生成新列表
        return userTimeSlots.entrySet().stream()
                .map(entry -> {
                    Long userId = entry.getKey();
                    List<HalfDayTime[]> timeSlots = entry.getValue();

                    TrainCourseUserRelationDTO dto = new TrainCourseUserRelationDTO();

                    // 设置基本属性（取第一条记录）
                    userList.stream().filter(d -> d.getUserId().equals(userId))
                            .findFirst().ifPresent(firstRecord -> BeanUtils.copyProperties(firstRecord, dto));

                    dto.setExecuteDateStr(HalfDayTime.formatTimeRangesSafely(timeSlots));
                    return dto;
                })
                .collect(Collectors.toList());
    }

    private static @NotNull List<TrainCourseUserRelationDTO> getChangedUsers(List<TrainCourseUserRelationDTO> newExecuteUsers, Map<Long, TrainCourseUserRelationDTO> oldExecuteUserMap) {
        List<TrainCourseUserRelationDTO> newAddedExecuteUsers = new ArrayList<>();
        for (TrainCourseUserRelationDTO newUser : newExecuteUsers) {
            if (!oldExecuteUserMap.containsKey(newUser.getUserId())) {
                newAddedExecuteUsers.add(newUser);
            }
        }
        return newAddedExecuteUsers;
    }

    private void buildRemoveSupportUserMsg(TrainCourseSaveOrUpdateDTO trainCourseDTO, List<TrainCourseUserRelationDTO> removedSupportUsers, List<DingTalkSendDTO> dingTalkSendDTOS, List<SystemMessageDTO> systemMessageDTOS) {
        for (TrainCourseUserRelationDTO removedSupportUser : removedSupportUsers) {
            String addExecuteUserContent = "<font color=\"#FF0000\">【重要通知-人员移除提醒】</font>" + "\n\n" +
                    "您原负责的班次已取消您的支援人身份，若有疑问请联系项目经理" + "\n\n" +
                    "班次名称：" + trainCourseDTO.getCourseName() + "\n\n" +
                    "所属项目：" + trainCourseDTO.getProjectCode() + " " + trainCourseDTO.getProjectName() + "\n\n" +
                    "通知时间：" + DateUtils.format(new Date(), "yyyy/MM/dd HH:mm:ss") + "\n\n";
            DingTalkSendDTO dingTalkSendDTO = new DingTalkSendDTO();
            dingTalkSendDTO.setCourseId(trainCourseDTO.getId());
            dingTalkSendDTO.setTitle("【重要通知-人员移除提醒】");
            dingTalkSendDTO.setUserIds(Collections.singletonList(removedSupportUser.getUserId().toString()));
            dingTalkSendDTO.setMdContent(addExecuteUserContent);
            dingTalkSendDTOS.add(dingTalkSendDTO);
            // 构建系统消息体
            String messageTitle = "你已被移除班次<span style=\"color: #007cff; cursor: pointer;\">《"+ trainCourseDTO.getCourseName()+"》</span>的支援人";
            String messageContent = "你原定负责的班次【"+ trainCourseDTO.getCourseName()+"】已取消你的支援人安排，原执行时间段为 "+ removedSupportUser.getExecuteDateStr() +"，请注意调整个人安排，若有疑问请联系项目经理";
            SystemMessageDTO systemMessageDTO = getSystemMessageDTO(removedSupportUser.getUserId().toString(), Long.valueOf(trainCourseDTO.getId()), messageTitle, messageContent,MessageEnum.COURSE_PERSON_REMOVE.getCode());
            systemMessageDTOS.add(systemMessageDTO);
        }
    }

    private void buildRemoveExecuteUserMsg(TrainCourseSaveOrUpdateDTO trainCourseDTO, List<TrainCourseUserRelationDTO> removedExecuteUsers, List<DingTalkSendDTO> dingTalkSendDTOS, List<SystemMessageDTO> systemMessageDTOS) {
        for (TrainCourseUserRelationDTO removedExecuteUser : removedExecuteUsers) {
            String addExecuteUserContent = "<font color=\"#FF0000\">【重要通知-人员移除提醒】</font>" + "\n\n" +
                    "您原负责的班次已取消您的执行人身份，若有疑问请联系项目经理" + "\n\n" +
                    "班次名称：" + trainCourseDTO.getCourseName() + "\n\n" +
                    "所属项目：" + trainCourseDTO.getProjectCode() + " " + trainCourseDTO.getProjectName() + "\n\n" +
                    "通知时间：" + DateUtils.format(new Date(), "yyyy/MM/dd HH:mm:ss") + "\n\n";
            DingTalkSendDTO dingTalkSendDTO = new DingTalkSendDTO();
            dingTalkSendDTO.setCourseId(trainCourseDTO.getId());
            dingTalkSendDTO.setTitle("【重要通知-人员移除提醒】");
            dingTalkSendDTO.setUserIds(Collections.singletonList(removedExecuteUser.getUserId().toString()));
            dingTalkSendDTO.setMdContent(addExecuteUserContent);
            dingTalkSendDTOS.add(dingTalkSendDTO);
            // 构建系统消息体
            String messageTitle = "你已被移除班次<span style=\"color: #007cff; cursor: pointer;\">《"+ trainCourseDTO.getCourseName()+"》</span>的执行人";
            String messageContent = "你原定负责的班次【"+ trainCourseDTO.getCourseName()+"】已取消你的执行人安排，原执行时间段为 "+ removedExecuteUser.getExecuteDateStr()+"，请注意调整个人安排，若有疑问请联系项目经理";
            SystemMessageDTO systemMessageDTO = getSystemMessageDTO(removedExecuteUser.getUserId().toString(), Long.valueOf(trainCourseDTO.getId()), messageTitle, messageContent,MessageEnum.COURSE_PERSON_REMOVE.getCode());
            systemMessageDTOS.add(systemMessageDTO);
        }
    }

    private void buildAddSupportUserMsg(TrainCourseSaveOrUpdateDTO trainCourseDTO, List<TrainCourseUserRelationDTO> newAddedSupportUsers, String newAreaStr, List<DingTalkSendDTO> dingTalkSendDTOS, List<SystemMessageDTO> systemMessageDTOS) {
        for (TrainCourseUserRelationDTO newAddedSupportUser : newAddedSupportUsers) {
            String addExecuteUserContent = "<font color=\"#FF0000\">【重要通知-人员分配提醒】</font>" + "\n\n" +
                    "您被分配为班次的支援人，请关注班次信息" + "\n\n" +
                    "班次名称：" + trainCourseDTO.getCourseName() + "\n\n" +
                    "所属项目：" + trainCourseDTO.getProjectCode() + " " + trainCourseDTO.getProjectName() + "\n\n" +
                    "班次实际执行日期：" + trainCourseDTO.getRealDateStr() + "\n\n" +
                    "执行地点：" + formatArea(newAreaStr) + "\n\n" +
                    "执行时段：" + newAddedSupportUser.getExecuteDateStr() + "\n\n" +
                    "通知时间：" + DateUtils.format(new Date(), "yyyy/MM/dd HH:mm:ss") + "\n\n";
            DingTalkSendDTO dingTalkSendDTO = new DingTalkSendDTO();
            dingTalkSendDTO.setCourseId(trainCourseDTO.getId());
            dingTalkSendDTO.setTitle("【重要通知-人员分配提醒】");
            dingTalkSendDTO.setUserIds(Collections.singletonList(newAddedSupportUser.getUserId().toString()));
            dingTalkSendDTO.setMdContent(addExecuteUserContent);
            dingTalkSendDTOS.add(dingTalkSendDTO);
            // 构建系统消息体
            String messageTitle = "你被分配为班次<span style=\"color: #007cff; cursor: pointer;\">《"+ trainCourseDTO.getCourseName()+"》</span>的支援人";
            String messageContent = "你被分配为班次《"+ trainCourseDTO.getCourseName()+"》的支援人，请关注班次信息，按时执行。";
            SystemMessageDTO systemMessageDTO = getSystemMessageDTO(newAddedSupportUser.getUserId().toString(), Long.valueOf(trainCourseDTO.getId()), messageTitle, messageContent,MessageEnum.COURSE_ASSIGN_PERSON.getCode());
            systemMessageDTOS.add(systemMessageDTO);
        }
    }

    private void buidAddExecuteUserMsg(TrainCourseSaveOrUpdateDTO trainCourseDTO, List<TrainCourseUserRelationDTO> newAddedExecuteUsers, String newAreaStr, List<DingTalkSendDTO> dingTalkSendDTOS, List<SystemMessageDTO> systemMessageDTOS) {
        for (TrainCourseUserRelationDTO newAddedExecuteUser : newAddedExecuteUsers) {
            String addExecuteUserContent = "<font color=\"#FF0000\">【重要通知-人员分配提醒】</font>" + "\n\n" +
                    "您被分配为班次的执行人，请关注班次信息" + "\n\n" +
                    "班次名称：" + trainCourseDTO.getCourseName() + "\n\n" +
                    "所属项目：" + trainCourseDTO.getProjectCode() + " " + trainCourseDTO.getProjectName() + "\n\n" +
                    "班次实际执行日期：" + trainCourseDTO.getRealDateStr() + "\n\n" +
                    "执行地点：" + formatArea(newAreaStr) + "\n\n" +
                    "执行时段：" + newAddedExecuteUser.getExecuteDateStr() + "\n\n" +
                    "通知时间：" + DateUtils.format(new Date(), "yyyy/MM/dd HH:mm:ss") + "\n\n";
            DingTalkSendDTO dingTalkSendDTO = new DingTalkSendDTO();
            dingTalkSendDTO.setCourseId(trainCourseDTO.getId());
            dingTalkSendDTO.setTitle("【重要通知-人员分配提醒】");
            dingTalkSendDTO.setUserIds(Collections.singletonList(newAddedExecuteUser.getUserId().toString()));
            dingTalkSendDTO.setMdContent(addExecuteUserContent);
            dingTalkSendDTOS.add(dingTalkSendDTO);
            // 构建系统消息体
            String messageTitle = "你被分配为班次<span style=\"color: #007cff; cursor: pointer;\">《"+ trainCourseDTO.getCourseName()+"》</span>的执行人";
            String messageContent = "你被分配为班次《"+ trainCourseDTO.getCourseName()+"》的执行人，请关注班次信息，按时执行。";
            SystemMessageDTO systemMessageDTO = getSystemMessageDTO(newAddedExecuteUser.getUserId().toString(), Long.valueOf(trainCourseDTO.getId()), messageTitle, messageContent,MessageEnum.COURSE_ASSIGN_PERSON.getCode());
            systemMessageDTOS.add(systemMessageDTO);
        }
    }

    private void buildKeptUserCourseChangesMsg(TrainCourseSaveOrUpdateDTO trainCourseDTO, List<TrainCourseUserRelationDTO> newAddedExecuteUsers,  List<TrainCourseUserRelationDTO> newAddedSupportUsers, List<TrainCourseUserRelationDTO> removedExecuteUsers, List<TrainCourseUserRelationDTO> removedSupportUsers, Set<String> keptUserIds, List<DingTalkSendDTO> dingTalkSendDTOS, List<SystemMessageDTO> systemMessageDTOS) {
        StringBuilder counrseUserChangeContent = new StringBuilder();
        List<String> courseUserChanges = new ArrayList<>();
        counrseUserChangeContent.append("【人员变更提醒】").append("\n\n");
        counrseUserChangeContent.append("你原定负责的班次《").append(trainCourseDTO.getCourseName()).append("》人员发生变更。若有疑问请联系项目经理").append("\n\n");
        counrseUserChangeContent.append("班次名称：").append(trainCourseDTO.getCourseName()).append("\n\n");
        counrseUserChangeContent.append("所属项目：").append(trainCourseDTO.getProjectCode()).append(" ").append(trainCourseDTO.getProjectName()).append("\n\n");

        if (!newAddedExecuteUsers.isEmpty()) {
            courseUserChanges.add(String.format("新增执行人【%s】", getUserNameList(newAddedExecuteUsers)));
        }
        if (!newAddedSupportUsers.isEmpty()) {
            courseUserChanges.add(String.format("新增支援人【%s】", getUserNameList(newAddedSupportUsers)));
        }
        if (!removedExecuteUsers.isEmpty()) {
            courseUserChanges.add(String.format("移除执行人【%s】", getUserNameList(removedExecuteUsers)));
        }
        if (!removedSupportUsers.isEmpty()) {
            courseUserChanges.add(String.format("移除支援人【%s】", getUserNameList(removedSupportUsers)));
        }
        if (!courseUserChanges.isEmpty()) {
            counrseUserChangeContent.append("变更内容：\n\n");
            for (int i = 0; i < courseUserChanges.size(); i++) {
                counrseUserChangeContent.append(i + 1).append("、").append(courseUserChanges.get(i)).append("\n\n");
            }
        }
        counrseUserChangeContent.append("通知时间：").append(DateUtils.format(new Date(), "yyyy/MM/dd HH:mm:ss")).append("\n\n");
        if(!courseUserChanges.isEmpty() && !keptUserIds.isEmpty()){
            DingTalkSendDTO dingTalkSendDTO = new DingTalkSendDTO();
            dingTalkSendDTO.setCourseId(trainCourseDTO.getId());
            dingTalkSendDTO.setTitle("【人员变更提醒】");
            dingTalkSendDTO.setUserIds(new ArrayList<>(keptUserIds));
            dingTalkSendDTO.setMdContent(counrseUserChangeContent.toString());
            dingTalkSendDTOS.add(dingTalkSendDTO);
            // 构建系统消息体
            StringBuilder systemMessageContent = new StringBuilder();
            systemMessageContent.append("你原定负责的班次《").append(trainCourseDTO.getCourseName()).append("》人员发生变更。变更内容：\n\n");
            for (int i = 0; i < courseUserChanges.size(); i++) {
                systemMessageContent.append(i + 1).append("、").append(courseUserChanges.get(i)).append("\n\n");
            }
            String messageTitle = "你原定负责的班次<span style=\"color: #007cff; cursor: pointer;\">《" + trainCourseDTO.getCourseName() + "》</span>人员变更提醒";
            String messageContent = systemMessageContent.toString();
            for (String keptUserId : keptUserIds) {
                SystemMessageDTO systemMessageDTO = getSystemMessageDTO(keptUserId, Long.valueOf(trainCourseDTO.getId()), messageTitle, messageContent, MessageEnum.COURSE_PERSON_CHANGE.getCode());
                systemMessageDTOS.add(systemMessageDTO);
            }
        }
    }

    private void buildCourseChanges(TrainCourseSaveOrUpdateDTO trainCourseDTO, TrainCourseVO oldTrainCourseVO, List<String> courseChanges, String oldAreaStr, String newAreaStr) {
        // 检查班次执行日期变更
        if (!Objects.equals(trainCourseDTO.getRealStartDate(), oldTrainCourseVO.getRealStartDate()) ||
                !Objects.equals(trainCourseDTO.getRealEndDate(), oldTrainCourseVO.getRealEndDate())) {

            // 获取新旧日期范围的半日时间格式
            String oldPeriod = HalfDayTime.formatTimeRangesSafely(Collections.singletonList(new HalfDayTime[]{oldTrainCourseVO.getRealStartDate(), oldTrainCourseVO.getRealEndDate()}));
            String newPeriod = trainCourseDTO.getRealDateStr();

            courseChanges.add(String.format("班次执行日期由【%s】改为【%s】", oldPeriod, newPeriod));
        }

        // 检查班次执行地点变更
        if (!StringParseUtil.compareString(oldAreaStr, newAreaStr)) {
            courseChanges.add(String.format("班次执行地点由【%s】改为【%s】",
                    formatArea(oldAreaStr),
                    formatArea(newAreaStr)
            ));
        }

        // 检查班次执行组织变更
        String oldDeptStr = oldTrainCourseVO.getCourseArea() != null ?
                oldTrainCourseVO.getCourseArea().replace("-", "/") :
                null;

        String newDeptStr = trainCourseDTO.getCourseArea() != null ?
                trainCourseDTO.getCourseArea().replace("-", "/") :
                null;
        if (!StringParseUtil.compareString(oldDeptStr, newDeptStr)) {
            courseChanges.add(String.format("班次执行组织由【%s】改为【%s】",
                    oldDeptStr,
                    newDeptStr
            ));
        }

        // 检查班次形式变更
        String oldCourseForm = oldTrainCourseVO.getCourseForm() != null ? CourseFormEnum.getByCode(oldTrainCourseVO.getCourseForm()).getName() : null;
        String nowCourseForm = trainCourseDTO.getCourseForm() != null ? CourseFormEnum.getByCode(trainCourseDTO.getCourseForm()).getName() : null;
        if (!StringParseUtil.compareString(oldCourseForm, nowCourseForm)) {
            courseChanges.add(String.format("班次形式由【%s】改为【%s】",
                    oldCourseForm,
                    nowCourseForm
            ));
        }
    }

    private void buildKeptAndNotChangeUserMsg(TrainCourseSaveOrUpdateDTO trainCourseDTO, List<String> courseChanges, Set<String> keptAndNotChangeUserIdsUserIds, List<DingTalkSendDTO> dingTalkSendDTOS, List<SystemMessageDTO> systemMessageDTOS) {
        StringBuilder content = buildCourseChangesContent(
                buildCommonMessageHeader(trainCourseDTO),
                courseChanges
        );
        content.append("通知时间：").append(DateUtils.format(new Date(), "yyyy/MM/dd HH:mm:ss"));
        // 构建钉钉消息体
        DingTalkSendDTO dto = new DingTalkSendDTO();
        dto.setCourseId(trainCourseDTO.getId());
        dto.setTitle("【重要通知-班次重要信息变更提醒】");
        dto.setUserIds(new ArrayList<>(keptAndNotChangeUserIdsUserIds));
        dto.setMdContent(content.toString());
        dingTalkSendDTOS.add(dto);
        // 构建系统消息体
        String messageTitle = "班次<span style=\"color: #007cff; cursor: pointer;\">《" + trainCourseDTO.getCourseName() + "》</span>班次重要信息变更提醒";
        StringBuilder systemMessageContent = new StringBuilder();
        systemMessageContent.append("你负责的班次《").append(trainCourseDTO.getCourseName()).append("》的重要信息有变更，变更内容：\n\n");
        for (int i = 0; i < courseChanges.size(); i++) {
            systemMessageContent.append(i + 1).append("、").append(courseChanges.get(i)).append("\n\n");
        }
        systemMessageContent.append("请关注调整后的班次信息。");
        String messageContent = systemMessageContent.toString();
        for (String userId : keptAndNotChangeUserIdsUserIds) {
            SystemMessageDTO systemMessageDTO = getSystemMessageDTO(userId, Long.valueOf(trainCourseDTO.getId()), messageTitle, messageContent, MessageEnum.COURSE_INFO_CHANGE.getCode());
            systemMessageDTOS.add(systemMessageDTO);
        }
    }

    private void buildChangedUserMsg(TrainCourseSaveOrUpdateDTO trainCourseDTO, Set<String> changedUserIds, List<String> courseChanges, Map<String, TrainCourseUserRelationDTO> oldUsersMap, Map<String, TrainCourseUserRelationDTO> newUsersMap, List<DingTalkSendDTO> dingTalkSendDTOS, List<SystemMessageDTO> systemMessageDTOS) {
        for (String userId : changedUserIds) {
            StringBuilder content = buildCourseChangesContent(
                    buildCommonMessageHeader(trainCourseDTO),
                    courseChanges
            );

            // 计算时段变更的序号（班次变更数量+1）
            int timeChangeNumber = courseChanges.size() + 1;

            // 添加时段变更专属内容（带序号和格式化日期）
            TrainCourseUserRelationDTO oldUser = oldUsersMap.getOrDefault(userId, new TrainCourseUserRelationDTO());
            TrainCourseUserRelationDTO newUser = newUsersMap.getOrDefault(userId, new TrainCourseUserRelationDTO());

            content.append(timeChangeNumber).append("、")
                    .append("你的分配时段由【")
                    .append(oldUser.getExecuteDateStr())
                    .append("】改为【")
                    .append(newUser.getExecuteDateStr())
                    .append("】\n\n");

            content.append("通知时间：").append(DateUtils.format(new Date(), "yyyy/MM/dd HH:mm:ss"));
            // 构建钉钉消息体
            DingTalkSendDTO dingTalkSendDTO = new DingTalkSendDTO();
            dingTalkSendDTO.setCourseId(trainCourseDTO.getId());
            dingTalkSendDTO.setTitle("【重要通知-班次重要信息变更提醒】");
            dingTalkSendDTO.setUserIds(Collections.singletonList(userId));
            dingTalkSendDTO.setMdContent(content.toString());
            dingTalkSendDTOS.add(dingTalkSendDTO);
            // 构建系统消息体
            String messageTitle = "班次<span style=\"color: #007cff; cursor: pointer;\">《" + trainCourseDTO.getCourseName() + "》</span>班次重要信息变更提";
            StringBuilder systemMessageContent = new StringBuilder();
            systemMessageContent.append("你负责的班次《").append(trainCourseDTO.getCourseName()).append("》的重要信息有变更，变更内容：\n\n");
            for (int i = 0; i < courseChanges.size(); i++) {
                systemMessageContent.append(i + 1).append("、").append(courseChanges.get(i)).append("\n\n");
            }
            systemMessageContent.append(timeChangeNumber).append("、")
                    .append("你的分配时段由【")
                    .append(oldUser.getExecuteDateStr())
                    .append("】改为【")
                    .append(newUser.getExecuteDateStr())
                    .append("】\n\n");
            systemMessageContent.append("请关注调整后的班次信息。");
            String messageContent = systemMessageContent.toString();
            SystemMessageDTO systemMessageDTO = getSystemMessageDTO(userId, Long.valueOf(trainCourseDTO.getId()), messageTitle, messageContent, MessageEnum.COURSE_INFO_CHANGE.getCode());
            systemMessageDTOS.add(systemMessageDTO);
        }
    }

    // 1. 公共消息头构建方法
    private StringBuilder buildCommonMessageHeader(TrainCourseSaveOrUpdateDTO dto) {
        StringBuilder header = new StringBuilder();
        header.append("<font color=\"#FF0000\">【重要通知-班次重要信息变更提醒】</font>").append("\n\n")
                .append("班次名称：").append(dto.getCourseName()).append("\n\n")
                .append("所属项目：").append(dto.getProjectCode())
                .append(" ").append(dto.getProjectName()).append("\n\n");
        return header;
    }

    // 2. 构建班次变更内容（带序号）
    private StringBuilder buildCourseChangesContent(StringBuilder header, List<String> courseChanges) {
        StringBuilder content = new StringBuilder(header);
        if (!courseChanges.isEmpty()) {
            content.append("重要信息变更内容：\n\n");
            for (int i = 0; i < courseChanges.size(); i++) {
                content.append(i + 1).append("、").append(courseChanges.get(i)).append("\n\n");
            }
        }
        return content;
    }



    private String getUserNameList(List<TrainCourseUserRelationDTO> newAddedExecuteUsers) {
        if (CollectionUtils.isEmpty(newAddedExecuteUsers)) {
            return "";
        }
        return newAddedExecuteUsers.stream()
               .map(TrainCourseUserRelationDTO::getUserName)
               .collect(Collectors.joining(";"));
    }

    // 将新旧人员分别合并成一个map，并去重处理
    private static void buildOldAndNewMap(List<TrainCourseUserRelationDTO> mergedOldUsers, List<TrainCourseUserRelationDTO> mergedNewUsers, Map<String, TrainCourseUserRelationDTO> oldUsersMap,Map<String, TrainCourseUserRelationDTO> newUsersMap) {
        for (TrainCourseUserRelationDTO dto : mergedOldUsers) {
            oldUsersMap.putIfAbsent(dto.getUserId().toString(), dto);
        }
        for (TrainCourseUserRelationDTO dto : mergedNewUsers) {
            newUsersMap.putIfAbsent(dto.getUserId().toString(), dto);
        }
    }

    private Set<String> findKeptUserIds(List<TrainCourseUserRelationDTO> oldList,
                                        List<TrainCourseUserRelationDTO> newList) {
        if (CollectionUtils.isEmpty(oldList) || CollectionUtils.isEmpty(newList)) {
            return Collections.emptySet();
        }

        Set<Long> oldUserIds = oldList.stream()
                .map(TrainCourseUserRelationDTO::getUserId)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());

        return newList.stream()
                .map(TrainCourseUserRelationDTO::getUserId)
                .filter(userId -> userId != null && oldUserIds.contains(userId))
                .map(String::valueOf)
                .collect(Collectors.toSet());
    }

    // 获取时段变更的用户ID
    private Set<String> findUserTimeChanges(List< TrainCourseUserRelationDTO> oldList, List< TrainCourseUserRelationDTO> newList) {

        Set<String> changedIds = new HashSet<>();
        if (CollectionUtils.isEmpty(oldList) && CollectionUtils.isEmpty(newList)) {
            return changedIds;
        }
        // 转换为Map
        Map<Long, TrainCourseUserRelationDTO> oldMap = toUserMap(oldList);
        Map<Long, TrainCourseUserRelationDTO> newMap = toUserMap(newList);

        // 检查用户时段变更
        for (Long userId : oldMap.keySet()) {
            if (newMap.containsKey(userId)) {
                TrainCourseUserRelationDTO oldUser = oldMap.get(userId);
                TrainCourseUserRelationDTO newUser = newMap.get(userId);
                if (!StringParseUtil.compareString(oldUser.getExecuteDateStr(), newUser.getExecuteDateStr())) {
                    changedIds.add(userId.toString());
                }
            }
        }
        return changedIds;
    }

    // 辅助方法：用户列表转Map
    private Map<Long, TrainCourseUserRelationDTO> toUserMap(List<TrainCourseUserRelationDTO> list) {
        if (CollectionUtils.isEmpty(list)) return Collections.emptyMap();
        return list.stream().collect(Collectors.toMap(TrainCourseUserRelationDTO::getUserId, Function.identity()));
    }


    // 辅助方法：格式化地区
    private String formatArea(String area) {
        if (StringUtils.isEmpty(area)) return "无";
        return area;
    }


    private static String getCourseSiteListStr(List<TrainCourseAreaRelationSaveOrUpdateDTO> saveOrUpdateDTOS,
                                               List<TrainCourseAreaRelationDTO> areaRelationDTOS) {

        if(CollectionUtils.isEmpty(saveOrUpdateDTOS) && CollectionUtils.isEmpty(areaRelationDTOS)){
            return "";
        }
        // 处理第一个集合的流
        Stream<String> firstStream = saveOrUpdateDTOS != null ?
                saveOrUpdateDTOS.stream()
                        .filter(Objects::nonNull)
                        .map(dto -> String.join("/",
                                dto.getProvince() != null ? dto.getProvince() : "",
                                dto.getCity() != null ? dto.getCity() : "",
                                dto.getArea() != null ? dto.getArea() : ""))
                : Stream.empty();

        // 处理第二个集合的流
        Stream<String> secondStream = areaRelationDTOS != null ?
                areaRelationDTOS.stream()
                        .filter(Objects::nonNull)
                        .map(dto -> String.join("/",
                                dto.getProvince() != null ? dto.getProvince() : "",
                                dto.getCity() != null ? dto.getCity() : "",
                                dto.getArea() != null ? dto.getArea() : ""))
                : Stream.empty();

        // 合并两个流
        Stream<String> combinedStream = Stream.concat(firstStream, secondStream);

        return combinedStream.collect(Collectors.joining(";"));
    }

    private void processAllAreaList(TrainCourseSaveOrUpdateDTO trainCourseDTO, TrainCourseDOWithBLOBs trainCourseDO) {
        List<TrainCourseAreaRelationSaveOrUpdateDTO> courseAreaList = trainCourseDTO.getCourseAreaList();
        List<TrainCourseAreaRelationSaveOrUpdateDTO> courseStudentAreaList = trainCourseDTO.getCourseStudentAreaList();
        List<TrainCourseAreaRelationSaveOrUpdateDTO> allAreaList = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(courseAreaList)){
            courseAreaList.forEach(area -> area.setRelationType(TrainCourseAreaRelationTypeEnum.EXECUTE.getCode()));
            allAreaList.addAll(courseAreaList);
        }
        if(CollectionUtils.isNotEmpty(courseStudentAreaList)){
            courseStudentAreaList.forEach(area -> area.setRelationType(TrainCourseAreaRelationTypeEnum.STUDENT.getCode()));
            allAreaList.addAll(courseStudentAreaList);
        }
        trainCourseAreaRelationService.saveOrUpdateTrainAreaRelation(trainCourseDO.getProjectId(),
                trainCourseDO.getId(), allAreaList);
    }

    private TrainCourseDOWithBLOBs saveDTO2DO(TrainCourseSaveOrUpdateDTO trainCourseDTO) {
        TrainCourseDOWithBLOBs trainCourseDO = new TrainCourseDOWithBLOBs();
        BeanUtils.copyProperties(trainCourseDTO, trainCourseDO);
        if(StringUtils.isNotEmpty(trainCourseDTO.getPlanDesc())){
            trainCourseDO.setHasPlanDesc(true);
        }
        if(StringUtils.isNotEmpty(trainCourseDTO.getExecutionProgress())){
            trainCourseDO.setHasExecutionProgress(true);
        }
        trainCourseDO.setPlanStartDate(trainCourseDTO.getPlanStartDate() == null ? null : trainCourseDTO.getPlanStartDate().toFullDate());
        trainCourseDO.setPlanEndDate(trainCourseDTO.getPlanEndDate() == null ? null : trainCourseDTO.getPlanEndDate().toFullDate());
        trainCourseDO.setRealStartDate(trainCourseDTO.getRealStartDate() == null ? null : trainCourseDTO.getRealStartDate().toFullDate());
        trainCourseDO.setRealEndDate(trainCourseDTO.getRealEndDate() == null ? null : trainCourseDTO.getRealEndDate().toFullDate());
        //判断班次状态
        HalfDayTime now = new HalfDayTime(new Date());
        if(trainCourseDTO.getRealStartDate().isAfter(now)){
            trainCourseDO.setCourseStatus(TrainCourseStatusEnum.NOT_STARTED.getCode());
        }else if(trainCourseDTO.getRealEndDate().isBefore(now)){
            trainCourseDO.setCourseStatus(TrainCourseStatusEnum.COMPLETED.getCode());
        }else{
            trainCourseDO.setCourseStatus(TrainCourseStatusEnum.IN_PROGRESS.getCode());
        }
        return trainCourseDO;
    }

    /**
     * 根据项目ID列表查询班次列表
     * 
     * @param projectIds 项目ID列表
     * @return 班次列表
     */
    public List<TrainCourseVO> listByProjectIds(List<Integer> projectIds) {
        if (CollectionUtils.isEmpty(projectIds)) {
            return new ArrayList<>();
        }
        List<TrainCourseDO> trainCourseDOS = trainCourseDOMapperEx.selectByProjectIds(projectIds);
        return trainCourseDOS.stream().map(this::do2vo).collect(Collectors.toList());
    }

    private TrainCourseVO do2vo(TrainCourseDO trainCourseDO) {
        TrainCourseVO trainCourseVO =  new TrainCourseVO();
        BeanUtils.copyProperties(trainCourseDO, trainCourseVO);
        trainCourseVO.setPlanStartDate(new HalfDayTime(trainCourseDO.getPlanStartDate()));
        trainCourseVO.setPlanEndDate(new HalfDayTime(trainCourseDO.getPlanEndDate()));
        trainCourseVO.setRealStartDate(new HalfDayTime(trainCourseDO.getRealStartDate()));
        trainCourseVO.setRealEndDate(new HalfDayTime(trainCourseDO.getRealEndDate()));

        RightLevelEnum rightLevel = trainUserService.getRightLevel(UserRequestContextHolder.getRequestUserId(), UserRequestContextHolder.getRequestManageDeptId(), trainCourseVO.getCourseAreaId());
        trainCourseVO.setRightLevel(rightLevel);
        return trainCourseVO;
    }
    private List<TrainCourseUserRelationDTO> saveDTO2DTO(List<TrainCourseUserRelationSaveOrUpdateDTO> saveOrUpdateDTOS) {
        List<TrainCourseUserRelationDTO> dtoList = new ArrayList<>();
        if(CollectionUtils.isEmpty(saveOrUpdateDTOS)){
            return dtoList;
        }
        saveOrUpdateDTOS.forEach(dto -> {
            TrainCourseUserRelationDTO relationDTO = new TrainCourseUserRelationDTO();
            BeanUtils.copyProperties(dto, relationDTO);
            dtoList.add(relationDTO);
        });
        return dtoList;
    }


    /**
     * 分页查询班次列表
     * 
     * @param query
     * @return
     */
    public Pagination<TrainCourseVO> pageCourse(TrainCourseQueryDTO query) {
        Pagination<TrainCourseVO> pagination = new Pagination<>();
        pagination.setPageIndex(query.getPageIndex());
        pagination.setPageSize(query.getPageSize());
        long total = trainCourseDOMapperEx.countByQuery(query);
        pagination.setTotal(total);
        if (total == 0) {
            pagination.setRows(new ArrayList<>());
            return pagination;
        }
        List<TrainCourseDO> trainCourseDOS = trainCourseDOMapperEx.selectByQuery(query);
        List<TrainCourseVO> trainCourseVOS = trainCourseDOS.stream().map(this::do2vo).collect(Collectors.toList());
        processRelations(trainCourseVOS);
        processCheckTask(trainCourseVOS);
        pagination.setRows(trainCourseVOS);
        return pagination;
    }

    private void processCheckTask(List<TrainCourseVO> trainCourseVOS) {
        List<Integer> courseIds = trainCourseVOS.stream().map(TrainCourseVO::getId).collect(Collectors.toList());
        TrainCheckTaskQueryDTO trainCheckTaskQueryDTO = new TrainCheckTaskQueryDTO();
        trainCheckTaskQueryDTO.setCheckStatus(CheckSummaryStatusEnum.NORMAL.getCode());
        trainCheckTaskQueryDTO.setCourseIds(courseIds);
        List<TrainCheckTaskDTO> trainCheckTaskDOS = trainCheckTaskService.listByQuery(trainCheckTaskQueryDTO);
        Map<Integer, List<HalfDayTimeWithUser>> halfDayTimeWithUserMap = trainCheckTaskDOS.stream().map(this::task2halfDayTimeWithUser).flatMap(List::stream).distinct().collect(Collectors.groupingBy(HalfDayTimeWithUser::getCourseId));
        trainCourseVOS.forEach(trainCourseVO -> {
            List<HalfDayTimeWithUser> halfDayTimeWithUserList = halfDayTimeWithUserMap.getOrDefault(trainCourseVO.getId(), new ArrayList<>());
            trainCourseVO.setActualUserDays(halfDayTimeWithUserList.size()/2f);
        });
    }

    private List<HalfDayTimeWithUser> task2halfDayTimeWithUser(TrainCheckTaskDTO trainCheckTaskDTO) {
        List<HalfDayTimeWithUser> halfDayTimeWithUserList = new ArrayList<>();
        if(trainCheckTaskDTO.getCheckDimension() != CheckDimensionEnum.AFTERNOON.getCode()){
            HalfDayTimeWithUser halfDayTimeWithUser = new HalfDayTimeWithUser();
            halfDayTimeWithUser.setHalfDayTime(new HalfDayTime(trainCheckTaskDTO.getCheckDate(), HalfDayEnum.AM));
            halfDayTimeWithUser.setUserId(trainCheckTaskDTO.getUserId());
            halfDayTimeWithUser.setCourseId(trainCheckTaskDTO.getCourseId());
            halfDayTimeWithUserList.add(halfDayTimeWithUser);
        }
        if(trainCheckTaskDTO.getCheckDimension() != CheckDimensionEnum.MORNING.getCode()){
            HalfDayTimeWithUser halfDayTimeWithUser = new HalfDayTimeWithUser();
            halfDayTimeWithUser.setUserId(trainCheckTaskDTO.getUserId());
            halfDayTimeWithUser.setHalfDayTime(new HalfDayTime(trainCheckTaskDTO.getCheckDate(), HalfDayEnum.PM));
            halfDayTimeWithUser.setCourseId(trainCheckTaskDTO.getCourseId());
            halfDayTimeWithUserList.add(halfDayTimeWithUser);
        }
        return halfDayTimeWithUserList;
    }

    private void processRelations(List<TrainCourseVO> trainCourseVOS) {
        List<Integer> courseIds = trainCourseVOS.stream().map(TrainCourseVO::getId).collect(Collectors.toList());
        List<TrainCourseUserRelationDTO> trainCourseUserRelationDOS = trainCourseUserRelationService.listByCourseIds(courseIds);
        Map<Integer, List<TrainCourseUserRelationDTO>> trainCourseUserRelationMap = trainCourseUserRelationDOS.stream().collect(Collectors.groupingBy(TrainCourseUserRelationDTO::getCourseId));
        List<TrainCourseAreaRelationDTO> trainCourseAreaRelationDOS = trainCourseAreaRelationService.listByCourseIds(courseIds);
        trainCourseAreaRelationMap = trainCourseAreaRelationDOS.stream().collect(Collectors.groupingBy(TrainCourseAreaRelationDTO::getCourseId));
        trainCourseVOS.forEach(trainCourseVO -> {
            List<TrainCourseUserRelationDTO> trainCourseUserRelationDTOList = trainCourseUserRelationMap.getOrDefault(trainCourseVO.getId(), new ArrayList<>());
            trainCourseUserRelationDTOList.forEach(relation -> processCourseRelationOver(relation, trainCourseVO.getRealStartDate(), trainCourseVO.getRealEndDate()));
            trainCourseVO.setExecuteUsers(trainCourseUserRelationDTOList.stream().filter(trainCourseUserRelationDO -> trainCourseUserRelationDO.getRelationType() == TrainCourseUserRelationTypeEnum.EXECUTE.getCode()).collect(Collectors.toList()));
            trainCourseVO.setSupportUsers(trainCourseUserRelationDTOList.stream().filter(trainCourseUserRelationDO -> trainCourseUserRelationDO.getRelationType() == TrainCourseUserRelationTypeEnum.SUPPORT.getCode()).collect(Collectors.toList()));
            List<TrainCourseAreaRelationDTO> trainCourseAreaRelationDTOList = trainCourseAreaRelationMap.getOrDefault(trainCourseVO.getId(), new ArrayList<>());
            trainCourseVO.setCourseAreaList(trainCourseAreaRelationDTOList.stream().filter(relation -> relation.getRelationType() == TrainCourseAreaRelationTypeEnum.EXECUTE.getCode()).collect(Collectors.toList()));
            trainCourseVO.setCourseStudentAreaList(trainCourseAreaRelationDTOList.stream().filter(relation -> relation.getRelationType() == TrainCourseAreaRelationTypeEnum.STUDENT.getCode()).collect(Collectors.toList()));
        });
    }

    private void processCourseRelationOver(TrainCourseUserRelationDTO relationDTO, HalfDayTime realStartDate, HalfDayTime realEndDate) {
        boolean isOverRealExecuteDate = relationDTO.getStartDate().isBefore(realStartDate) || relationDTO.getEndDate().isAfter(realEndDate);
        relationDTO.setIsOverRealExecuteDate(isOverRealExecuteDate);
    }

    public TrainCourseVO getById(Integer courseId) {
        TrainCourseDOWithBLOBs trainCourseDO = trainCourseDOMapperEx.selectByPrimaryKey(courseId);
        if (trainCourseDO == null) {
            return null;
        }
        TrainCourseVO trainCourseVO = do2vo(trainCourseDO);
        DictMappingVO courseTypeDict = dictMappingService.getByTypeAndDictCode("course_type", String.valueOf(trainCourseDO.getCourseType()));
        trainCourseVO.setCourseTypeName(courseTypeDict.getDictValue());
        processRelations(Arrays.asList(trainCourseVO));
        return trainCourseVO;
    }

    public void preCheckSaveOrUpdate(TrainCourseSaveOrUpdateDTO trainCourseDTO) {
        // if (trainCourseDTO.getPlanStartDate().isAfter(trainCourseDTO.getPlanEndDate())) {
        //     throw ExceptionUtil.businessException(ManagementExceptionEnum.PLAN_START_DATE_AFTER_END_DATE);
        // }
        // if (trainCourseDTO.getRealStartDate().isAfter(trainCourseDTO.getRealEndDate())) {
        //     throw ExceptionUtil.businessException(ManagementExceptionEnum.REAL_START_DATE_AFTER_END_DATE);
        // }
        if(trainCourseDTO.getId() == null){
            return;
        }
        TrainCourseVO trainCourseVO = getById(trainCourseDTO.getId());
        if(trainCourseVO == null){
            throw ExceptionUtil.businessException(ManagementExceptionEnum.COURSE_NOT_EXIST);
        }
        //TODO 校验是否可编辑
        return;
    }

    public TrainCourseVO preCheckDelete(Integer courseId) {
        TrainCourseVO trainCourseVO = getById(courseId);
        if(trainCourseVO == null){
            throw ExceptionUtil.businessException(ManagementExceptionEnum.COURSE_NOT_EXIST);
        }
        return trainCourseVO;
    }

    /**
     * 删除班次
     * 
     * @param courseId 班次ID
     */
    @Transactional(rollbackFor = Exception.class)
    public void delete(Integer courseId) {
        deleteCourseSendMsg(courseId);
        trainCourseDOMapperEx.deleteByPrimaryKey(courseId);
        trainCourseUserRelationService.invalidateUserRelation(courseId, null);
        trainCourseAreaRelationService.deleteByCourseId(courseId);
        trainTaskService.deleteByCourseId(courseId);
    }

    private void deleteCourseSendMsg(Integer courseId) {
        try {
            TrainCourseVO trainCourseVO = getById(courseId);
            List<DingTalkSendDTO> dingTalkSendDTOS = new ArrayList<>();
            List<SystemMessageDTO> systemMessageDTOS = new ArrayList<>();
            if (trainCourseVO != null) {
                log.info("班次删除发送消息构建消息体, courseId:{}, courseName:{}", courseId, trainCourseVO.getCourseName());
                List<TrainCourseUserRelationDTO> executeUsers = trainCourseVO.getExecuteUsers();
                List<TrainCourseUserRelationDTO> supportUsers = trainCourseVO.getSupportUsers();
                Set<Long> userIds = Stream.concat(executeUsers.stream().map(TrainCourseUserRelationDTO::getUserId), supportUsers.stream().map(TrainCourseUserRelationDTO::getUserId)).collect(Collectors.toSet());

                if (CollectionUtils.isNotEmpty(userIds)) {
                    List<String> userIdStrings = userIds.stream()
                            .map(String::valueOf)
                            .collect(Collectors.toList());
                    String addExecuteUserContent = "<font color=\"#FF0000\">【重要通知-班次删除提醒】</font>" + "\n\n" +
                            "你原负责的班次已被删除,请注意排班变动。如有疑问请联系项目经理" + "\n\n" +
                            "原班次名称：" + trainCourseVO.getCourseName() + "\n\n" +
                            "所属项目：" + trainCourseVO.getProjectCode() + " " + trainCourseVO.getProjectName() + "\n\n" +
                            "通知时间：" + DateUtils.format(new Date(), "yyyy/MM/dd HH:mm:ss") + "\n\n";
                    DingTalkSendDTO dingTalkSendDTO = new DingTalkSendDTO();
                    dingTalkSendDTO.setCourseId(trainCourseVO.getId());
                    dingTalkSendDTO.setTitle("【重要通知-班次删除提醒】");
                    dingTalkSendDTO.setUserIds(userIdStrings);
                    dingTalkSendDTO.setMdContent(addExecuteUserContent);
                    dingTalkSendDTOS.add(dingTalkSendDTO);
                    // 构建系统消息体
                    for (Long userId : userIds) {
                        String messageTitle = "班次<span style=\"color: #007cff; cursor: pointer;\">《【" + trainCourseVO.getCourseName() + "】》</span>已被删除";
                        String messageContent = "你原负责的班次【" + trainCourseVO.getCourseName() + "】已被删除，请注意排班变动。如有疑问请联系项目经理。";
                        SystemMessageDTO systemMessageDTO = getSystemMessageDTO(userId.toString(), Long.valueOf(trainCourseVO.getId()), messageTitle, messageContent, MessageEnum.COURSE_DELETE.getCode());
                        systemMessageDTOS.add(systemMessageDTO);
                    }
                }
                saveAndSendMessages(systemMessageDTOS, dingTalkSendDTOS);
            }
        } catch (Exception e) {
            log.info("班次删除发送消息失败, courseId:{}, courseName:{}", courseId, e.getMessage(), e);
        }
    }



    /**
     * 根据班次ID列表查询班次列表
     * 
     * @param courseIds 班次ID列表
     * @param withBlobs 是否包含BLOB字段
     * @return 班次列表
     */
    public List<TrainCourseVO> listByIds(List<Integer> courseIds, boolean withBlobs) {
        if (CollectionUtils.isEmpty(courseIds)) {
            return new ArrayList<>();
        }
        List<? extends TrainCourseDO> trainCourseDOS = withBlobs ? 
            trainCourseDOMapperEx.selectBlobByPrimaryKeys(courseIds) : 
            trainCourseDOMapperEx.selectByPrimaryKeys(courseIds);
        List<DictMappingVO> courseTypeDictList = dictMappingService.getDictListByDictType("course_type");
        Map<String, String> courseTypeDictMap = courseTypeDictList.stream().collect(Collectors.toMap(DictMappingVO::getDictCode, DictMappingVO::getDictValue));
        return trainCourseDOS.stream().map(trainCourseDO -> {
            TrainCourseVO trainCourseVO = do2vo(trainCourseDO);
            trainCourseVO.setCourseTypeName(courseTypeDictMap.getOrDefault(String.valueOf(trainCourseDO.getCourseType()), ""));
            return trainCourseVO;
        }).collect(Collectors.toList());
    }

    /**
     * 处理班次状态
     */
    @Transactional(rollbackFor = Exception.class)
    public void processStatus() {
        trainCourseDOMapperEx.processCompleteStatus();
        trainCourseDOMapperEx.processStartStatus();
    }

    public ProjectStatusSummaryVO getCourseStatusSummary(TrainCourseQueryDTO query) {
        return trainCourseDOMapperEx.getCourseStatusSummary(query);
    }

    /**
     * 获取跨区支持班次涉及的区域ID列表
     * @param excludeCourseAreaIds 需要排除的班次区域ID列表
     * @return 区域ID列表
     */
    public List<String> getCrossAreaIds(List<String> excludeCourseAreaIds, String userId) {
        return trainCourseDOMapperEx.selectCrossAreaIds(excludeCourseAreaIds, userId);
    }


    /**
     * 提前一天发送班次开始消息
     */
    public void sendMsgBeforeStart() {
        log.info("提前一天发送班次次日开始消息");
        TrainCourseQueryDTO query = new TrainCourseQueryDTO();
        query.setStartTime(DateUtil.beginOfDay(DateUtil.tomorrow()));
        query.setEndTime(DateUtil.endOfDay(DateUtil.tomorrow()));
        query.setCourseStatus(TrainCourseStatusEnum.NOT_STARTED.getCode());
        List<TrainCourseDO> trainCourseDOS = trainCourseDOMapperEx.selectByQuery(query);

        if (CollectionUtils.isEmpty(trainCourseDOS)) {
            log.info("没有需要提前一天发送班次开始消息的班次");
            return;
        }

        List<TrainCourseVO> trainCourseVOS = trainCourseDOS.stream().map(this::do2vo).collect(Collectors.toList());
        processRelations(trainCourseVOS);
        List<DingTalkSendDTO> dingTalkSendDTOS = new ArrayList<>();
        List<SystemMessageDTO> systemMessageDTOs = new ArrayList<>();

        List<Integer> projectIds = trainCourseVOS.stream().map(TrainCourseVO::getProjectId).distinct().collect(Collectors.toList());
        List<TrainProjectDO> trainProjectDOS = trainProjectMapperEx.selectByIds(projectIds);
        Map<Integer, TrainProjectDO> trainProjectMap = trainProjectDOS.stream().collect(Collectors.toMap(TrainProjectDO::getId, Function.identity()));
        // 构建消息体
        for (TrainCourseVO trainCourseVO : trainCourseVOS) {
            // 执行人
            List<TrainCourseUserRelationDTO> newAddedExecuteUsers = trainCourseVO.getExecuteUsers();
            // 支援人
            List<TrainCourseUserRelationDTO> newAddedSupportUsers = trainCourseVO.getSupportUsers();
            List<TrainCourseUserRelationDTO> allUsers = new ArrayList<>(Stream.concat(newAddedExecuteUsers.stream(), newAddedSupportUsers.stream())
                    .collect(Collectors.toMap(
                            TrainCourseUserRelationDTO::getUserId,
                            Function.identity(),
                            (existing, replacement) -> existing
                    )).values());
            String allUserNames = allUsers.stream().map(TrainCourseUserRelationDTO::getUserName).collect(Collectors.joining(","));
            // 获取新旧日期范围的半日时间格式
            String coursePeriod = HalfDayTime.formatTimeRangesSafely(Collections.singletonList(new HalfDayTime[]{trainCourseVO.getRealStartDate(), trainCourseVO.getRealEndDate()}));
            // 项目经理通知
            TrainProjectDO projectDO = trainProjectMap.get(trainCourseVO.getProjectId());
            if (projectDO != null && projectDO.getProjectManagerId() != null) {
                TrainProjectDO trainProjectDO = trainProjectMap.get(trainCourseVO.getProjectId());
                String managerId = StringUtils.substring(trainProjectDO.getProjectManagerId(), 5);
                String addProjectManagerContent = "【班次开始提醒】" + "\n\n" +
                        "班次将于明日开始执行，请关注执行安排" + "\n\n" +
                        "班次名称：" + trainCourseVO.getCourseName() + "\n\n" +
                        "所属项目：" + trainCourseVO.getProjectCode() + " " + trainCourseVO.getProjectName() + "\n\n" +
                        "班次实际执行日期：" + coursePeriod + "\n\n" +
                        "执行地点：" + formatArea(getCourseSiteListStr(null, trainCourseVO.getCourseAreaList())) + "\n\n" +
                        "执行人+支援人：" + allUserNames + "\n\n" +
                        "通知时间：" + DateUtils.format(new Date(), "yyyy/MM/dd HH:mm:ss") + "\n\n";
                DingTalkSendDTO dingTalkSendDTO = new DingTalkSendDTO();
                dingTalkSendDTO.setCourseId(trainCourseVO.getId());
                dingTalkSendDTO.setTitle("【班次开始提醒】");
                dingTalkSendDTO.setUserIds(Collections.singletonList(managerId));
                dingTalkSendDTO.setMdContent(addProjectManagerContent);
                dingTalkSendDTOS.add(dingTalkSendDTO);
                // 构建系统消息体
                String messageTitle = "班次<span style=\"color: #007cff; cursor: pointer;\">《"+trainCourseVO.getCourseName()+"》</span>将于明日开始执行";
                String messageContent = "你管理区域内班次【"+trainCourseVO.getCourseName()+"】将于明日开始执行，所属项目【"+trainCourseVO.getProjectName()+"】请关注执行安排";
                SystemMessageDTO systemMessageDTO = getSystemMessageDTO(managerId, Long.valueOf(trainCourseVO.getId()), messageTitle, messageContent,MessageEnum.COURSE_START.getCode());
                systemMessageDTOs.add(systemMessageDTO);
            }

            // 执行人+支援人通知
            if (!allUsers.isEmpty()) {
                Map<Long, List<HalfDayTime[]>> allUserTimeSlots = getUserTimeSlots(null,allUsers);
                for (TrainCourseUserRelationDTO user : allUsers) {
                    List<HalfDayTime[]> slotsOrDefault = allUserTimeSlots.getOrDefault(user.getUserId(), new ArrayList<>());
                    String timeSlotsStr = HalfDayTime.formatTimeRangesSafely(slotsOrDefault);
                    String addUserContent = "【班次执行提醒】" + "\n\n" +
                            "班次将于明日开始执行，请关注执行安排" + "\n\n" +
                            "班次名称：" + trainCourseVO.getCourseName() + "\n\n" +
                            "所属项目：" + trainCourseVO.getProjectCode() + " " + trainCourseVO.getProjectName() + "\n\n" +
                            "班次实际执行日期：" + coursePeriod + "\n\n" +
                            "执行地点：" + formatArea(getCourseSiteListStr(null, trainCourseVO.getCourseAreaList())) + "\n\n" +
                            "执行时段：" + timeSlotsStr + "\n\n" +
                            "通知时间：" + DateUtils.format(new Date(), "yyyy/MM/dd HH:mm:ss") + "\n\n";
                    DingTalkSendDTO dingTalkSendDTO = new DingTalkSendDTO();
                    dingTalkSendDTO.setCourseId(trainCourseVO.getId());
                    dingTalkSendDTO.setTitle("【班次执行提醒】");
                    dingTalkSendDTO.setUserIds(Collections.singletonList(user.getUserId().toString()));
                    dingTalkSendDTO.setMdContent(addUserContent);
                    dingTalkSendDTOS.add(dingTalkSendDTO);
                    // 构建系统消息体
                    String messageTitle = "班次<span style=\"color: #007cff; cursor: pointer;\">《"+trainCourseVO.getCourseName()+"》</span>将于明日开始执行";
                    String messageContent = "你负责的班次【"+trainCourseVO.getCourseName()+"】将于明日开始执行，请关注执行安排";
                    SystemMessageDTO systemMessageDTO = getSystemMessageDTO(user.getUserId().toString(), Long.valueOf(trainCourseVO.getId()), messageTitle, messageContent,MessageEnum.COURSE_START.getCode());
                    systemMessageDTOs.add(systemMessageDTO);
                }
            }
        }
        saveAndSendMessages(systemMessageDTOs, dingTalkSendDTOS);
    }

    private static SystemMessageDTO getSystemMessageDTO(String userId, Long businessId, String title, String content, Integer messageType) {
        SystemMessageDTO systemMessageDTO = new SystemMessageDTO();
        if (StringUtils.isEmpty(userId)) {
            systemMessageDTO.setUserId(0L);
        } else {
            systemMessageDTO.setUserId(Long.valueOf(userId));
        }
        systemMessageDTO.setMessageType(messageType);
        systemMessageDTO.setMessageTitle(title);
        systemMessageDTO.setMessageContent(content);
        systemMessageDTO.setIsRead(0);
        systemMessageDTO.setBusinessId(businessId);
        systemMessageDTO.setBusinessType(1);
        systemMessageDTO.setGmtCreate(new Date());
        return systemMessageDTO;
    }


}
