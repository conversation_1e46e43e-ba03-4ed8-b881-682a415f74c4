package com.dt.train.management.center.web.fetch.project;

import java.util.List;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.dt.framework.business.constant.DtHeaders;
import com.dt.framework.business.dto.page.Pagination;
import com.dt.framework.business.dto.web.common.WebResult;
import com.dt.train.management.center.enums.TrainTaskTypeEnum;
import com.dt.train.management.center.manager.TrainTaskManager;
import com.dt.train.management.center.model.dto.project.TrainCourseQueryDTO;
import com.dt.train.management.center.model.dto.task.TrainTaskQueryDTO;
import com.dt.train.management.center.model.vo.project.ProjectStatusSummaryVO;
import com.dt.train.management.center.model.vo.project.TrainCourseVO;
import com.dt.train.management.center.model.vo.task.TrainTaskWithUserVO;
import com.dt.train.management.center.service.cache.DeptCacheService;
import com.dt.train.management.center.service.project.TrainCourseService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;

@RestController
@RequestMapping("/fetch/course")
@Api(tags = "班次获取")
public class TrainCourseController {

    private final TrainCourseService trainCourseService;

    private final TrainTaskManager trainTaskManager;

    private final DeptCacheService deptCacheService;

    public TrainCourseController(TrainCourseService trainCourseService, TrainTaskManager trainTaskManager, DeptCacheService deptCacheService) {
        this.trainCourseService = trainCourseService;
        this.trainTaskManager = trainTaskManager;
        this.deptCacheService = deptCacheService;
    }

    @GetMapping
    @ApiOperation("分页获取班次列表")
    public WebResult<Pagination<TrainCourseVO>> page(TrainCourseQueryDTO query, @RequestHeader(DtHeaders.USER_ID) Long userId) {
        buildChildrenAreaIds(query);
        return WebResult.successData(trainCourseService.pageCourse(query));
    }

    @GetMapping("/cross")
    @ApiOperation("分页获取跨区域支持班次列表")
    public WebResult<Pagination<TrainCourseVO>> pageCross(TrainCourseQueryDTO query, @RequestHeader(DtHeaders.USER_ID) Long userId) {
        buildExcludeChildrenAreaIds(query);
        buildChildrenAreaIds(query);
        return WebResult.successData(trainCourseService.pageCourse(query));
    }

    @GetMapping("/detail")
    @ApiOperation("获取班次详情")
    public WebResult<TrainCourseVO> detail(@ApiParam("班次ID") @RequestParam(required = true) Integer courseId) {
        return WebResult.successData(trainCourseService.getById(courseId));
    }

    @GetMapping("/task/detail")
    @ApiOperation("获取班次任务详情")
    public WebResult<TrainTaskWithUserVO> taskDetail(@ApiParam("班次ID") @RequestParam(required = true) Integer courseId) {
        TrainTaskQueryDTO queryDTO = new TrainTaskQueryDTO();
        queryDTO.setCourseId(courseId);
        queryDTO.setTaskType(TrainTaskTypeEnum.CHECK_IN.getCode());
        return WebResult.successData(trainTaskManager.getTaskWithUser(queryDTO));
    }

    @ApiOperation("获取班次状态")
    @GetMapping("/status/summary")
    public WebResult<ProjectStatusSummaryVO> getCourseStatusSummary(TrainCourseQueryDTO query) {
        buildChildrenAreaIds(query);
        return WebResult.successData(trainCourseService.getCourseStatusSummary(query));
    }

    @ApiOperation("获取跨区域支持班次状态")
    @GetMapping("/status/summary/cross")
    public WebResult<ProjectStatusSummaryVO> getCourseStatusSummaryCross(TrainCourseQueryDTO query) {
        buildExcludeChildrenAreaIds(query);
         buildChildrenAreaIds(query);
        return WebResult.successData(trainCourseService.getCourseStatusSummary(query));
    }

    @ApiOperation("获取跨区支持班次涉及区域ID")
    @GetMapping("/cross/area/ids")
    public WebResult<List<String>> getCrossAreaIds(@ApiParam("当前部门") @RequestParam String currentDeptId,
                                        @ApiParam("参与用户") @RequestParam(required = false) String courseRelationUserId) {
        List<Integer> allChildrenDeptIds = deptCacheService.getAllChildrenDeptIds(Integer.valueOf(currentDeptId));
        List<String> excludeCourseAreaIds = allChildrenDeptIds.stream()
            .map(String::valueOf)
            .collect(Collectors.toList());
        return WebResult.successData(trainCourseService.getCrossAreaIds(excludeCourseAreaIds, courseRelationUserId));
    }

    private void buildChildrenAreaIds(TrainCourseQueryDTO query) {
        List<Integer> allChildrenDeptIds = deptCacheService.getAllChildrenDeptIds(query.getCourseAreaId());
        if (CollectionUtils.isNotEmpty(query.getExcludeCourseAreaIds())) {
            allChildrenDeptIds = allChildrenDeptIds.stream()
                .filter(id -> !query.getExcludeCourseAreaIds().contains(id))
                .collect(Collectors.toList());
        }
        query.setCourseAreaIds(allChildrenDeptIds);
        query.setCourseAreaId(null);
    }

    private void buildExcludeChildrenAreaIds(TrainCourseQueryDTO query) {
        if (query.getCurrentDeptId() == null) {
            return;
        }
        List<Integer> allChildrenDeptIds = deptCacheService.getAllChildrenDeptIds(query.getCurrentDeptId());
        query.setExcludeCourseAreaIds(allChildrenDeptIds);
    }

}
