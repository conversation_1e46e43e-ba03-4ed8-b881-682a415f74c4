package com.dt.train.management.center.utils;

import org.apache.commons.lang3.StringUtils;

public class DeptUtils {

    /**
     * 判断A部门是否是B部门的子部门
     * 
     * @param deptAs 部门A编号;分割
     * @param deptBs 部门B编号;分割
     * @return 如果A是B的子部门，则返回true，否则返回false
     */
    public static boolean isSubOf(String deptAs, String deptBs){
        if(StringUtils.isBlank(deptAs) || StringUtils.isBlank(deptBs)){
            return false;
        }
        String[] deptAArray = StringUtils.split(deptAs, ";");
        String[] deptBArray = StringUtils.split(deptBs, ";");
        for(String deptA : deptAArray){
            for(String deptB : deptBArray){
                if(isEqualOrSub(deptA, deptB)){
                    return true;
                }
            }
        }
        return false;
    }

    public static boolean isEqualOrSub(String deptA, String deptB) {
        if(StringUtils.isBlank(deptA) || StringUtils.isBlank(deptB)){
            return false;
        }
        return StringUtils.equals(deptA, deptB) || isSubDept(deptA, deptB);
    }
    
    /**
     * 判断A部门是否是B部门的子部门
     * @param deptA 部门A编号
     * @param deptB 部门B编号
     * @return 如果A是B的子部门，则返回true，否则返回false
     */
    public static boolean isSubDept(String deptA, String deptB) {
        if(StringUtils.isBlank(deptA) || StringUtils.isBlank(deptB)){
            return false;
        }
        if (StringUtils.equals(deptA, deptB)) {
            return false;
        }
        
        if (deptB.endsWith("0000")) { // 一级部门
            return deptA.startsWith(deptB.substring(0, 2));
        } else if (deptB.endsWith("00")) { // 二级部门
            return deptA.startsWith(deptB.substring(0, 4));
        }
        
        return false;
    }
}
