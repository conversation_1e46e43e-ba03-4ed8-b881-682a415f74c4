package com.dt.train.management.center.job;

import java.time.LocalDate;
import java.time.LocalTime;

import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.dt.train.management.center.config.BusinessConfig;
import com.dt.train.management.center.enums.MessageEnum;
import com.dt.train.management.center.manager.TrainCheckTaskManager;

import lombok.extern.slf4j.Slf4j;

@RestController
@RequestMapping("/feign/job/check")
@Slf4j
public class CheckTaskJob {

    private final TrainCheckTaskManager trainCheckTaskManager;

    private final BusinessConfig businessConfig;

    public CheckTaskJob(TrainCheckTaskManager trainCheckTaskManager, BusinessConfig businessConfig) {
        this.trainCheckTaskManager = trainCheckTaskManager;
        this.businessConfig = businessConfig;
    }

    /**
     * 前一天未打卡考勤处理
     */
    @PostMapping("/status")
    public void processCheckStatus() {
        trainCheckTaskManager.processUnCheckStatus(LocalDate.now().minusDays(1));
    }

    @PostMapping("/message")
    public void processMessage() {
        LocalTime now = LocalTime.now();
        if (now.isBefore(LocalTime.ofSecondOfDay(businessConfig.getCheckInTimeNormalSecond()))) {
            //上班签到提醒
            trainCheckTaskManager.processMessage(MessageEnum.SIGN_IN);
            return;
        }
        if(now.isBefore(LocalTime.ofSecondOfDay(businessConfig.getSignOnSiteAlertSecond()))) {
            //现场签到提醒
            trainCheckTaskManager.processMessage(MessageEnum.SIGN_ON_SITE);
            return;
        }
        if(now.isBefore(LocalTime.ofSecondOfDay(businessConfig.getCheckOutTimeNormalSecond()))) {
            //下班签到提醒
            trainCheckTaskManager.processMessage(MessageEnum.SIGN_OUT);
            return;
        }
    }
}
