package com.dt.train.management.center.service.task;

import java.util.List;
import java.util.stream.Collectors;

import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import com.dt.framework.business.dto.page.Pagination;
import com.dt.train.management.center.config.UserRequestContextHolder;
import com.dt.train.management.center.enums.CheckDimensionEnum;
import com.dt.train.management.center.enums.CheckStatusEnum;
import com.dt.train.management.center.enums.CheckSummaryStatusEnum;
import com.dt.train.management.center.mapper.ex.TrainCheckTaskDOMapperEx;
import com.dt.train.management.center.model.dao.TrainCheckTaskDO;
import com.dt.train.management.center.model.dto.task.TrainCheckTaskDTO;
import com.dt.train.management.center.model.dto.task.TrainCheckTaskDTOWithCourse;
import com.dt.train.management.center.model.dto.task.TrainCheckTaskQueryDTO;
import com.dt.train.management.center.model.vo.check.TrainCheckTaskVO;
import com.dt.train.management.center.model.vo.check.TrainCheckTaskVOWithCourse;

@Service
public class TrainCheckTaskService {

    private final TrainCheckTaskDOMapperEx trainCheckTaskMapperEx;

    public TrainCheckTaskService(TrainCheckTaskDOMapperEx trainCheckTaskMapperEx) {
        this.trainCheckTaskMapperEx = trainCheckTaskMapperEx;
    }

    /**
     * 分页查询打卡任务
     * @param trainCheckTaskQueryDTO
     * @return
     */
    public Pagination<TrainCheckTaskVOWithCourse> page(TrainCheckTaskQueryDTO trainCheckTaskQueryDTO) {
        Integer total = trainCheckTaskMapperEx.countByQuery(trainCheckTaskQueryDTO);
        Pagination<TrainCheckTaskVOWithCourse> result = new Pagination<>();
        result.setPageIndex(trainCheckTaskQueryDTO.getPageIndex());
        result.setPageSize(trainCheckTaskQueryDTO.getPageSize());
        result.setTotal(total);
        if(total == 0) {
            return result;
        }
        List<TrainCheckTaskDTOWithCourse> trainCheckTaskDOs = trainCheckTaskMapperEx.listByQuery(trainCheckTaskQueryDTO);
        result.setRows(trainCheckTaskDOs.stream().map(this::do2voWithCourse).collect(Collectors.toList()));
        return result;
    }

    /**
     * 根据查询条件查询打卡任务
     * @param trainCheckTaskQueryDTO
     * @return
     */
    public List<TrainCheckTaskDTO> listByQuery(TrainCheckTaskQueryDTO trainCheckTaskQueryDTO) {
        List<TrainCheckTaskDTOWithCourse> trainCheckTaskDOs = trainCheckTaskMapperEx.listByQuery(trainCheckTaskQueryDTO);
        return trainCheckTaskDOs.stream().map(dto -> {
            TrainCheckTaskDTO trainCheckTaskDTO = new TrainCheckTaskDTO();
            BeanUtils.copyProperties(dto, trainCheckTaskDTO);
            return trainCheckTaskDTO;
        }).collect(Collectors.toList());
    }

    private TrainCheckTaskDTO do2dto(TrainCheckTaskDO trainCheckTaskDO) {
        TrainCheckTaskDTO trainCheckTaskDTO = new TrainCheckTaskDTO();
        BeanUtils.copyProperties(trainCheckTaskDO, trainCheckTaskDTO);
        return trainCheckTaskDTO;
    }

    /**
     * 保存或更新打卡任务
     * @param trainCheckTaskDTO
     */
    public TrainCheckTaskVO saveOrUpdate(TrainCheckTaskDTO trainCheckTaskDTO) {
        TrainCheckTaskDO trainCheckTaskDO = dto2do(trainCheckTaskDTO);
        if(trainCheckTaskDO.getId() == null) {
            trainCheckTaskDO.setCreatedBy(UserRequestContextHolder.getRequestUserId());
            trainCheckTaskDO.setUpdatedBy(UserRequestContextHolder.getRequestUserId());
            trainCheckTaskMapperEx.insertSelective(trainCheckTaskDO);
        } else {
            trainCheckTaskDO.setUpdatedBy(UserRequestContextHolder.getRequestUserId());
            trainCheckTaskMapperEx.updateByPrimaryKeySelective(trainCheckTaskDO);
        }
        return do2vo(trainCheckTaskDO);
    }

    /**
     * 修改打卡状态
     * @param checkTaskId
     * @param checkStatus
     */
    public void updateCheckStatus(Integer checkTaskId, Integer checkStatus) {
        TrainCheckTaskDO trainCheckTaskDO = new TrainCheckTaskDO();
        trainCheckTaskDO.setId(checkTaskId);
        trainCheckTaskDO.setCheckStatus(checkStatus);
        trainCheckTaskDO.setUpdatedBy(UserRequestContextHolder.getRequestUserId());
        trainCheckTaskMapperEx.updateByPrimaryKeySelective(trainCheckTaskDO);
    }

    private TrainCheckTaskDO dto2do(TrainCheckTaskDTO dto) {
        TrainCheckTaskDO trainCheckTaskDO = new TrainCheckTaskDO();
        BeanUtils.copyProperties(dto, trainCheckTaskDO);
        if(dto.getCheckInStatus() == CheckStatusEnum.NORMAL.getCode() 
            && dto.getCheckOutStatus() == CheckStatusEnum.NORMAL.getCode() 
            && dto.getCheckOnSiteStatus() == CheckStatusEnum.NORMAL.getCode()) {
            trainCheckTaskDO.setCheckStatus(CheckSummaryStatusEnum.NORMAL.getCode());
        } else {
            trainCheckTaskDO.setCheckStatus(CheckSummaryStatusEnum.ABNORMAL.getCode());
        }
        return trainCheckTaskDO;
    }

    private TrainCheckTaskVOWithCourse do2voWithCourse(TrainCheckTaskDTOWithCourse trainCheckTaskDO) {
        TrainCheckTaskVOWithCourse trainCheckTaskVOWithCourse = new TrainCheckTaskVOWithCourse();
        BeanUtils.copyProperties(trainCheckTaskDO, trainCheckTaskVOWithCourse);
        trainCheckTaskVOWithCourse.setIsAllDay(CheckDimensionEnum.getByCode(trainCheckTaskDO.getCheckDimension()).getDimension());
        return trainCheckTaskVOWithCourse;
    }

    private TrainCheckTaskVO do2vo(TrainCheckTaskDO trainCheckTaskDO) {
        TrainCheckTaskVO trainCheckTaskVO = new TrainCheckTaskVO();
        BeanUtils.copyProperties(trainCheckTaskDO, trainCheckTaskVO);
        trainCheckTaskVO.setIsAllDay(CheckDimensionEnum.getByCode(trainCheckTaskDO.getCheckDimension()).getDimension());
        return trainCheckTaskVO;
    }

    public TrainCheckTaskVO dto2vo(TrainCheckTaskDTO trainCheckTaskDTO) {
        TrainCheckTaskVO trainCheckTaskVO = new TrainCheckTaskVO();
        BeanUtils.copyProperties(trainCheckTaskDTO, trainCheckTaskVO);
        trainCheckTaskVO.setIsAllDay(CheckDimensionEnum.getByCode(trainCheckTaskDTO.getCheckDimension()).getDimension());
        return trainCheckTaskVO;
    }

    /**
     * 根据ID获取打卡任务
     * @param checkTaskId
     * @return
     */
    public TrainCheckTaskDTO getById(Integer checkTaskId) {
        TrainCheckTaskDO trainCheckTaskDO = trainCheckTaskMapperEx.selectByPrimaryKey(checkTaskId);
        return do2dto(trainCheckTaskDO);
    }

    public void saveBatch(List<TrainCheckTaskDTO> saveTrainCheckTaskDTOs) {
        if(saveTrainCheckTaskDTOs.isEmpty()) {
            return;
        }
        List<TrainCheckTaskDO> trainCheckTaskDOs = saveTrainCheckTaskDTOs.stream().map(this::dto2do).collect(Collectors.toList());
        trainCheckTaskMapperEx.insertBatch(trainCheckTaskDOs);
    }
}
