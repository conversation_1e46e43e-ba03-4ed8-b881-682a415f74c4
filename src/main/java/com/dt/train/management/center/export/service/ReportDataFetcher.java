package com.dt.train.management.center.export.service;

import java.util.List;
import java.util.Map;

import com.dt.train.management.center.export.enums.ReportType;

/**
 * 报表数据获取器接口。
 * 负责根据报表类型和参数从数据源获取原始数据，并将其封装成统一的 Excel 导出 DTO 列表。
 */
public interface ReportDataFetcher {
    /**
     * 判断当前数据获取器是否支持指定报表类型。
     * @param reportType 报表类型
     * @return true 如果支持，否则 false
     */
    boolean supports(ReportType reportType);

    /**
     * 根据报表参数获取数据，并将其封装成对应的 Excel 导出 DTO 列表。
     * @param parameters 报表过滤参数
     * @return 对应的 Excel 导出 DTO 列表。
     */
    List<?> fetchData(Map<String, Object> parameters);
}