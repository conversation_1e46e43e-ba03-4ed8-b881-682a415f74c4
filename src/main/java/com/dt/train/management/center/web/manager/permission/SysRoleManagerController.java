package com.dt.train.management.center.web.manager.permission;

import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.dt.framework.business.dto.web.common.WebResult;
import com.dt.train.management.center.model.dao.SysRoleDO;
import com.dt.train.management.center.model.dto.permission.SysRolePermissionRequest;
import com.dt.train.management.center.model.dto.permission.SysRoleRequest;
import com.dt.train.management.center.model.vo.permission.SysRoleVO;
import com.dt.train.management.center.service.permission.SysRoleService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;


/**
 * 角色管理控制器
 * 提供角色相关的API接口
 */
@RestController
@RequestMapping("/manager/role")
@Api(tags = "角色相关接口")
public class SysRoleManagerController {

    private final SysRoleService sysRoleService;

    public SysRoleManagerController(SysRoleService sysRoleService) {
        this.sysRoleService = sysRoleService;
    }

    /**
     * 保存角色（创建或更新）
     * @param request 角色请求参数
     * @return 操作结果
     */
    @PostMapping("")
    @ApiOperation(value = "保存角色", notes = "创建或更新角色")
    public WebResult<SysRoleVO> saveOrUpdateRole(@RequestBody SysRoleRequest request) {
        SysRoleDO roleDO = new SysRoleDO();
        roleDO.setId(request.getId());
        roleDO.setRoleName(request.getRoleName());
        roleDO.setRemark(request.getRemark());
        
        if (request.getId() == null) {
            // 创建新角色
            return WebResult.successData(sysRoleService.createRole(roleDO));
        } else {
            // 更新现有角色
            return WebResult.successData(sysRoleService.updateRole(roleDO));
        }
    }

    /**
     * 删除角色
     * @param id 角色ID
     * @return 操作结果
     */
    @DeleteMapping("/_delete")
    @ApiOperation(value = "删除角色", notes = "根据ID删除角色")
    public WebResult<Void> deleteRole(
        @ApiParam(value = "角色ID", required = true) @RequestParam Integer id) {
        sysRoleService.deleteRole(id);
        return WebResult.success();
    }

    /**
     * 分配角色权限
     * @param roleId 角色ID
     * @param permissionKeys 权限Key列表
     * @return 操作结果
     */
    @PostMapping("/permissions")
    @ApiOperation(value = "分配角色权限", notes = "根据角色ID分配权限")
    public WebResult<Void> assignPermissions(@Validated @RequestBody SysRolePermissionRequest request) {
        sysRoleService.assignPermissions(request.getRoleId(), request.getPermissionKeys());
        return WebResult.success();
    }
    
}
