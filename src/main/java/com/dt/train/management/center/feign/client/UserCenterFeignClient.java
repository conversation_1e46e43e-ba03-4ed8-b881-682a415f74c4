package com.dt.train.management.center.feign.client;

import java.util.List;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import com.dt.train.management.center.feign.param.RegionSearchParam;
import com.dt.train.management.center.feign.param.UserSearchParam;
import com.dt.train.management.center.feign.result.RegionDTO;
import com.dt.train.management.center.feign.result.UserDTO;

@FeignClient(value = "user-center")
public interface UserCenterFeignClient {
    /**
     * 根据姓名、手机号等信息获取用户基本信息
     *
     * @return 基本信息
     */
    @PostMapping("/feign/user/listUsers")
     List<UserDTO> listUsers(@RequestBody UserSearchParam searchParam);

    @PostMapping("/feign/region/listRegions")
    List<RegionDTO> listRegions(@RequestBody RegionSearchParam searchParam);

    @GetMapping("/feign/user/get")
    UserDTO getUserById(@RequestParam Long userId);

}
