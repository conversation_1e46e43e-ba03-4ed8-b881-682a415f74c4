package com.dt.train.management.center.model.response.fxiaoke;

import java.util.List;
import java.util.Map;

import lombok.Data;

/**
 * 纷享销客自定义对象查询响应
 * 
 * <AUTHOR>
 * @since 2025-05-16
 */
@Data
public class FxiaokeDataObjectQueryResponse {
    
    /**
     * 错误码
     */
    private Integer errorCode;
    
    /**
     * 错误信息
     */
    private String errorMessage;

    /**
     * 错误描述
     */
    private String errorDescription;

    /**
     * 请求ID
     */
    private String traceId;
    
    /**
     * 数据列表
     */
    private QueryData data;

    @Data
    public static class QueryData {

        private List<Map<String, Object>> dataList;

        private Integer total;

        private Integer offset;

        private Integer limit;
    }
}