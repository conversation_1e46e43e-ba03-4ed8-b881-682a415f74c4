package com.dt.train.management.center.service.project;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import com.dt.train.management.center.enums.CheckDimensionEnum;
import com.dt.train.management.center.enums.CheckStatusEnum;
import com.dt.train.management.center.enums.CheckSummaryStatusEnum;
import com.dt.train.management.center.enums.HalfDayEnum;
import com.dt.train.management.center.enums.TrainCourseUserRelationTypeEnum;
import com.dt.train.management.center.enums.UserTypeEnum;
import com.dt.train.management.center.manager.UserStatusManager;
import com.dt.train.management.center.mapper.TrainCourseUserRelationDOMapper;
import com.dt.train.management.center.mapper.TrainUserDOMapper;
import com.dt.train.management.center.model.common.HalfDayTime;
import com.dt.train.management.center.model.dao.TrainCourseUserRelationDO;
import com.dt.train.management.center.model.dao.TrainUserDO;
import com.dt.train.management.center.model.dto.project.CourseUserRelationQueryDTO;
import com.dt.train.management.center.model.dto.project.TrainCourseUserRelationDTO;
import com.dt.train.management.center.model.dto.project.TrainCourseUserRelationSaveOrUpdateDTO;
import com.dt.train.management.center.model.dto.task.TrainCheckTaskDTO;
import com.dt.train.management.center.model.dto.task.TrainCourseUserRelationDTOWithCourse;
import com.dt.train.management.center.model.vo.project.CalendarUserUnitVO;
import com.dt.train.management.center.model.vo.project.TrainCourseUserRelationVOWithCourse;
import com.dt.train.management.center.model.vo.task.TrainTaskVO;
import com.dt.train.management.center.service.task.TrainTaskService;

@Service
public class TrainCourseUserRelationService {

    private final TrainCourseUserRelationDOMapper trainCourseUserRelationDOMapper;

    private final TrainTaskService trainTaskService;

    private final UserStatusManager userStatusManager;

    private final TrainUserDOMapper trainUserDOMapper;

    public TrainCourseUserRelationService(TrainCourseUserRelationDOMapper trainCourseUserRelationDOMapper,
                                        TrainTaskService trainTaskService,
                                        UserStatusManager userStatusManager,
                                        TrainUserDOMapper trainUserDOMapper) {
        this.trainCourseUserRelationDOMapper = trainCourseUserRelationDOMapper;
        this.trainTaskService = trainTaskService;
        this.userStatusManager = userStatusManager;
        this.trainUserDOMapper = trainUserDOMapper;
    }

    /**
     * 根据项目ID批量更新项目区域ID
     * @param projectId 项目ID
     * @param newAreaId 新的区域ID
     */
    public void updateProjectAreaIdByProjectId(Integer projectId, Integer newAreaId) {
        if (projectId == null || newAreaId == null) {
            return;
        }
        trainCourseUserRelationDOMapper.batchUpdateProjectAreaIdByProjectId(projectId, newAreaId);
    }

    /**
     * 更新用户部门
     * 
     * @param userId 用户ID
     * @param deptId 部门ID
     * @param deptName 部门名称
     */
    public void updateUserDept(Long userId, String deptId, String deptName) {
        TrainCourseUserRelationDO updateRecord = new TrainCourseUserRelationDO();
        updateRecord.setUserDeptId(deptId);
        updateRecord.setUserDeptName(deptName);
        
        // 创建查询条件
        CourseUserRelationQueryDTO query = new CourseUserRelationQueryDTO();
        query.setUserId(userId);
        
        // 获取用户类型
        TrainUserDO user = trainUserDOMapper.selectByUserId(userId);
        Integer userType = user != null ? user.getUserType() : UserTypeEnum.FULL_TIME.getCode();

        // 批量更新
        List<TrainCourseUserRelationDO> relations = trainCourseUserRelationDOMapper.selectByQuery(query);
        relations.forEach(relation -> {
            relation.setUserDeptId(deptId);
            relation.setUserDeptName(deptName);
            relation.setUserType(userType); // 同时更新用户类型
            trainCourseUserRelationDOMapper.updateByPrimaryKeySelective(relation);
        });
    }

    /**
     * 更新用户ID
     * 
     * @param oldUserId 旧用户ID
     * @param newUserId 新用户ID
     */
    public void updateRelationUserId(Long oldUserId, Long newUserId) {
        // 获取所有关联记录
        CourseUserRelationQueryDTO query = new CourseUserRelationQueryDTO();
        query.setUserId(oldUserId);
        List<TrainCourseUserRelationDO> relations = trainCourseUserRelationDOMapper.selectByQuery(query);
        if (relations.isEmpty()) {
            return; // 没有关联记录，直接返回
        }
        
        // 批量更新
        relations.forEach(relation -> {
            relation.setUserId(newUserId);
            trainCourseUserRelationDOMapper.updateByPrimaryKeySelective(relation);
        });
    }

    /**
     * 根据项目id列表获取培训人员关系列表
     * @param projectIds 项目ID列表
     */
    public List<TrainCourseUserRelationDTO> listByProjectIds(List<Integer> projectIds) {
        if(projectIds == null || projectIds.isEmpty()) {
            return Collections.emptyList();
        }
        List<TrainCourseUserRelationDO> trainCourseUserRelationDOList = trainCourseUserRelationDOMapper.selectByProjectIds(projectIds);
        return trainCourseUserRelationDOList.stream().map(this::convertToDTO).collect(Collectors.toList());
    }

    public List<TrainCourseUserRelationDTO> listByCourseIds(List<Integer> courseIds) {
        if(courseIds == null || courseIds.isEmpty()) {
            return Collections.emptyList();
        }
        List<TrainCourseUserRelationDO> trainCourseUserRelationDOList = trainCourseUserRelationDOMapper.selectByCourseIds(courseIds);
        return trainCourseUserRelationDOList.stream().map(this::convertToDTO).collect(Collectors.toList());
    }

    /**
     * 保存或更新执行用户
     * 
     * @param projectId
     * @param courseId
     * @param executeUsers
     */
    public void saveOrUpdateExecuteUser(Integer projectId, Integer courseId, List<TrainCourseUserRelationSaveOrUpdateDTO> executeUsers) {
        saveOrUpdateUser(projectId, courseId, executeUsers, TrainCourseUserRelationTypeEnum.EXECUTE.getCode());
    }

    /**
     * 保存或更新支援用户
     * 
     * @param projectId
     * @param courseId
     * @param supportUsers
     */
    public void saveOrUpdateSupportUser(Integer projectId, Integer courseId, List<TrainCourseUserRelationSaveOrUpdateDTO> supportUsers) {
        saveOrUpdateUser(projectId, courseId, supportUsers, TrainCourseUserRelationTypeEnum.SUPPORT.getCode());
    }
    

    /**
     * 保存或更新班次与用户关系
     * 
     * @param trainCourseUserRelationDTO
     */
    public void saveOrUpdateUser(Integer projectId, Integer courseId
    , List<TrainCourseUserRelationSaveOrUpdateDTO> executeUsers, Integer relationType) {
        if (executeUsers == null) {
            return;
        }
        invalidateUserRelation(courseId, relationType);
        for (TrainCourseUserRelationSaveOrUpdateDTO trainCourseUserRelationDTO : executeUsers) {
            TrainCourseUserRelationDO trainCourseUserRelationDO = saveDTO2DO(trainCourseUserRelationDTO);
            trainCourseUserRelationDO.setRelationType(relationType);
            trainCourseUserRelationDO.setProjectId(projectId);
            trainCourseUserRelationDO.setProjectAreaId(trainCourseUserRelationDTO.getProjectAreaId());
            trainCourseUserRelationDO.setCourseId(courseId);
            trainCourseUserRelationDO.setCourseAreaId(trainCourseUserRelationDTO.getCourseAreaId());
            if (trainCourseUserRelationDTO.getId() == null) {
                trainCourseUserRelationDOMapper.insertSelective(trainCourseUserRelationDO);
            } else {
                trainCourseUserRelationDO.setInvalid(0);
                trainCourseUserRelationDOMapper.updateByPrimaryKeySelective(trainCourseUserRelationDO);
            }
        }
    }

    public void invalidateUserRelation(Integer courseId, Integer relationType) {
        TrainCourseUserRelationDO trainCourseUserRelationDO = new TrainCourseUserRelationDO();
        trainCourseUserRelationDO.setCourseId(courseId);
        trainCourseUserRelationDO.setRelationType(relationType);
        trainCourseUserRelationDOMapper.deleteByCourseIdAndRelationType(trainCourseUserRelationDO);
    }

    private TrainCourseUserRelationDO saveDTO2DO(TrainCourseUserRelationSaveOrUpdateDTO trainCourseUserRelationDTO) {
        TrainCourseUserRelationDO trainCourseUserRelationDO = new TrainCourseUserRelationDO();
        BeanUtils.copyProperties(trainCourseUserRelationDTO, trainCourseUserRelationDO);
        trainCourseUserRelationDO.setStartDate(trainCourseUserRelationDTO.getStartDate().toFullDate());
        trainCourseUserRelationDO.setEndDate(trainCourseUserRelationDTO.getEndDate().toFullDate());

        // 根据用户ID获取用户类型
        if (trainCourseUserRelationDTO.getUserId() != null) {
            TrainUserDO user = trainUserDOMapper.selectByUserId(trainCourseUserRelationDTO.getUserId());
            if (user != null) {
                trainCourseUserRelationDO.setUserType(user.getUserType());
            } else {
                // 如果找不到用户，默认设置为正式员工
                trainCourseUserRelationDO.setUserType(UserTypeEnum.FULL_TIME.getCode());
            }
        } else {
            // 如果没有用户ID，默认设置为正式员工
            trainCourseUserRelationDO.setUserType(UserTypeEnum.FULL_TIME.getCode());
        }

        return trainCourseUserRelationDO;
    }

    private TrainCourseUserRelationDTO convertToDTO(TrainCourseUserRelationDO trainCourseUserRelationDO) {
        TrainCourseUserRelationDTO trainCourseUserRelationDTO = new TrainCourseUserRelationDTO();
        BeanUtils.copyProperties(trainCourseUserRelationDO, trainCourseUserRelationDTO);
        trainCourseUserRelationDTO.setStartDate(new HalfDayTime(trainCourseUserRelationDO.getStartDate()));
        trainCourseUserRelationDTO.setEndDate(new HalfDayTime(trainCourseUserRelationDO.getEndDate()));
        trainCourseUserRelationDTO.setUserStatus(userStatusManager.getUserStatus(String.valueOf(trainCourseUserRelationDO.getUserId())));

        // 设置用户类型名称
        trainCourseUserRelationDTO.setUserTypeName(UserTypeEnum.getByCode(trainCourseUserRelationDO.getUserType()).getDesc());

        return trainCourseUserRelationDTO;
    }

    /**
     * 根据时间查询班次与用户关系列表
     * 
     * @param startDate 开始时间
     * @param endDate 结束时间
     * @return 班次与用户关系列表
     */
    public List<TrainCourseUserRelationDTO> listByQuery(CourseUserRelationQueryDTO queryDTO) {
        List<TrainCourseUserRelationDO> trainCourseUserRelationDOList = trainCourseUserRelationDOMapper.selectByQuery(queryDTO);
        return trainCourseUserRelationDOList.stream().map(this::convertToDTO).collect(Collectors.toList());
    }

    /**
     * 根据时间查询班次与用户关系列表
     * 
     * @param startDate 开始时间
     * @param endDate 结束时间
     * @return 班次与用户关系列表
     */
    public List<TrainCourseUserRelationVOWithCourse> listWithCourseByQuery(CourseUserRelationQueryDTO queryDTO) {
        List<TrainCourseUserRelationDTOWithCourse> trainCourseUserRelationDTOList = trainCourseUserRelationDOMapper.listWithCourseByQuery(queryDTO);
        return trainCourseUserRelationDTOList.stream().map(this::convertToDTOWithCourse).collect(Collectors.toList());
    }

    private TrainCourseUserRelationVOWithCourse convertToDTOWithCourse(TrainCourseUserRelationDTOWithCourse dto) {
        TrainCourseUserRelationVOWithCourse vo = new TrainCourseUserRelationVOWithCourse();
        BeanUtils.copyProperties(dto, vo);
        vo.setStartDate(new HalfDayTime(dto.getStartDate()));
        vo.setEndDate(new HalfDayTime(dto.getEndDate()));
        return vo;
    }

    /**
     * 根据时间查询班次与用户关系列表
     * 拆分成半天
     * 
     * @param startDate 开始时间
     * @param endDate 结束时间
     * @param userId 用户id
     * @param courseId 班次id
     * @return 班次与用户关系列表
     */
    public List<CalendarUserUnitVO> listCalendarByTime(Date startDate, Date endDate, Long userId, Integer courseId) {
        CourseUserRelationQueryDTO queryDTO = new CourseUserRelationQueryDTO();
        queryDTO.setStartDate(startDate);
        queryDTO.setEndDate(endDate);
        queryDTO.setUserId(userId);
        queryDTO.setCourseId(courseId);
        List<TrainCourseUserRelationDTO> trainCourseUserRelationDTOList = listByQuery(queryDTO);
        return trainCourseUserRelationDTOList.stream()
            .map(this::dto2UnitVO)
            .flatMap(List::stream)
            .filter(vo -> {
                boolean res = true;
                HalfDayTime atDate = vo.getAtDate();
                if(startDate != null){
                    res = res && atDate.isAfterEqual(new HalfDayTime(startDate));
                }
                if(endDate != null){
                    res = res && atDate.isBeforeEqual(new HalfDayTime(endDate));
                }
                return res;
            })
            .collect(Collectors.toList());
    }

    /**
     * 根据时间查询班次与用户关系列表
     * 拆分成半天
     * 
     * @param startDate 开始时间
     * @param endDate 结束时间
     * @param userId 用户id
     * @param courseId 班次id
     * @return 班次与用户关系列表
     */
    public List<TrainCheckTaskDTO> listCheckTaskByTime(Date startDate, Date endDate, Long userId, Integer courseId) {
        CourseUserRelationQueryDTO queryDTO = new CourseUserRelationQueryDTO();
        queryDTO.setStartDate(startDate);
        queryDTO.setEndDate(endDate);
        queryDTO.setUserId(userId);
        queryDTO.setCourseId(courseId);
        List<TrainCourseUserRelationDTO> trainCourseUserRelationDTOList = listByQuery(queryDTO);
        if(trainCourseUserRelationDTOList.isEmpty()) {
            return Collections.emptyList();
        }
        List<TrainTaskVO> trainTaskVOList = trainTaskService.getTasksByCourseIds(trainCourseUserRelationDTOList.stream().map(TrainCourseUserRelationDTO::getCourseId).collect(Collectors.toList()));
        Map<Integer, Integer> trainTaskVOsMap = trainTaskVOList.stream().collect(Collectors.toMap(TrainTaskVO::getCourseId, TrainTaskVO::getId));
        Map<String, TrainCheckTaskDTO> checkTaskDTOList = trainCourseUserRelationDTOList.stream()
            .filter(dto -> trainTaskVOsMap.containsKey(dto.getCourseId()))
            .map(dto -> dto2CheckTaskDTO(dto, trainTaskVOsMap.get(dto.getCourseId())))
            .flatMap(List::stream)
            .filter(dto -> !dto.getCheckDate().before(startDate) && !dto.getCheckDate().after(endDate))
            .collect(Collectors.toMap(
                dto -> dto.getCourseId() + "_" + dto.getCheckDate().getTime() + "_" + dto.getUserId(), 
                Function.identity(), 
                (a, b) -> {
                    if(a.getCheckDimension() == CheckDimensionEnum.FULL_DAY.getCode()) {
                        return a;
                    }
                    if((a.getCheckDimension() == CheckDimensionEnum.MORNING.getCode() && b.getCheckDimension() == CheckDimensionEnum.AFTERNOON.getCode())
                     || (a.getCheckDimension() == CheckDimensionEnum.AFTERNOON.getCode() && b.getCheckDimension() == CheckDimensionEnum.MORNING.getCode())) {
                        a.setCheckDimension(CheckDimensionEnum.FULL_DAY.getCode());
                        return a;
                    }
                    return b;
                
                }));
        return new ArrayList<>(checkTaskDTOList.values());
    }

    /**
     * 将DTO转换为CalendarUserUnitVO,按照日期拆开
     * @param dto
     * @return
     */
    private List<CalendarUserUnitVO> dto2UnitVO(TrainCourseUserRelationDTO dto) {
        HalfDayTime start = dto.getStartDate();
        HalfDayTime end = dto.getEndDate();
        List<CalendarUserUnitVO> unitVOList = new ArrayList<>();
        while(!start.isAfter(end)) {
            CalendarUserUnitVO unitVO = new CalendarUserUnitVO();
            BeanUtils.copyProperties(dto, unitVO);
            unitVO.setAtDate(start);
            unitVOList.add(unitVO);
            start = start.next();
        }
        return unitVOList;
    }

    /**
     * 将DTO转换为CalendarUserUnitVO,按照日期拆开
     * @param dto
     * @return
     */
    private List<TrainCheckTaskDTO> dto2CheckTaskDTO(TrainCourseUserRelationDTO dto, Integer taskId) {
        HalfDayTime start = dto.getStartDate();
        HalfDayTime end = dto.getEndDate();
        List<TrainCheckTaskDTO> checkTaskDTOList = new ArrayList<>();
        while(start.isBeforeEqual(end)) {
            TrainCheckTaskDTO checkTaskDTO = new TrainCheckTaskDTO();
            checkTaskDTO.setProjectId(dto.getProjectId());
            checkTaskDTO.setTaskId(taskId);
            checkTaskDTO.setUserId(dto.getUserId());
            checkTaskDTO.setUserName(dto.getUserName());
            checkTaskDTO.setCourseId(dto.getCourseId());
            checkTaskDTO.setCheckDate(start.startOfDay());
            checkTaskDTO.setCheckStatus(CheckSummaryStatusEnum.ABNORMAL.getCode());
            checkTaskDTO.setCheckInStatus(CheckStatusEnum.NOT_CHECKED.getCode());
            checkTaskDTO.setCheckOutStatus(CheckStatusEnum.NOT_CHECKED.getCode());
            checkTaskDTO.setCheckOnSiteStatus(CheckStatusEnum.NOT_CHECKED.getCode());
            checkTaskDTOList.add(checkTaskDTO);
            if(start.getHalfDay() == HalfDayEnum.PM) {
                checkTaskDTO.setCheckDimension(CheckDimensionEnum.AFTERNOON.getCode());
                start = start.next();
                continue;
            }
            if(start.equals(end)) {
                checkTaskDTO.setCheckDimension(CheckDimensionEnum.MORNING.getCode());
                break;
            }
            checkTaskDTO.setCheckDimension(CheckDimensionEnum.FULL_DAY.getCode());
            start = start.nextDay();
        }
        return checkTaskDTOList;
    }

}
