package com.dt.train.management.center.model.dao;

import java.util.Date;

/**
 * 
 * 表名: train_course_user_relation
 *
 * @mbg.generated
 */
@lombok.Data
public class TrainCourseUserRelationDO {
    /**
     * 字段描述: 主键ID
     *
     * 字段名: train_course_user_relation.id
     *
     * @mbg.generated
     */
    private Integer id;

    /**
     * 字段描述: 项目ID
     *
     * 字段名: train_course_user_relation.project_id
     *
     * @mbg.generated
     */
    private Integer projectId;

    private Integer projectAreaId;

    /**
     * 字段描述: 班次ID
     *
     * 字段名: train_course_user_relation.course_id
     *
     * @mbg.generated
     */
    private Integer courseId;

    private Integer courseAreaId;

    /**
     * 字段描述: 用户ID
     *
     * 字段名: train_course_user_relation.user_id
     *
     * @mbg.generated
     */
    private Long userId;

    private String userName;

    private String originUserName;

    /**
     * 字段描述: 成员类型，0-正式员工，1-兼职人员
     *
     * 字段名: train_course_user_relation.user_type
     *
     * @mbg.generated
     */
    private Integer userType;

    private String userDeptId;

    private String userDeptName;

    /**
     * 字段描述: 关系类型,1-支援人，2-执行人
     *
     * 字段名: train_course_user_relation.relation_type
     *
     * @mbg.generated
     */
    private Integer relationType;

    /**
     * 字段描述: 开始时间
     *
     * 字段名: train_course_user_relation.start_date
     *
     * @mbg.generated
     */
    private Date startDate;

    /**
     * 字段描述: 结束时间
     *
     * 字段名: train_course_user_relation.end_date
     *
     * @mbg.generated
     */
    private Date endDate;

    /**
     * 字段描述: 是否已删除 0 否 1 是
     *
     * 字段名: train_course_user_relation.invalid
     *
     * @mbg.generated
     */
    private Integer invalid;

    /**
     * 字段描述: 创建人
     *
     * 字段名: train_course_user_relation.created_by
     *
     * @mbg.generated
     */
    private Long createdBy;

    /**
     * 字段描述: 更新人
     *
     * 字段名: train_course_user_relation.updated_by
     *
     * @mbg.generated
     */
    private Long updatedBy;

    /**
     * 字段描述: 创建时间
     *
     * 字段名: train_course_user_relation.gmt_create
     *
     * @mbg.generated
     */
    private Date gmtCreate;

    /**
     * 字段描述: 修改时间
     *
     * 字段名: train_course_user_relation.gmt_modified
     *
     * @mbg.generated
     */
    private Date gmtModified;
}