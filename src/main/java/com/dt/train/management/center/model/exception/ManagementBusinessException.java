package com.dt.train.management.center.model.exception;

import com.dt.framework.core.exception.BusinessException;
import com.dt.train.management.center.enums.ManagementExceptionEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
public class ManagementBusinessException extends BusinessException {

    private String detail;

    /**
     * meeting business exception
     *
     * @param exceptionEnum exception enum
     */
    public ManagementBusinessException(ManagementExceptionEnum exceptionEnum) {
        super(Integer.valueOf(exceptionEnum.getCode()), exceptionEnum.getMessage());
    }

    /**
     * meeting business exception
     *
     * @param exceptionEnum exception enum
     */
    public ManagementBusinessException(ManagementExceptionEnum exceptionEnum, String detail) {
        super(Integer.valueOf(exceptionEnum.getCode()), exceptionEnum.getMessage() + detail);
        this.detail = detail;
    }

    public ManagementBusinessException(ManagementExceptionEnum exceptionEnum, String detail, String... args) {
        super(Integer.valueOf(exceptionEnum.getCode()), String.format(exceptionEnum.getMessage(), args) + detail);
        this.detail = detail;
    }

}
