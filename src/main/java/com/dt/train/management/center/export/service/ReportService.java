package com.dt.train.management.center.export.service;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;

import org.springframework.context.ApplicationContext;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.dt.train.management.center.config.UserRequestContextHolder;
import com.dt.train.management.center.export.enums.ReportFormat;
import com.dt.train.management.center.export.enums.ReportStatus;
import com.dt.train.management.center.export.enums.ReportType;
import com.dt.train.management.center.export.exception.ReportGenerationException;
import com.dt.train.management.center.export.models.ReportMetadata;
import com.dt.train.management.center.export.models.ReportRequest;
import com.dt.train.management.center.utils.RedisUtil;

import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
public class ReportService {

    private final ApplicationContext applicationContext;
    // 使用 ConcurrentHashMap 确保线程安全
    private Map<ReportType, ReportDataFetcher> dataFetchers = new ConcurrentHashMap<>();
    private Map<ReportFormat, ReportGenerator> reportGenerators = new ConcurrentHashMap<>();

    @Resource
    private RedisUtil redisUtil;

    private static final String REPORT_STATUS_KEY_PREFIX = "train-management-center:report:status:";

    public ReportService(ApplicationContext applicationContext) {
        this.applicationContext = applicationContext;
    }

    @PostConstruct
    public void init() {
        // 动态查找并注册所有 ReportDataFetcher 实现
        applicationContext.getBeansOfType(ReportDataFetcher.class)
                .values()
                .forEach(fetcher -> {
                    // 遍历 ReportType 找到当前 fetcher 支持的类型
                    for (ReportType type : ReportType.values()) {
                        if (fetcher.supports(type)) {
                            if (dataFetchers.put(type, fetcher) != null) {
                                throw new IllegalStateException("发现重复的报表数据获取器支持同一类型: " + type);
                            }
                            break; // 假设一个Fetcher只支持一种ReportType，或只注册第一个匹配的
                        }
                    }
                });

        // 动态查找并注册所有 ReportGenerator 实现
        applicationContext.getBeansOfType(ReportGenerator.class)
                .values()
                .forEach(generator -> {
                    // 遍历 ReportFormat 找到当前 generator 支持的格式
                    for (ReportFormat format : ReportFormat.values()) {
                        if (generator.supports(format)) {
                            if (reportGenerators.put(format, generator) != null) {
                                throw new IllegalStateException("发现重复的报表生成器支持同一格式: " + format);
                            }
                            break; // 假设一个Generator只支持一种ReportFormat，或只注册第一个匹配的
                        }
                    }
                });
        log.info("ReportDataFetchers loaded: {}", dataFetchers.keySet());
        log.info("ReportGenerators loaded: {}", reportGenerators.keySet());
    }

    /**
     * 同步生成并返回报表内容。
     * @param request 报表请求
     * @return 报表内容的字节数组
     * @throws IllegalArgumentException 如果找不到对应的报表处理器
     * @throws ReportGenerationException 如果报表生成失败
     */
    public String generateReport(ReportRequest request) {
        ReportDataFetcher dataFetcher = dataFetchers.get(request.getReportType());
        if (dataFetcher == null) {
            throw new IllegalArgumentException("未找到支持的报表数据获取器: " + request.getReportType());
        }

        ReportGenerator reportGenerator = reportGenerators.get(request.getFormat());
        if (reportGenerator == null) {
            throw new IllegalArgumentException("未找到支持的报表生成器: " + request.getFormat());
        }

        // 1. 获取数据 (现在返回 List<?>)
        log.info("开始获取报表数据，类型: {}, 参数: {}", request.getReportType(), request.getParameters());
        List<?> data = dataFetcher.fetchData(request.getParameters());
        log.info("数据获取完成，数据量: {}", (data != null ? data.size() : 0));

        // 2. 生成报表
        log.info("开始生成报表，格式: {}", request.getFormat());
        String url = reportGenerator.generateReport(data, request.getReportType());
        log.info("报表生成完成，url: {}", url);
        return url;
    }

    /**
     * 异步生成报表。（需要 Spring 的 @EnableAsync 和 TaskExecutor 配置）
     * 实际项目中，异步生成后，会将文件保存到文件系统/OSS，并更新数据库中的报表状态。
     * @param request 报表请求
     * @param reportId 唯一的报表ID，用于后续查询和下载
     * @return CompletableFuture 用于异步结果处理
     */
    @Async("reportTaskExecutor") // 使用 AsyncConfig 中定义的线程池
    public CompletableFuture<Void> generateReportAsync(ReportRequest request, String reportId) {
        // 设置初始状态为 PROCESSING
        ReportMetadata metadata = new ReportMetadata(reportId, ReportStatus.PROCESSING, null, null, null, System.currentTimeMillis());
        redisUtil.sset(REPORT_STATUS_KEY_PREFIX + reportId, JSON.toJSONString(metadata), 2 * 60 * 60);
        log.info("异步报表ID " + reportId + ": 初始状态设置为 PROCESSING。");
        String deptId = UserRequestContextHolder.getRequestDeptId();
        Long userId = UserRequestContextHolder.getRequestUserId();
        String userName = UserRequestContextHolder.getRequestUserName();
        String manageDeptId = UserRequestContextHolder.getRequestManageDeptId();

        return CompletableFuture.runAsync(() -> {
            try {                
                UserRequestContextHolder.setRequestDeptId(deptId);
                UserRequestContextHolder.setRequestUserId(userId);
                UserRequestContextHolder.setRequestUserName(userName);
                UserRequestContextHolder.setRequestManageDeptId(manageDeptId);

                String url = generateReport(request);
                String filename = request.getReportType().name().toLowerCase() + "_report" + request.getFormat().getFileExtension();
                String contentType = request.getFormat().getContentType();

                // 模拟将报表内容存储起来，并更新状态
                metadata.setStatus(ReportStatus.COMPLETED);
                metadata.setUrl(url);
                metadata.setFilename(filename);
                metadata.setContentType(contentType);
                redisUtil.sset(REPORT_STATUS_KEY_PREFIX + reportId, JSON.toJSONString(metadata), 2 * 60 * 60);
                log.info("异步报表ID " + reportId + ": 生成完成。url: " + url);

            } catch (Exception e) {
                metadata.setStatus(ReportStatus.FAILED);
                redisUtil.sset(REPORT_STATUS_KEY_PREFIX + reportId, JSON.toJSONString(metadata), 2 * 60 * 60);
                log.error("异步报表生成失败，ID: " + reportId + ": " + e.getMessage(), e);
            }
        });
    }

    /**
     * 根据报表ID获取报表元数据，包括状态和文件信息。
     * @param reportId 报表唯一ID
     * @return 包含报表元数据的 Optional 对象
     */
    public Optional<ReportMetadata> getReportMetadata(String reportId) {
        ReportMetadata metadata = redisUtil.get(REPORT_STATUS_KEY_PREFIX + reportId, ReportMetadata.class);
        return Optional.ofNullable(metadata);
    }
}
