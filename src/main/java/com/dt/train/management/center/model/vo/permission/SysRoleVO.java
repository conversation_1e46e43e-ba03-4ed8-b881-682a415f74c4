package com.dt.train.management.center.model.vo.permission;

/**
 * 
 * 表名: sys_role
 *
 * @mbg.generated
 */
@lombok.Data
public class SysRoleVO {
    /**
     * 字段描述: 角色ID
     *
     * 字段名: sys_role.id
     *
     * @mbg.generated
     */
    private Integer id;

    /**
     * 字段描述: 角色名称
     *
     * 字段名: sys_role.role_name
     *
     * @mbg.generated
     */
    private String roleName;

    /**
     * 字段描述: 角色状态（0正常 1停用 -1删除）
     *
     * 字段名: sys_role.role_status
     *
     * @mbg.generated
     */
    private Integer roleStatus;

    /**
     * 字段描述: 备注
     *
     * 字段名: sys_role.remark
     *
     * @mbg.generated
     */
    private String remark;
}