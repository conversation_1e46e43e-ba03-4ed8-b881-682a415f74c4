package com.dt.train.management.center.service.permission;


import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import com.dt.train.management.center.constant.CacheConstant;
import com.dt.train.management.center.mapper.SysPermissionDOMapper;
import com.dt.train.management.center.model.dao.SysPermissionDO;
import com.dt.train.management.center.model.vo.permission.SysPermissionTreeVO;
import com.dt.train.management.center.utils.RedisUtil;

import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
public class SysPermissionService {

    private final SysPermissionDOMapper sysPermissionMapper;

    private final RedisUtil redisUtil;

    public SysPermissionService(SysPermissionDOMapper sysPermissionMapper, RedisUtil redisUtil) {
        this.sysPermissionMapper = sysPermissionMapper;
        this.redisUtil = redisUtil;
    }

    /**
     * 生成权限树并缓存到Redis
     */
    public List<SysPermissionTreeVO> getPermissionTree() {
        String cachedTree = redisUtil.get(CacheConstant.SYS_PERMISSION_TREE);
        if (cachedTree != null) {
            // 如果缓存存在，直接返回
            return com.alibaba.fastjson.JSON.parseArray(cachedTree, SysPermissionTreeVO.class);
        }
        List<SysPermissionDO> allPermissions = sysPermissionMapper.selectAll();
        List<SysPermissionTreeVO> tree = buildTree(allPermissions);
        redisUtil.set(CacheConstant.SYS_PERMISSION_TREE, com.alibaba.fastjson.JSON.toJSONString(tree), 60 * 60 * 24); // 缓存一天
        return tree;
    }

    /**
     * 构建树形结构
     */
    private List<SysPermissionTreeVO> buildTree(List<SysPermissionDO> allPermissions) {
        Map<Integer, SysPermissionTreeVO> map = new HashMap<>();
        List<SysPermissionTreeVO> roots = new ArrayList<>();
        for (SysPermissionDO perm : allPermissions) {
            SysPermissionTreeVO vo = new SysPermissionTreeVO();
            BeanUtils.copyProperties(perm, vo);
            vo.setChildren(new ArrayList<>());
            map.put(vo.getId(), vo);
        }
        for (SysPermissionTreeVO vo : map.values()) {
            if (vo.getParentId() == null || vo.getParentId() == 0) {
                roots.add(vo);
            } else {
                SysPermissionTreeVO parent = map.get(vo.getParentId());
                if (parent != null) {
                    parent.getChildren().add(vo);
                } else {
                    roots.add(vo); // parent not found, treat as root
                }
            }
        }
        // 按sortOrder排序
        sortTree(roots);
        return roots;
    }

    private void sortTree(List<SysPermissionTreeVO> nodes) {
        if (nodes == null) return;
        nodes.sort(Comparator.comparing(SysPermissionTreeVO::getSortOrder, Comparator.nullsLast(Integer::compareTo)));
        for (SysPermissionTreeVO node : nodes) {
            sortTree(node.getChildren());
        }
    }

}
