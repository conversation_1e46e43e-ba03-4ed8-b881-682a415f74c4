package com.dt.train.management.center.web.manager.log;

import java.util.List;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.dt.framework.business.dto.web.common.WebResult;
import com.dt.train.management.center.enums.LogTypeEnum;
import com.dt.train.management.center.model.dto.log.CommonLogQueryDTO;
import com.dt.train.management.center.model.vo.log.CommonLogVO;
import com.dt.train.management.center.service.log.CommonLogService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;

@Api(tags = "日志管理")
@RestController
@RequestMapping("/manager/log")
public class CommonLogManagerController {

    private final CommonLogService commonLogService;

    public CommonLogManagerController(CommonLogService commonLogService) {
        this.commonLogService = commonLogService;
    }

    @ApiOperation(value = "查询日志")
    @GetMapping("/{logType}/{logSecondType}")
    public WebResult<List<CommonLogVO>> list(
        @ApiParam(value = "日志类型") @PathVariable LogTypeEnum logType
        , @ApiParam(value = "日志二级类型") @PathVariable String logSecondType
        , @ApiParam(value = "用户id") @RequestParam(required = false) String userId
        , @ApiParam(value = "用户名称") @RequestParam(required = false) String userName
        , @ApiParam(value = "业务id") @RequestParam(required = false) String businessId
        , @ApiParam(value = "业务类型") @RequestParam(required = false) String businessType) {
        CommonLogQueryDTO commonLogQueryDTO = new CommonLogQueryDTO();
        commonLogQueryDTO.setLogType(logType.getCode());
        commonLogQueryDTO.setLogSecondType(logSecondType);
        commonLogQueryDTO.setUserId(userId);
        commonLogQueryDTO.setUserName(userName);
        commonLogQueryDTO.setBusinessId(businessId);
        commonLogQueryDTO.setBusinessType(businessType);
        return WebResult.successData(commonLogService.list(commonLogQueryDTO));
    }

}
