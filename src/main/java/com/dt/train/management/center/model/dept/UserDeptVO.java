package com.dt.train.management.center.model.dept;

import com.dt.train.management.center.model.vo.user.TrainUserVO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class UserDeptVO {
    private Integer deptId;
    private Integer parentDeptId;
    private String deptName;
    private String deptNameShort;
    private Integer level;
    private Integer userCount;
    List<TrainUserVO> userInfoList;
    private List<UserDeptVO> children;
}
