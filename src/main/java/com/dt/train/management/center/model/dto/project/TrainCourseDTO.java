package com.dt.train.management.center.model.dto.project;

import java.util.Date;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class TrainCourseDTO {

     /**
     * 字段描述: 主键ID
     *
     * 字段名: train_course.id
     *
     */
    @ApiModelProperty(value = "主键ID")
    private Integer id;

    /**
     * 字段描述: 项目ID
     *
     * 字段名: train_course.project_id
     *
     */
    @ApiModelProperty(value = "项目ID")
    private Integer projectId;

    /**
     * 字段描述: 项目名称
     *
     * 字段名: train_course.project_name
     *
     */
    @ApiModelProperty(value = "项目名称")
    private String projectName;

    /**
     * 字段描述: 项目编码
     *
     * 字段名: train_course.project_code
     *
     */
    @ApiModelProperty(value = "项目编码")
    private String projectCode;

    /**
     * 字段描述: 班次名称
     *
     * 字段名: train_course.course_name
     *
     */
    @ApiModelProperty(value = "班次名称")
    private String courseName;

    /**
     * 字段描述: 班次状态,0-未开始，1-进行中，2-已完成
     *
     * 字段名: train_course.course_status
     *
     */
    @ApiModelProperty(value = "班次状态,0-未开始，1-进行中，2-已完成")
    private Integer courseStatus;

    /**
     * 字段描述: 班次类型,train_course_type
     *
     * 字段名: train_course.course_type
     *
     */
    @ApiModelProperty(value = "班次类型,train_course_type")
    private Integer courseType;

    /**
     * 字段描述: 班次人数
     *
     * 字段名: train_course.course_people_num
     *
     */
    @ApiModelProperty(value = "班次人数")
    private Integer coursePeopleNum;

    /**
     * 字段描述: 服务对象
     *
     * 字段名: train_course.service_object
     *
     */
    @ApiModelProperty(value = "服务对象")
    private String serviceObject;

    /**
     * 字段描述: 是否异地，0-否，1-是
     *
     * 字段名: train_course.is_remote
     *
     */
    @ApiModelProperty(value = "是否异地，0-否，1-是")
    private Integer isRemote;

    /**
     * 字段描述: 班次执行区域
     *
     * 字段名: train_course.course_area
     *
     */
    @ApiModelProperty(value = "班次执行区域")
    private String courseArea;

    /**
     * 字段描述: 班次执行区域id
     *
     * 字段名: train_course.course_area_id
     *
     */
    @ApiModelProperty(value = "班次执行区域id")
    private Integer courseAreaId;

    /**
     * 字段描述: 省份id
     *
     * 字段名: train_course.province_id
     *
     */
    @ApiModelProperty(value = "省份id")
    private String provinceId;

    /**
     * 字段描述: 城市id
     *
     * 字段名: train_course.city_id
     *
     */
    @ApiModelProperty(value = "城市id")
    private String cityId;

    /**
     * 字段描述: 地区id
     *
     * 字段名: train_course.area_id
     *
     */
    @ApiModelProperty(value = "地区id")
    private String areaId;

    /**
     * 字段描述: 省份
     *
     * 字段名: train_course.province
     *
     */
    @ApiModelProperty(value = "省份")
    private String province;

    /**
     * 字段描述: 城市
     *
     * 字段名: train_course.city
     *
     */
    @ApiModelProperty(value = "城市")
    private String city;

    /**
     * 字段描述: 区县
     *
     * 字段名: train_course.area
     *
     */
    @ApiModelProperty(value = "区县")
    private String area;

    /**
     * 字段描述: 是否填写计划说明，0-否，1-是
     *
     * 字段名: train_course.has_plan_desc
     *
     */
    @ApiModelProperty(value = "是否填写计划说明，0-否，1-是")
    private Boolean hasPlanDesc;

    /**
     * 字段描述: 是否填写执行进展说明，0-否，1-是
     *
     * 字段名: train_course.has_execution_progress
     *
     */
    @ApiModelProperty(value = "是否填写执行进展说明，0-否，1-是")
    private Boolean hasExecutionProgress;

    /**
     * 字段描述: 计划开始时间
     *
     * 字段名: train_course.plan_start_date
     *
     */
    @ApiModelProperty(value = "计划开始时间")
    private Date planStartDate;

    /**
     * 字段描述: 计划结束时间
     *
     * 字段名: train_course.plan_end_date
     *
     */
    @ApiModelProperty(value = "计划结束时间")
    private Date planEndDate;

    /**
     * 字段描述: 实际开始时间
     *
     * 字段名: train_course.real_start_date
     *
     */
    @ApiModelProperty(value = "实际开始时间")
    private Date realStartDate;

    /**
     * 字段描述: 实际结束时间
     *
     * 字段名: train_course.real_end_date
     *
     */
    @ApiModelProperty(value = "实际结束时间")
    private Date realEndDate;
    
}
