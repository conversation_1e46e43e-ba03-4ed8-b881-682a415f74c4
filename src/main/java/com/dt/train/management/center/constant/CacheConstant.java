package com.dt.train.management.center.constant;

public interface CacheConstant {

    String FXIAOKE_TOKEN_KEY = "train-management-center:fxiaoke:token";

    String DICT_MAPPING_KEY = "train-management-center:dict-mapping:%s";

    String CONFIG_KEY = "train-management-center:config";

    String DINGDING_TOKEN_KEY = "train-management-center:dingding:token";

    String DINGDING_ACCESS_ALL_DEPT_KEY = "train-management-center:dingding:access-all-dept";

    String DINGDING_ACCESS_DEPT_USER_KEY = "train-management-center:dingding:access-dept:user:%s";

    String SYNC_CRM_PROJECT_PROCESS_KEY = "train-management-center:sync-crm-project-process:%s";

    String TRAIN_USER_INFO_KEY = "train-management-center:train-user:info:%s";

    String SYS_PERMISSION_TREE = "train-management-center:sys-permission:tree";

    static String getDictMappingKey(String dictType) {
        return String.format(DICT_MAPPING_KEY, dictType);
    }
    
    static String getDingdingAccessDeptUserKey(String deptName) {
        return String.format(DINGDING_ACCESS_DEPT_USER_KEY, deptName);
    }

    static String getSyncCrmProjectProcessKey(String syncRequestId) {
        return String.format(SYNC_CRM_PROJECT_PROCESS_KEY, syncRequestId);
    }

    static String getTrainUserInfoKey(String userId) {
        return String.format(TRAIN_USER_INFO_KEY, userId);
    }
    
    /**
     * 离职用户ID集合
     */
    String USER_STATUS_RESIGNED_SET = "train-management-center:user-status:resigned-set";
}
