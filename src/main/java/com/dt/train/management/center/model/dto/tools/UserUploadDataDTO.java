package com.dt.train.management.center.model.dto.tools;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@EqualsAndHashCode(callSuper = false)
public class UserUploadDataDTO {
    @ExcelProperty("姓名")
    private String name;

    @ExcelProperty("手机号")
    private String mobile;

    @ExcelProperty("本系统角色")
    private String role;

    @ExcelProperty("本系统所属部门名称")
    private String deptName;

    private  int lineNum;

}
