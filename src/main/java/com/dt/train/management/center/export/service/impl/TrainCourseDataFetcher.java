package com.dt.train.management.center.export.service.impl;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.dt.train.management.center.enums.CourseFormEnum;
import com.dt.train.management.center.enums.TrainCourseAreaRelationTypeEnum;
import com.dt.train.management.center.enums.TrainCourseUserRelationTypeEnum;
import com.dt.train.management.center.export.enums.ReportType;
import com.dt.train.management.center.export.models.TrainCourseExcelDTO;
import com.dt.train.management.center.export.service.ReportDataFetcher;
import com.dt.train.management.center.model.dto.project.TrainCourseAreaRelationDTO;
import com.dt.train.management.center.model.dto.project.TrainCourseUserRelationDTO;
import com.dt.train.management.center.model.vo.project.TrainCourseVO;
import com.dt.train.management.center.service.project.TrainCourseAreaRelationService;
import com.dt.train.management.center.service.project.TrainCourseService;
import com.dt.train.management.center.service.project.TrainCourseUserRelationService;

import lombok.extern.slf4j.Slf4j;

@Component
@Slf4j
public class TrainCourseDataFetcher implements ReportDataFetcher {

    @Autowired
    private TrainCourseService trainCourseService;

    @Autowired
    private TrainCourseAreaRelationService trainCourseAreaRelationService;

    @Autowired
    private TrainCourseUserRelationService trainCourseUserRelationService;

    private static final Pattern IMG_PATTERN = Pattern.compile("<img[^>]+src\\s*=\\s*['\"]([^'\"]+)['\"][^>]*>");

    private static final SimpleDateFormat DATE_FORMAT = new SimpleDateFormat("yyyy/MM/dd HH:mm:ss");

    @Override
    public boolean supports(ReportType reportType) {
        return ReportType.TRAIN_COURSE_REPORT.equals(reportType);
    }

    @Override
    public List<?> fetchData(Map<String, Object> parameters) {
        log.info("Fetching train course data with parameters: {}", parameters);
        // 在实际应用中，会根据 parameters 查询数据库获取 TrainCourseDTO 列表
        // List<TrainCourseDTO> rawCourses = trainCourseService.queryTrainCourses(projectId, courseName, planStartDate, ...);
        if (!(parameters.get("ids") instanceof String)) {
            return new ArrayList<>();
        }
        String ids = (String) parameters.get("ids");
        List<Integer> idList = Arrays.asList(ids.split(",")).stream().map(Integer::parseInt).collect(Collectors.toList());
        List<TrainCourseVO> trainCourseVOs = trainCourseService.listByIds(idList, true);
        if(trainCourseVOs.isEmpty()) {
            return new ArrayList<>();
        }
        
        // 将列表转换为Map，便于根据ID查找
        Map<Integer, TrainCourseVO> courseMap = trainCourseVOs.stream()
            .collect(Collectors.toMap(TrainCourseVO::getId, course -> course));

        // 按照idList的顺序重新构建排序后的列表
        List<TrainCourseVO> sortedTrainCourseVOs = idList.stream()
            .map(courseMap::get)
            .filter(Objects::nonNull) // 过滤掉可能不存在的课程
            .collect(Collectors.toList());
        
        // 获取班次区域关系
        List<TrainCourseAreaRelationDTO> areaRelations = trainCourseAreaRelationService.listByCourseIds(idList);
        // 将区域关系转换为 Map<courseId, List<TrainCourseAreaRelationDTO>>
        Map<Integer, List<TrainCourseAreaRelationDTO>> courseAreaMap = areaRelations.stream()
            .collect(Collectors.groupingBy(TrainCourseAreaRelationDTO::getCourseId));
        // 获取班次人员关系
        List<TrainCourseUserRelationDTO> userRelations = trainCourseUserRelationService.listByCourseIds(idList);
        // 将人员关系转换为 Map<courseId, List<TrainCourseUserRelationDTO>> 
        Map<Integer, List<TrainCourseUserRelationDTO>> courseUserMap = userRelations.stream()
            .collect(Collectors.groupingBy(TrainCourseUserRelationDTO::getCourseId));

        // 将 TrainCourseDTO 转换为 TrainCourseExcelDTO
        List<TrainCourseExcelDTO> excelDTOs = new ArrayList<>();
        for (TrainCourseVO course : sortedTrainCourseVOs) {
            TrainCourseExcelDTO dto = new TrainCourseExcelDTO();
            dto.setId(course.getId());
            dto.setProjectCode(course.getProjectCode());
            dto.setProjectName(course.getProjectName());
            dto.setCourseForm(course.getCourseForm() != null ? CourseFormEnum.getByCode(course.getCourseForm()).getName() : null);
            dto.setCourseName(course.getCourseName());
            // 班次状态转换
            dto.setCourseStatus(course.getCourseStatus() != null ? (
                course.getCourseStatus() == 0 ? "未开始" :
                course.getCourseStatus() == 1 ? "进行中" :
                course.getCourseStatus() == 2 ? "已完成" : "未知"
            ) : null);
            dto.setCourseTypeName(course.getCourseTypeName());
            dto.setCoursePeopleNum(course.getCoursePeopleNum());
            dto.setServiceObject(course.getServiceObject());
            // 是否异地转换
            dto.setIsRemote(course.getIsRemote() != null ? (course.getIsRemote() == 1 ? "异地面授" : "本地面授") : null);
            dto.setCourseArea(course.getCourseArea());
            // 获取班次区域关系
            List<TrainCourseAreaRelationDTO> areaRelationsForCourse = courseAreaMap.get(course.getId());
            
            if (areaRelationsForCourse != null && !areaRelationsForCourse.isEmpty()) {
                String provinceCityStr = areaRelationsForCourse.stream()
                    .filter(relation -> relation.getRelationType() == TrainCourseAreaRelationTypeEnum.EXECUTE.getCode())
                    .map(relation -> {
                        List<String> parts = new ArrayList<>();
                        if (relation.getProvince() != null && !relation.getProvince().isEmpty()) {
                            parts.add(relation.getProvince());
                        }
                        if (relation.getCity() != null && !relation.getCity().isEmpty()) {
                            parts.add(relation.getCity());
                        }
                        if (relation.getArea() != null && !relation.getArea().isEmpty()) {
                            parts.add(relation.getArea());
                        }
                        return String.join("-", parts);
                    })
                    .collect(Collectors.joining(","));
                dto.setCourseProvinceCity(provinceCityStr);
            } else {
                dto.setCourseProvinceCity("");
            }
            // 布尔值转换
            String planDesc = course.getPlanDesc();
            if (planDesc == null) {
                planDesc = "";
            }
            // 提取文本内容
            String textContent = planDesc.replaceAll("<[^>]*>", "").trim();
            dto.setPlanDescTxt(textContent);
            // 使用预编译模式提取图片链接
            Matcher matcher = IMG_PATTERN.matcher(planDesc);
            if (matcher.find()) {
                String imageUrl = matcher.group(1);
                dto.setPlanDescImage(imageUrl);
            } else {
                dto.setPlanDescImage("");
            }
            if(course.getPlanStartDate() != null && course.getPlanStartDate().isValid() && course.getPlanEndDate() != null && course.getPlanEndDate().isValid()) {
                dto.setContractDate(String.format("%s - %s", course.getPlanStartDate().toString(), course.getPlanEndDate().toString()));
                dto.setContractDays(course.getPlanEndDate().daysBetweenWithHalf(course.getPlanStartDate()));
            }
            if(course.getRealStartDate() != null && course.getRealStartDate().isValid() && course.getRealEndDate() != null && course.getRealEndDate().isValid()) {
                dto.setRealDate(String.format("%s - %s", course.getRealStartDate().toString(), course.getRealEndDate().toString()));
                dto.setRealDays(course.getRealEndDate().daysBetweenWithHalf(course.getRealStartDate()));
            } else {
                dto.setRealDate("");
                dto.setRealDays(null);
            }
            // 获取班次人员关系
            List<TrainCourseUserRelationDTO> userRelationsForCourse = courseUserMap.getOrDefault(course.getId(), new ArrayList<>());
            List<TrainCourseUserRelationDTO> executeUsers = userRelationsForCourse.stream()
                .filter(user -> user.getRelationType() == TrainCourseUserRelationTypeEnum.EXECUTE.getCode()) // 执行人
                .collect(Collectors.toList());
            List<TrainCourseUserRelationDTO> supportUsers = userRelationsForCourse.stream()
                .filter(user -> user.getRelationType() == TrainCourseUserRelationTypeEnum.SUPPORT.getCode()) // 支援人
                .collect(Collectors.toList());
            course.setSupportUsers(supportUsers);
            course.setExecuteUsers(executeUsers);
            // 设置执行人和支援人
            if (executeUsers != null) {
                String executors = executeUsers.stream()
                    .map(TrainCourseUserRelationDTO::getUserName)
                    .collect(Collectors.joining(","));
                dto.setExecutors(executors);
            } else {
                dto.setExecutors("");
            }
            
            if (supportUsers != null) {
                String supporters = supportUsers.stream()
                    .map(TrainCourseUserRelationDTO::getUserName)
                    .collect(Collectors.joining(","));
                dto.setSupporters(supporters);
            } else {
                dto.setSupporters("");
            }
            
            dto.setScheduledPersonCount(course.getScheduledPersonCount());
            // 设置班次人天
            dto.setCourseManDay(course.getPlanUserDays());
            
            // 处理执行进展说明
            String executionProgress = course.getExecutionProgress();
            if (executionProgress == null) {
                executionProgress = "";
            }
            // 提取文本内容
            String progressTextContent = executionProgress.replaceAll("<[^>]*>", "").trim();
            dto.setProgressDescTxt(progressTextContent);
            // 使用预编译模式提取进展图片
            Matcher progressMatcher = IMG_PATTERN.matcher(executionProgress);
            if (progressMatcher.find()) {
                String imageUrl = progressMatcher.group(1);
                dto.setProgressDescImage(imageUrl);
            } else {
                dto.setProgressDescImage("");
            }
            dto.setGmtCreate(DATE_FORMAT.format(course.getGmtCreate()));
            excelDTOs.add(dto);
        }
        return excelDTOs;
    }
}
