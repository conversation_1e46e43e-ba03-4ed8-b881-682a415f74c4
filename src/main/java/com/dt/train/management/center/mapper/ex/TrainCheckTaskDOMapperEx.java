package com.dt.train.management.center.mapper.ex;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.dt.train.management.center.mapper.TrainCheckTaskDOMapper;
import com.dt.train.management.center.model.dao.TrainCheckTaskDO;
import com.dt.train.management.center.model.dto.task.TrainCheckTaskDTOWithCourse;
import com.dt.train.management.center.model.dto.task.TrainCheckTaskQueryDTO;

public interface TrainCheckTaskDOMapperEx extends TrainCheckTaskDOMapper {

    /**
     * 根据查询条件查询打卡任务
     * @param trainCheckTaskQueryDTO
     * @return
     */
    List<TrainCheckTaskDTOWithCourse> listByQuery(TrainCheckTaskQueryDTO trainCheckTaskQueryDTO);

    /**
     * 根据查询条件查询打卡任务数量
     * @param trainCheckTaskQueryDTO
     * @return
     */
    Integer countByQuery(TrainCheckTaskQueryDTO trainCheckTaskQueryDTO);

    void insertBatch(@Param("list") List<TrainCheckTaskDO> trainCheckTaskDOs);

}
