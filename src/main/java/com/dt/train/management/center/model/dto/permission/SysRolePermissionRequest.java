package com.dt.train.management.center.model.dto.permission;

import java.util.List;

import javax.validation.constraints.NotNull;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class SysRolePermissionRequest {

    /** 角色ID */
    @NotNull(message = "角色ID不能为空")
    @ApiModelProperty(value = "角色ID", required = true)
    private Integer roleId;

    /** 权限Key列表 */
    @ApiModelProperty(value = "权限Key列表", required = true)
    private List<String> permissionKeys;

}
