package com.dt.train.management.center.model.dao;

import java.util.Date;

/**
 * 
 * 表名: train_course_area_relation
 *
 * @mbg.generated
 */
@lombok.Data
public class TrainCourseAreaRelationDO {
    /**
     * 字段描述: 主键ID
     *
     * 字段名: train_course_area_relation.id
     *
     * @mbg.generated
     */
    private Integer id;

    /**
     * 字段描述: 项目ID
     *
     * 字段名: train_course_area_relation.project_id
     *
     * @mbg.generated
     */
    private Integer projectId;

    /**
     * 字段描述: 班次ID
     *
     * 字段名: train_course_area_relation.course_id
     *
     * @mbg.generated
     */
    private Integer courseId;

    /**
     * 字段描述: 关联类型
     *
     * 字段名: train_course_area_relation.relation_type
     *
     * @mbg.generated
     */
    private Integer relationType;

    /**
     * 字段描述: 省份id
     *
     * 字段名: train_course_area_relation.province_id
     *
     * @mbg.generated
     */
    private String provinceId;

    /**
     * 字段描述: 城市id
     *
     * 字段名: train_course_area_relation.city_id
     *
     * @mbg.generated
     */
    private String cityId;

    /**
     * 字段描述: 地区id
     *
     * 字段名: train_course_area_relation.area_id
     *
     * @mbg.generated
     */
    private String areaId;

    /**
     * 字段描述: 省份
     *
     * 字段名: train_course_area_relation.province
     *
     * @mbg.generated
     */
    private String province;

    /**
     * 字段描述: 城市
     *
     * 字段名: train_course_area_relation.city
     *
     * @mbg.generated
     */
    private String city;

    /**
     * 字段描述: 区县
     *
     * 字段名: train_course_area_relation.area
     *
     * @mbg.generated
     */
    private String area;

    /**
     * 字段描述: 是否已删除 0 否 1 是
     *
     * 字段名: train_course_area_relation.invalid
     *
     * @mbg.generated
     */
    private Integer invalid;

    /**
     * 字段描述: 创建人
     *
     * 字段名: train_course_area_relation.created_by
     *
     * @mbg.generated
     */
    private Long createdBy;

    /**
     * 字段描述: 更新人
     *
     * 字段名: train_course_area_relation.updated_by
     *
     * @mbg.generated
     */
    private Long updatedBy;

    /**
     * 字段描述: 创建时间
     *
     * 字段名: train_course_area_relation.gmt_create
     *
     * @mbg.generated
     */
    private Date gmtCreate;

    /**
     * 字段描述: 修改时间
     *
     * 字段名: train_course_area_relation.gmt_modified
     *
     * @mbg.generated
     */
    private Date gmtModified;
}