package com.dt.train.management.center.model.dto.user;

import com.dt.train.management.center.feign.result.UserDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.List;
@Data
public class UserAndDepartmentMaintDTO {

    @ApiModelProperty(value = "用户信息(姓名、手机号)", required = true)
    @NotEmpty(message = "用户信息不能为空")
    private List<UserDTO> userDTOList;

    @ApiModelProperty(value = "排班系统部门名称", required = true)
    @NotBlank(message = "排班系统部门名称不能为空(目前没有一个人有多个部门情况)")
    private List<String> departmentNameList;
    @ApiModelProperty(value = "排班系统角色", required = true)
    @NotBlank(message = "排班系统角色不能为空")
    private String roleName;

    @ApiModelProperty(value = "更新用户信息时部门操作类型 ADD:为用户所属部门增加指定部门，UPDATE:修改用户所属部门为指定部门", required = true)
    @NotBlank(message = "操作类型不能为空")
    private String operationType;

}
