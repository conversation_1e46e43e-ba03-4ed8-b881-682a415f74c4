package com.dt.train.management.center.config;

import java.sql.Connection;

import org.apache.ibatis.executor.statement.StatementHandler;
import org.apache.ibatis.plugin.Interceptor;
import org.apache.ibatis.plugin.Intercepts;
import org.apache.ibatis.plugin.Invocation;
import org.apache.ibatis.plugin.Signature;
import org.apache.ibatis.reflection.MetaObject;
import org.apache.ibatis.reflection.SystemMetaObject;
import org.springframework.beans.factory.annotation.Autowired;

import lombok.extern.slf4j.Slf4j;

@Intercepts({
    @Signature(type = StatementHandler.class, method = "prepare", args = {Connection.class, Integer.class})
})
@Slf4j
public class SqlInterceptor implements Interceptor {

    @Autowired
    private BusinessConfig businessConfig;
    
    @Override
    public Object intercept(Invocation invocation) throws Throwable {
        if(!businessConfig.getLogSqlEnable()){
            return invocation.proceed();
        }
        StatementHandler handler = (StatementHandler) invocation.getTarget();
        MetaObject metaObject = SystemMetaObject.forObject(handler);
        
        // 获取完整 SQL（含动态参数）
        String sql = handler.getBoundSql().getSql();
        
        // 获取真实参数值
        Object param = metaObject.getValue("parameterHandler.parameterObject");
        
        log.info("Executed SQL: {}", sql);
        log.info("Parameters: {}", param);
        
        return invocation.proceed();
      }
}
