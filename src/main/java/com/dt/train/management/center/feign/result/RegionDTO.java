package com.dt.train.management.center.feign.result;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 省市区
 * @author: zhangheng
 * @time: 2018-10-22  10:41
 */
@Data
public class RegionDTO {

    /**
     * 字段描述: 行政区编码
     */
    private Long id;

    /**
     * 字段描述: 名称
     */
    private String name;

    /**
     * 字段描述: 简称
     */
    private String fullName;

    /**
     * 字段描述: 父节点编码
     */
    private Long pid;

    /**
     * 高德地图地址码
     */
    private String gaodeAdcode;

    /**
     * 教研网行政区地址码
     */
    private String yanxiuAdcode;

    /**
     * 字段描述: 是否是叶子结点
     */
    private Integer isLeaf;

    /**
     * 字段描述: 数据来源(1:三人行 2:教研网)
     */
    private Integer resourceRoot;

    /**
     * 字段描述: 经度
     */
    private BigDecimal lng;

    /**
     * 字段描述: 纬度
     */
    private BigDecimal lat;

    /**
     * 字段描述: 层级
     */
    private Integer level;

    /**
     * 字段描述: 备注
     */
    private String comments;

    /**
     * 字段描述: 创建人
     */
    private String createdBy;

    /**
     * 字段描述: 最后更新人
     */
    private String modifiedBy;

    /**
     * 字段描述: 创建时间
     */
    private Date gmtCreate;

    /**
     * 字段描述: 更新时间
     */
    private Date gmtModified;

    /**
     * 字段描述: 删除:(0:有效,1:删除)
     */
    private Integer yn;
    /**
     * 拼音首字母
     */
    @ApiModelProperty("拼音首字母")
    private String letter;
}