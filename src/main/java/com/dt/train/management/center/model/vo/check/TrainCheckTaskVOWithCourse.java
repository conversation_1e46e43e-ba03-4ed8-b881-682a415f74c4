package com.dt.train.management.center.model.vo.check;

import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;

/**
 * 
 * 表名: train_check_task
 *
 * @mbg.generated
 */
@lombok.Data
@EqualsAndHashCode(callSuper = true)
public class TrainCheckTaskVOWithCourse extends TrainCheckTaskVO {
    
    @ApiModelProperty(value = "项目名称")
    private String projectName;

    @ApiModelProperty(value = "项目编码")
    private String projectCode;

    @ApiModelProperty(value = "班次名称")
    private String courseName;

    @ApiModelProperty(value = "班次形式，1-面授，2-远程直播，3-现场直播")
    private Integer courseForm;
    
}