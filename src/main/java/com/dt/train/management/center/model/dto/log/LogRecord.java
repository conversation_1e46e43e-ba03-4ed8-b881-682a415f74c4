package com.dt.train.management.center.model.dto.log;

import java.time.LocalDateTime;

import lombok.Data;

@Data
public class LogRecord {

    // @Id // 如果使用JPA
    // @GeneratedValue(strategy = GenerationType.IDENTITY) // 如果使用JPA
    private Long id;

    private Long operatorId;       // 操作人ID
    private String operatorName;     // 操作人名称

    private String businessModule;   // 业务模块 (来自枚举)

    private String action;           // 操作类型/描述

    private String targetUserId;     // 目标用户ID (来自枚举的targetUserIdTemplate字段，SpEL解析后)

    private String businessId;       // 业务ID (来自枚举的name字段，SpEL解析后)

    private String content;          // 日志详细内容 (来自枚举的contentTemplate字段，SpEL解析后)

    private String methodName;       // 被调用的方法名 (全限定名)
    // @Lob // 如果使用JPA且内容可能很长
    private String parameters;       // 方法参数 (JSON格式)
    private LocalDateTime timestamp; // 操作时间
    private String ipAddress;        // IP地址
    private String status;           // 执行状态 (成功/失败)
    // @Lob // 如果使用JPA且内容可能很长
    private String errorDetails;     // 错误详情 (如果操作失败)

}