package com.dt.train.management.center.service.user;

import com.dt.train.management.center.model.dao.TrainDeptDO;
import com.dt.train.management.center.model.dto.tools.UserUploadDataDTO;
import com.dt.train.management.center.model.response.fxiaoke.FxiaokeUserQueryResponse;
import com.dt.train.management.center.model.user.DingdingUserVO;
import org.springframework.stereotype.Service;

import java.util.List;


@Service
public interface UserAndDepartmentMaintenanceService {

    /**
     * 新增指定用户和部门关系（支持批量）
     *
     * @param uploadDataDTOList 用户上传数据dto列表
     * @param operationType 操作类型
     */
    void batchSaveOrUpdate(List<UserUploadDataDTO> uploadDataDTOList, String operationType, List<DingdingUserVO> dingdingUserVOS, List<TrainDeptDO> trainDeptDOs,
                           List<String> mobileList,List<FxiaokeUserQueryResponse.Employee> empList);

    /**
     * 定时任务同步用户在职状态及个人信息
     */
    void syncUserStatusAndInfo();

    /**
     * 根据用户手机号查询userId,如果查询不到返回一个比现存不存在userId-1的数字
     */
    Long queryUserIdByMobile(String mobile);

    /**
     * 同步更新所有之前没有在CRM注册过的用户
     */
    void syncUserNotExistsInCrm();

}
