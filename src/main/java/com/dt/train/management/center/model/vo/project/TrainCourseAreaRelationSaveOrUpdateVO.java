package com.dt.train.management.center.model.vo.project;

/**
 * 
 * 表名: train_course_area_relation
 *
 * @mbg.generated
 */
@lombok.Data
public class TrainCourseAreaRelationSaveOrUpdateVO {
    /**
     * 字段描述: 主键ID
     *
     * 字段名: train_course_area_relation.id
     *
     * @mbg.generated
     */
    private Integer id;

    /**
     * 字段描述: 项目ID
     *
     * 字段名: train_course_area_relation.project_id
     *
     * @mbg.generated
     */
    private Integer projectId;

    /**
     * 字段描述: 班次ID
     *
     * 字段名: train_course_area_relation.course_id
     *
     * @mbg.generated
     */
    private Integer courseId;

    /**
     * 字段描述: 省份id
     *
     * 字段名: train_course_area_relation.province_id
     *
     * @mbg.generated
     */
    private String provinceId;

    /**
     * 字段描述: 城市id
     *
     * 字段名: train_course_area_relation.city_id
     *
     * @mbg.generated
     */
    private String cityId;

    /**
     * 字段描述: 地区id
     *
     * 字段名: train_course_area_relation.area_id
     *
     * @mbg.generated
     */
    private String areaId;

    /**
     * 字段描述: 省份
     *
     * 字段名: train_course_area_relation.province
     *
     * @mbg.generated
     */
    private String province;

    /**
     * 字段描述: 城市
     *
     * 字段名: train_course_area_relation.city
     *
     * @mbg.generated
     */
    private String city;

    /**
     * 字段描述: 区县
     *
     * 字段名: train_course_area_relation.area
     *
     * @mbg.generated
     */
    private String area;
}