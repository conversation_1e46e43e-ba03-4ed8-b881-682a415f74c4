package com.dt.train.management.center.mapper;

import com.dt.train.management.center.model.dao.TrainProjectDO;

public interface TrainProjectDOMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(TrainProjectDO row);

    int insertSelective(TrainProject<PERSON><PERSON> row);

    TrainProjectDO selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(TrainProjectDO row);

    int updateByPrimaryKey(TrainProjectDO row);
}