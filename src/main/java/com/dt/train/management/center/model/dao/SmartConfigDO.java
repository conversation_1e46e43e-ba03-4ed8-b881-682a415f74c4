package com.dt.train.management.center.model.dao;

import java.util.Date;

/**
 * 
 * 表名: smart_config
 *
 * @mbg.generated
 */
@lombok.Data
public class SmartConfigDO {
    /**
     * 字段描述: 主键
     *
     * 字段名: smart_config.id
     *
     * @mbg.generated
     */
    private Integer id;

    /**
     * 字段描述: 配置键
     *
     * 字段名: smart_config.config_key
     *
     * @mbg.generated
     */
    private String configKey;

    /**
     * 字段描述: 配置键
     *
     * 字段名: smart_config.second_key
     *
     * @mbg.generated
     */
    private String secondKey;

    /**
     * 字段描述: 排序
     *
     * 字段名: smart_config.config_order
     *
     * @mbg.generated
     */
    private Integer configOrder;

    /**
     * 字段描述: 扩展信息
     *
     * 字段名: smart_config.ext_info
     *
     * @mbg.generated
     */
    private String extInfo;

    /**
     * 字段描述: 配置类型 0 single, 1 chunk
     *
     * 字段名: smart_config.config_type
     *
     * @mbg.generated
     */
    private Integer configType;

    /**
     * 字段描述: 是否已删除 0 否 1 是
     *
     * 字段名: smart_config.invalid
     *
     * @mbg.generated
     */
    private Integer invalid;

    /**
     * 字段描述: 创建人
     *
     * 字段名: smart_config.created_by
     *
     * @mbg.generated
     */
    private Long createdBy;

    /**
     * 字段描述: 修改人
     *
     * 字段名: smart_config.updated_by
     *
     * @mbg.generated
     */
    private Long updatedBy;

    /**
     * 字段描述: 创建时间
     *
     * 字段名: smart_config.gmt_create
     *
     * @mbg.generated
     */
    private Date gmtCreate;

    /**
     * 字段描述: 修改时间
     *
     * 字段名: smart_config.gmt_modified
     *
     * @mbg.generated
     */
    private Date gmtModified;

    /**
     * 字段描述: 配置值
     *
     * 字段名: smart_config.config_value
     *
     * @mbg.generated
     */
    private String configValue;
}