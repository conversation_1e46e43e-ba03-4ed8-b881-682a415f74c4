package com.dt.train.management.center.web.front;

import java.time.LocalDate;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

import javax.validation.Valid;

import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.dt.framework.business.constant.DtHeaders;
import com.dt.framework.business.dto.web.common.WebResult;
import com.dt.train.management.center.config.UserRequestContextHolder;
import com.dt.train.management.center.manager.TrainCheckTaskManager;
import com.dt.train.management.center.model.dto.task.TrainCheckRecordQueryDTO;
import com.dt.train.management.center.model.dto.task.TrainCheckRecordSaveDTO;
import com.dt.train.management.center.model.vo.check.TrainCheckRecordVO;
import com.dt.train.management.center.model.vo.check.TrainCheckWorkbenchVO;
import com.dt.train.management.center.model.vo.project.TrainCourseVOWithRecord;
import com.dt.train.management.center.service.task.TrainCheckRecordService;
import com.dt.train.management.center.utils.DateTimeUtils;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;

@RestController
@RequestMapping("/front/task/check")
@Api(tags = "打卡任务")
public class CheckTaskController {

    private final TrainCheckTaskManager trainCheckTaskManager;

    private final TrainCheckRecordService trainCheckRecordService;

    public CheckTaskController(TrainCheckTaskManager trainCheckTaskManager, TrainCheckRecordService trainCheckRecordService) {
        this.trainCheckTaskManager = trainCheckTaskManager;
        this.trainCheckRecordService = trainCheckRecordService;
    }

    @ApiOperation(value = "获取打卡日历")
    @GetMapping("/workbench")
    public WebResult<TrainCheckWorkbenchVO> getCheckWorkbench(
        @ApiParam(value = "指定用户ID", required = false) @RequestParam(required = false) Long userId
        , @ApiParam(value = "班次ID", required = false) @RequestParam(required = false) Integer courseId
        , @ApiParam(value = "开始日期,yyyy-MM-dd", required = true) @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate startDate
        , @ApiParam(value = "结束日期,yyyy-MM-dd", required = true) @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate endDate) {
        if(userId == null) {
            userId = UserRequestContextHolder.getRequestUserId();
        }
        TrainCheckWorkbenchVO trainCheckWorkbenchVO = trainCheckTaskManager.getCheckWorkbench(userId, courseId, startDate, endDate);
        return WebResult.successData(trainCheckWorkbenchVO);
    }

    @ApiModelProperty(value = "打卡签到")
    @PutMapping("/record")
    public WebResult<TrainCheckRecordVO> check(@RequestHeader(DtHeaders.USER_ID) Long userId
        , @Valid @RequestBody TrainCheckRecordSaveDTO trainCheckRecordSaveDTO) {
        trainCheckRecordSaveDTO.setUserId(userId);
        trainCheckRecordSaveDTO.setUserName(UserRequestContextHolder.getRequestUserName());
        trainCheckRecordSaveDTO.setCheckTime(new Date());
        TrainCheckRecordVO trainCheckRecordVO = trainCheckTaskManager.checkIn(trainCheckRecordSaveDTO);
        return WebResult.successData(trainCheckRecordVO);
    }

    @ApiOperation(value = "获取打卡记录")
    @GetMapping("/record")
    public WebResult<List<TrainCheckRecordVO>> getCheckRecord(
        @ApiParam(value = "指定用户ID", required = false) @RequestParam(required = false) Long userId
        , @ApiParam(value = "班次ID", required = false) @RequestParam(required = false) Integer courseId
        , @ApiParam(value = "开始日期,yyyy-MM-dd", required = true) @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate startDate
        , @ApiParam(value = "结束日期,yyyy-MM-dd", required = true) @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate endDate) {
            if(userId == null) {
                userId = UserRequestContextHolder.getRequestUserId();
            }
            TrainCheckRecordQueryDTO queryDTO = new TrainCheckRecordQueryDTO();
            queryDTO.setUserId(userId);
            if(courseId != null) {
                queryDTO.setCourseIds(Arrays.asList(courseId));
            }
            queryDTO.setStartCheckTime(DateTimeUtils.getStartOfDay(startDate));
            queryDTO.setEndCheckTime(DateTimeUtils.getEndOfDay(endDate));
        List<TrainCheckRecordVO> trainCheckRecordVOs = trainCheckRecordService.listByQuery(queryDTO);
        return WebResult.successData(trainCheckRecordVOs);
    }

    @ApiOperation(value = "获取班次信息 + 打卡记录")
    @GetMapping("/record/course")
    public WebResult<List<TrainCourseVOWithRecord>> getCourseWithRecord(
        @ApiParam(value = "指定用户ID", required = false) @RequestParam(required = false) Long userId
        , @ApiParam(value = "指定日期,yyyy-MM-dd", required = true) @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate atDate
        ) {
        if(userId == null) {
            userId = UserRequestContextHolder.getRequestUserId();
        }
        List<TrainCourseVOWithRecord> trainCourseVOWithRecords = trainCheckTaskManager.getCourseWithRecord(userId, atDate);
        return WebResult.successData(trainCourseVOWithRecords);
    }

}
