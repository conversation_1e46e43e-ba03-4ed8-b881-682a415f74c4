package com.dt.train.management.center.manager;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import com.dt.train.management.center.enums.HalfDayEnum;
import com.dt.train.management.center.model.common.HalfDayTime;
import com.dt.train.management.center.model.dto.project.CourseUserRelationQueryDTO;
import com.dt.train.management.center.model.dto.user.TrainUserWithQueryDTO;
import com.dt.train.management.center.model.vo.project.CalendarUserUnitVO;
import com.dt.train.management.center.model.vo.project.HalfDayCalendarVO;
import com.dt.train.management.center.model.vo.project.ScheduleCalendarVO;
import com.dt.train.management.center.model.vo.project.TrainCourseCalendarVO;
import com.dt.train.management.center.model.vo.project.TrainCourseUserRelationVOWithCourse;
import com.dt.train.management.center.model.vo.project.TrainCourseVO;
import com.dt.train.management.center.model.vo.user.TrainUserVO;
import com.dt.train.management.center.model.vo.user.TrainUserVOWithSummary;
import com.dt.train.management.center.service.project.TrainCourseService;
import com.dt.train.management.center.service.project.TrainCourseUserRelationService;

import lombok.extern.slf4j.Slf4j;

@Component
@Slf4j
public class ScheduleManager {

    private final TrainCourseService trainCourseService;

    private final TrainCourseUserRelationService trainCourseUserRelationService;

    public ScheduleManager(TrainCourseService trainCourseService, TrainCourseUserRelationService trainCourseUserRelationService) {
        this.trainCourseService = trainCourseService;
        this.trainCourseUserRelationService = trainCourseUserRelationService;
    }

    /**
     * 获取用户排班总结
     * @param users
     * @param startDate
     * @param endDate
     * @return
     */
    public List<TrainUserVOWithSummary> getUserScheduleSummary(List<TrainUserVO> users, TrainUserWithQueryDTO query) {
        List<Long> userIds = users.stream().map(TrainUserVO::getUserId).collect(Collectors.toList());
        CourseUserRelationQueryDTO relationQuery = new CourseUserRelationQueryDTO();
        relationQuery.setUserIds(userIds);
        HalfDayTime startTime = new HalfDayTime(query.getStartTime(), query.getStartHalfDay());
        HalfDayTime endTime = new HalfDayTime(query.getEndTime(), query.getEndHalfDay());
        relationQuery.setStartDate(startTime.toFullDate());
        relationQuery.setEndDate(endTime.toFullDate());
        List<TrainCourseUserRelationVOWithCourse> relationUsers = trainCourseUserRelationService.listWithCourseByQuery(relationQuery);
        Map<Long, List<TrainCourseUserRelationVOWithCourse>> relationUsersMap = relationUsers.stream().collect(Collectors.groupingBy(TrainCourseUserRelationVOWithCourse::getUserId));
        List<TrainUserVOWithSummary> userWithSummaryList = users.stream().map(user -> {
            List<TrainCourseUserRelationVOWithCourse> relationList = relationUsersMap.getOrDefault(user.getUserId(), new ArrayList<>());
            TrainUserVOWithSummary userWithSummary = getTotalDates(user, relationList, startTime, endTime);
            return userWithSummary;
        }).collect(Collectors.toList());
        return userWithSummaryList;
    }

    private TrainUserVOWithSummary getTotalDates(TrainUserVO user,
            List<TrainCourseUserRelationVOWithCourse> relationList, HalfDayTime startTime, HalfDayTime endTime) {
        TrainUserVOWithSummary userWithSummary = new TrainUserVOWithSummary();
        BeanUtils.copyProperties(user, userWithSummary);

        userWithSummary.setRunningCourse(relationList.stream()
                .filter(relation -> relation.getCourseStatus() == 1)
                .map(TrainCourseUserRelationVOWithCourse::getCourseId)
                .distinct()
                .count());
        userWithSummary.setWaitCourse(relationList.stream()
                .filter(relation -> relation.getCourseStatus() == 0)
                .map(TrainCourseUserRelationVOWithCourse::getCourseId)
                .distinct()
                .count());
        userWithSummary.setCompletedCourse(relationList.stream()
                .filter(relation -> relation.getCourseStatus() == 2)
                .map(TrainCourseUserRelationVOWithCourse::getCourseId)
                .distinct()
                .count());
        userWithSummary.setTotalCourse(userWithSummary.getRunningCourse() + userWithSummary.getWaitCourse() + userWithSummary.getCompletedCourse());
        List<HalfDayTime> totalDates = relationList.stream()
                .map(this::vo2UnitVO)
                .flatMap(List::stream)
                .map(CalendarUserUnitVO::getAtDate)
                .filter(date -> date.isAfterEqual(startTime) && date.isBeforeEqual(endTime))
                .distinct()
                .collect(Collectors.toList());
        userWithSummary.setAllocatedDay(totalDates.size() / 2f);
        HalfDayTime now = HalfDayTime.now();
        List<HalfDayTime> allocatedDates = totalDates.stream()
                .filter(date -> date.isBefore(now))
                .collect(Collectors.toList());
        userWithSummary.setPastDay(allocatedDates.size() / 2f);
        userWithSummary.setFutureDay(userWithSummary.getAllocatedDay() - userWithSummary.getPastDay());
        return userWithSummary;
    }

    /**
     * 将DTO转换为CalendarUserUnitVO,按照日期拆开
     * @param dto
     * @return
     */
    private List<CalendarUserUnitVO> vo2UnitVO(TrainCourseUserRelationVOWithCourse vo) {
        HalfDayTime start = vo.getStartDate();
        HalfDayTime end = vo.getEndDate();
        List<CalendarUserUnitVO> unitVOList = new ArrayList<>();
        while(!start.isAfter(end)) {
            CalendarUserUnitVO unitVO = new CalendarUserUnitVO();
            BeanUtils.copyProperties(vo, unitVO);
            unitVO.setAtDate(start);
            unitVOList.add(unitVO);
            start = start.next();
        }
        return unitVOList;
    }
    /**
     * 获取排班日历
     * 
     * @param startDate 开始时间
     * @param endDate 结束时间
     * @return 排班日历
     */
    public ScheduleCalendarVO getCalendar(Date startDate, Date endDate) {
        HalfDayTime start = new HalfDayTime(startDate);
        HalfDayTime end = new HalfDayTime(endDate);
        end.setHalfDay(HalfDayEnum.PM);
        // 获取开始时间到结束时间之间的所有人员排班
        List<CalendarUserUnitVO> userUnitVOList = trainCourseUserRelationService.listCalendarByTime(start.toFullDate(), end.toFullDate(), null, null);
        //获取关联班次
        List<Integer> courseIds = userUnitVOList.stream().map(CalendarUserUnitVO::getCourseId).collect(Collectors.toList());
        //获取班次
        List<TrainCourseVO> trainCourseVOS = trainCourseService.listByIds(courseIds, false);
        Map<Integer, List<TrainCourseVO>> trainCourseVOMap = trainCourseVOS.stream()
                                     .collect(Collectors.groupingBy(TrainCourseVO::getId));
        // 将userUnitVOList按日期分组后，在按照AM/PM分组，在按照courseId分组，最后将courseId对应的trainCourseVOMap的值赋值给userUnitVOList
        Map<LocalDate, Map<HalfDayEnum, Map<Integer, List<CalendarUserUnitVO>>>> userUnitVOMap = 
            userUnitVOList.stream()
                .collect(
                    Collectors.groupingBy(vo -> vo.getAtDate().getDate(),
                        Collectors.groupingBy(vo -> vo.getAtDate().getHalfDay(),
                            Collectors.groupingBy(CalendarUserUnitVO::getCourseId))));
        //将userUnitVOMap转化为ScheduleCalendarVO
        ScheduleCalendarVO scheduleCalendarVO = new ScheduleCalendarVO();
        scheduleCalendarVO.setCalendarList(userUnitVOMap.entrySet().stream()
            .map(entry -> {
                HalfDayCalendarVO halfDayCalendarVO = new HalfDayCalendarVO();
                halfDayCalendarVO.setDate(entry.getKey());
                List<TrainCourseCalendarVO> amCourseListVO = getTrainCourseCalendarVO(entry.getValue().get(HalfDayEnum.AM), trainCourseVOMap);
                List<TrainCourseCalendarVO> pmCourseListVO = getTrainCourseCalendarVO(entry.getValue().get(HalfDayEnum.PM), trainCourseVOMap);
                halfDayCalendarVO.setAmCourseList(amCourseListVO);
                halfDayCalendarVO.setPmCourseList(pmCourseListVO);
                Set<Integer> dayCourseIds = new HashSet<>();
                dayCourseIds.addAll(amCourseListVO.stream().map(TrainCourseCalendarVO::getId).collect(Collectors.toList()));
                dayCourseIds.addAll(pmCourseListVO.stream().map(TrainCourseCalendarVO::getId).collect(Collectors.toList()));
                halfDayCalendarVO.setCourseCount(dayCourseIds.size());
                return halfDayCalendarVO;
            })
            .sorted(Comparator.comparing(HalfDayCalendarVO::getDate))
            .collect(Collectors.toList()));
        return scheduleCalendarVO;
    }

    private List<TrainCourseCalendarVO> getTrainCourseCalendarVO(Map<Integer, List<CalendarUserUnitVO>> userUnitVOMap, Map<Integer, List<TrainCourseVO>> trainCourseVOMap) {
        // 过滤掉没有班次的用户
        if(userUnitVOMap == null || userUnitVOMap.isEmpty()){
            return new ArrayList<>();
        }
        return userUnitVOMap.entrySet().stream()
                .map(vo -> {
                    List<TrainCourseVO> trainCourseVOList = trainCourseVOMap.get(vo.getKey());
                    if (trainCourseVOList == null || trainCourseVOList.isEmpty()) {
                        return null;
                    }
                    TrainCourseVO trainCourseVO = trainCourseVOList.get(0);
                    TrainCourseCalendarVO trainCourseCalendarVO = new TrainCourseCalendarVO();
                    BeanUtils.copyProperties(trainCourseVO, trainCourseCalendarVO);
                    trainCourseCalendarVO.setRelationUsers(vo.getValue());
                    return trainCourseCalendarVO;
                })
                .filter(vo -> vo != null)
                .collect(Collectors.toList());
    }
}
