package com.dt.train.management.center.model.dao;

import java.util.Date;

/**
 * 
 * 表名: train_check_task
 *
 * @mbg.generated
 */
@lombok.Data
public class TrainCheckTaskDO {
    /**
     * 字段描述: 主键ID
     *
     * 字段名: train_check_task.id
     *
     * @mbg.generated
     */
    private Integer id;

    /**
     * 字段描述: 项目ID
     *
     * 字段名: train_check_task.project_id
     *
     * @mbg.generated
     */
    private Integer projectId;

    /**
     * 字段描述: 班次ID
     *
     * 字段名: train_check_task.course_id
     *
     * @mbg.generated
     */
    private Integer courseId;

    /**
     * 字段描述: 任务ID
     *
     * 字段名: train_check_task.task_id
     *
     * @mbg.generated
     */
    private Integer taskId;

    /**
     * 字段描述: 用户ID
     *
     * 字段名: train_check_task.user_id
     *
     * @mbg.generated
     */
    private Long userId;

    /**
     * 字段描述: 用户名
     *
     * 字段名: train_check_task.user_name
     *
     * @mbg.generated
     */
    private String userName;

    /**
     * 字段描述: 打卡维度，1-全天，2-上午，3-下午
     *
     * 字段名: train_check_task.check_dimension
     *
     * @mbg.generated
     */
    private Integer checkDimension;

    /**
     * 字段描述: 上班打卡状态,0-未打卡，1-正常打卡，2-迟到打卡,3-早退打卡
     *
     * 字段名: train_check_task.check_in_status
     *
     * @mbg.generated
     */
    private Integer checkInStatus;

    /**
     * 字段描述: 下班打卡状态,0-未打卡，1-正常打卡，2-迟到打卡,3-早退打卡
     *
     * 字段名: train_check_task.check_out_status
     *
     * @mbg.generated
     */
    private Integer checkOutStatus;

    /**
     * 字段描述: 现场签到状态,0-未签到，1-已签到
     *
     * 字段名: train_check_task.check_on_site_status
     *
     * @mbg.generated
     */
    private Integer checkOnSiteStatus;

    /**
     * 字段描述: 打卡状态,0-正常，1-异常
     *
     * 字段名: train_check_task.check_status
     *
     * @mbg.generated
     */
    private Integer checkStatus;

    /**
     * 字段描述: 打卡日期
     *
     * 字段名: train_check_task.check_date
     *
     * @mbg.generated
     */
    private Date checkDate;

    /**
     * 字段描述: 是否已删除 0 否 1 是
     *
     * 字段名: train_check_task.invalid
     *
     * @mbg.generated
     */
    private Integer invalid;

    /**
     * 字段描述: 创建人
     *
     * 字段名: train_check_task.created_by
     *
     * @mbg.generated
     */
    private Long createdBy;

    /**
     * 字段描述: 更新人
     *
     * 字段名: train_check_task.updated_by
     *
     * @mbg.generated
     */
    private Long updatedBy;

    /**
     * 字段描述: 创建时间
     *
     * 字段名: train_check_task.gmt_create
     *
     * @mbg.generated
     */
    private Date gmtCreate;

    /**
     * 字段描述: 修改时间
     *
     * 字段名: train_check_task.gmt_modified
     *
     * @mbg.generated
     */
    private Date gmtModified;
}