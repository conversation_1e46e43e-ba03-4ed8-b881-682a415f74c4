package com.dt.train.management.center.manager;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.dt.train.management.center.model.dto.config.SmartConfigDTO;
import com.dt.train.management.center.model.vo.config.SmartConfigVO;
import com.dt.train.management.center.service.config.SmartConfigService;

import lombok.extern.slf4j.Slf4j;

@Component
@Slf4j
public class SmartConfigManager {

    private final SmartConfigService smartConfigService;

    public SmartConfigManager(SmartConfigService smartConfigService) {
        this.smartConfigService = smartConfigService;
    }

    /**
     * get single config
     *
     * @param key key
     * @return {@link T }
     */
    public <T> T getSingleConfigValue(String key, List<String> secondKey, TypeReference<T> reference){
        List<SmartConfigDTO> configs = smartConfigService.getConfig(key);
        if(CollectionUtils.isEmpty(configs)){
            return null;
        }
        String value = configs.stream()
                .filter(c -> secondKey == null || secondKey.contains(c.getSecondKey()))
                .findFirst()
                .map(SmartConfigDTO::getConfigValue)
                .orElse(null);
        return JSONObject.parseObject(value, reference);
    }

    /**
     * get single config
     *
     * @param key key
     * @return {@link T }
     */
    public SmartConfigVO getSingleConfig(String key, List<String> secondKey){
        List<SmartConfigDTO> configs = smartConfigService.getConfig(key);
        if(CollectionUtils.isEmpty(configs)){
            return null;
        }
        SmartConfigDTO config = configs.stream()
                .filter(c -> secondKey == null || secondKey.contains(c.getSecondKey()))
                .findFirst()
                .orElse(null);
        return dto2vo(config);
    }

    private SmartConfigVO dto2vo(SmartConfigDTO config) {
        if(config == null){
            return null;
        }
        SmartConfigVO smartConfigVO = new SmartConfigVO();
        BeanUtils.copyProperties(config, smartConfigVO);
        smartConfigVO.setConfigValue(JSONObject.parseObject(config.getConfigValue()));
        return smartConfigVO;
    }

    /**
     * get chunk config
     *
     * @param key key
     * @return {@link T }
     */
    public <T> List<T> getChunkConfig(String key, List<String> secondKey, Class<T> clazz){
        List<SmartConfigDTO> configs = smartConfigService.getConfig(key);
        if(CollectionUtils.isEmpty(configs)){
            return new ArrayList<T>();
        }
        return configs.stream()
                .filter(c -> secondKey == null || secondKey.contains(c.getSecondKey()))
                .map(SmartConfigDTO::getConfigValue)
                .map(v -> JSONArray.parseArray(v, clazz))
                .reduce((a, b) -> {
                    a.addAll(b);
                    return a;
                }).orElse(new ArrayList<>());
    }

    /**
     * get list config
     *
     * @param key key
     * @return {@link T }
     */
    public <T> List<T> getConfigs(String key, List<String> secondKey, TypeReference<T> reference){
        List<SmartConfigDTO> configs = smartConfigService.getConfig(key);
        if(CollectionUtils.isEmpty(configs)){
            return new ArrayList<>();
        }
        return configs.stream()
                .filter(c -> secondKey == null || secondKey.contains(c.getSecondKey()))
                .map(SmartConfigDTO::getConfigValue)
                .map(v -> JSONObject.parseObject(v, reference))
                .collect(Collectors.toList());
    }

    /**
     * save or update by key
     *
     * @param smartConfigVO smartConfigVO
     * @return {@link SmartConfigVO }
     */
    public SmartConfigVO saveOrUpdateByKey(SmartConfigVO smartConfigVO) {
        SmartConfigDTO smartConfigDTO = new SmartConfigDTO();
        BeanUtils.copyProperties(smartConfigVO, smartConfigDTO);
        smartConfigDTO.setConfigValue(smartConfigVO.getConfigValue().toJSONString());
        smartConfigService.saveOrUpdateByKey(smartConfigDTO);
        return smartConfigVO;
    }
}
