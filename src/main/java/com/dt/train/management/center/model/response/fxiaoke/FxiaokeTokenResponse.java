package com.dt.train.management.center.model.response.fxiaoke;

import lombok.Data;

/**
 * 纷享销客token返回
 * 
 * <AUTHOR>
 * @since 2025-05-16 14:58:58
 */
@Data
public class FxiaokeTokenResponse {

    /**
     * 错误码
     */
    private Integer errorCode;

    /**
     * 错误信息
     */
    private String errorMessage;
    
    /**
     * 企业access_token
     */
    private String corpAccessToken;

    /**
     * 企业corpId
     */
    private String corpId;

    /**
     * 企业应用访问公司合法性凭证的过期时间，单位为秒，取值在0~7200之间，在过期时间在0-6600之间请求该接口会返回相同的corpAccessToken，在6600-7200之间请求该接口会返回新的token，如果要续期token，则需要在该时刻进行请求。
     */
    private Long expiresIn;
}