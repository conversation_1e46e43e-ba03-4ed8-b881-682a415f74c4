package com.dt.train.management.center.feign.param;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.util.Date;
import java.util.Set;

/**
 * @author: LiDeGao
 * @time: 2018-09-19  9:53
 */
@Getter
@Setter
@NoArgsConstructor
public class UserSearchParam implements Serializable {

    /**
     * 字段描述: 用户id
     */
    private Long id;

    private Set<Long> ids;

    /**
     * 字段描述: 用户编码
     */
    private Long userCode;

    private Set<Long> userCodes;

    /**
     * 字段描述: 真实姓名
     */
    private String realName;

    /**
     * 字段描述: 昵称
     */
    private String nickName;

    /**
     * 字段描述: 用户性别(0:女 1:男)
     */
    private Integer gender;

    /**
     * 字段描述: 角色
     */
    private Integer roleCode;

    /**
     * 手机号
     */
    private String mobile;

    /**
     * 字段描述: 邮箱
     */
    private String email;

    /**
     * 字段描述: 身份证号
     */
    private String idNo;

    /**
     * 字段描述: 头像
     */
    private String icon;

    /**
     * 字段描述: 省份id
     */
    private Long provId;

    /**
     * 字段描述: 城市id
     */
    private Long cityId;

    /**
     * 字段描述: 区域id
     */
    private Long areaId;

    /**
     * 字段描述: 地址
     */
    private String address;

    /**
     * 字段描述: 生日
     */
    private Date birth;

    /**
     * 字段描述: 个性签名
     */
    private String selfSignature;

    /**
     * 字段描述: 备注
     */
    private String comments;

    /**
     * 字段描述: 用户标签
     */
    private String userTag;

    /**
     * 注册时间
     */
    private Date gmtCreate;

    private String loginName;

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.JSON_STYLE)
                .append("id", id)
                .append("ids", ids)
                .append("userCode", userCode)
                .append("userCodes", userCodes)
                .append("realName", realName)
                .append("nickName", nickName)
                .append("gender", gender)
                .append("roleCode", roleCode)
                .append("email", email)
                .append("idNo",idNo)
                .append("icon", icon)
                .append("provId", provId)
                .append("cityId", cityId)
                .append("areaId", areaId)
                .append("address", address)
                .append("birth", birth)
                .append("selfSignature", selfSignature)
                .append("comments",comments)
                .append("gmtCreate", gmtCreate)
                .append("loginName", loginName)
                .toString();
    }
}
