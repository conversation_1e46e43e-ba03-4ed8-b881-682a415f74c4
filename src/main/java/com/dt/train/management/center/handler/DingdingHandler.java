package com.dt.train.management.center.handler;

import com.alibaba.fastjson.JSON;
import com.dingtalk.api.DefaultDingTalkClient;
import com.dingtalk.api.DingTalkClient;
import com.dingtalk.api.request.*;
import com.dingtalk.api.response.*;
import com.dt.train.management.center.config.BusinessConfig;
import com.dt.train.management.center.model.dept.DingdingDeptVO;
import com.dt.train.management.center.model.user.DingdingUserVO;
import com.dt.train.management.center.utils.RedisUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.taobao.api.ApiException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.bouncycastle.pqc.crypto.xmss.XMSSReducedSignature;
import org.springframework.stereotype.Component;
import com.dt.train.management.center.constant.CacheConstant;
import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Component
@Slf4j
public class DingdingHandler {


    @Resource
    private BusinessConfig businessConfig;
    @Resource
    private RedisUtil redisUtil ;

    public String getToken() {
        // 先从缓存中获取token
        String cachedToken = redisUtil.get(CacheConstant.DINGDING_TOKEN_KEY);
        if (StringUtils.isNotBlank(cachedToken)) {
            log.info("从缓存获取钉钉token成功");
            return cachedToken;
        }

        try {
            DingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/gettoken");
            OapiGettokenRequest req = new OapiGettokenRequest();
            req.setAppkey(businessConfig.getDingTalkAppKey());
            req.setAppsecret(businessConfig.getDingTalkAppSecret());
            req.setHttpMethod("GET");

            OapiGettokenResponse rsp = client.execute(req);
            if (rsp != null && rsp.isSuccess()) {
                String accessToken = rsp.getAccessToken();
                // 钉钉token有效期默认7200秒（2小时），我们缓存30分钟（1800秒）
                redisUtil.set(CacheConstant.DINGDING_TOKEN_KEY, accessToken, 1800);
                return accessToken;
            } else {
                log.error("获取钉钉token失败，返回信息：{}", rsp);
            }
        } catch (ApiException e) {
            log.error("获取钉钉token异常", e);
        }

        return "";
    }

    /**
    * 根据手机号获取用户id列表
    * */
    public List<DingdingUserVO> getUseridListByMobiles(List<String> mobileList, String token) {
        List<DingdingUserVO> userInfoList = Lists.newArrayList();
        for (String mobile : mobileList) {

            try {
                DingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/topapi/v2/user/getbymobile");
                OapiV2UserGetbymobileRequest req = new OapiV2UserGetbymobileRequest();
                req.setMobile(mobile);
                OapiV2UserGetbymobileResponse rsp = client.execute(req, token);
                if (rsp.getResult() != null) {
                    DingdingUserVO dingdingUserVO = new DingdingUserVO();
                    dingdingUserVO.setMobile(mobile);
                    dingdingUserVO.setUserId(rsp.getResult().getUserid());
                    userInfoList.add(dingdingUserVO);
                }
            } catch (Exception e) {
                log.error("钉钉根据手机号获取用户id失败, mobile:{}", mobile, e);
            }
        }
        return userInfoList;
    }

    /**
     * 根据用户id获取用户详情
     * */
    public List<DingdingUserVO> getUserInfoByUserId(List<String> userIdList, String token) {
        List<DingdingUserVO> dingdingUserVOList = Lists.newArrayList();
        if(CollectionUtils.isEmpty(userIdList)){
            log.info("用户id列表为空，不进行获取用户详情");
            return dingdingUserVOList;
        }
        for (String userId : userIdList) {
            try {
                DingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/topapi/v2/user/get");
                OapiV2UserGetRequest req = new OapiV2UserGetRequest();
                req.setUserid(userId);
                req.setLanguage("zh_CN");
                OapiV2UserGetResponse rsp = client.execute(req, token);
                if (rsp.getResult() != null) {
                    OapiV2UserGetResponse.UserGetResponse result = rsp.getResult();
                    DingdingUserVO dingdingUserVO = new DingdingUserVO();
                    dingdingUserVO.setUserId(result.getUserid());
                    dingdingUserVO.setName(result.getName());
                    dingdingUserVO.setMobile(result.getMobile());
                    dingdingUserVO.setEmail(result.getEmail());
                    dingdingUserVO.setOrgEmail(result.getOrgEmail());
                    dingdingUserVO.setDeptIds(result.getDeptIdList());
                    dingdingUserVO.setWorkPlace(result.getWorkPlace());
                    dingdingUserVO.setUserPosition(result.getTitle());
                    dingdingUserVOList.add(dingdingUserVO);
                }
            } catch (Exception e) {
                log.error("钉钉根据userId获取用户信息失败, userId:{}", userId, e);
            }
        }
        return dingdingUserVOList;

    }
    /**
     * 根据手机号获取用户详情
     * */
    public List<DingdingUserVO> getUserInfoByMobiles(List<String> mobileList) {
        String token = getToken();
        List<DingdingUserVO> userVOList = getUseridListByMobiles(mobileList, token);
        if(CollectionUtils.isEmpty(userVOList)){
            log.info("钉钉用户信息列表为空，不进行获取用户详情");
            return userVOList;
        }
        List<String> userIdList = userVOList.stream().map(DingdingUserVO::getUserId).collect(Collectors.toList());
        Map<String, String> idToMobileMap = userVOList.stream().collect(Collectors.toMap(DingdingUserVO::getUserId, DingdingUserVO::getMobile));
        List<DingdingUserVO> dingdingUserVOList = getUserInfoByUserId(userIdList, token);
        if(CollectionUtils.isEmpty(dingdingUserVOList)){
            log.info("根据id获取用户信息为空");
            return dingdingUserVOList;
        }
        for (DingdingUserVO dingdingUserVO : dingdingUserVOList) {
            dingdingUserVO.setMobile(idToMobileMap.get(dingdingUserVO.getUserId()));
        }
        return dingdingUserVOList;
    }

    /**
     * 获取某个部门下用户姓名和id
     * 只支持获取当前部门内员工信息，子部门员工信息获取不到
     */
    public List<DingdingUserVO> getUserInfoByDeptId(Long deptId, String token) {
        List<DingdingUserVO> dingdingUserVOList = Lists.newArrayList();
        Long cursor = 0L;
        final Long pageSize = 100L;

        try {
            DingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/topapi/user/listsimple");
            while (true) {
                OapiUserListsimpleRequest req = new OapiUserListsimpleRequest();
                req.setDeptId(deptId);
                req.setCursor(cursor);
                req.setSize(pageSize);
                req.setLanguage("zh_CN");
                OapiUserListsimpleResponse rsp = client.execute(req, token);
                if (rsp.getResult() == null || !rsp.isSuccess()) {
                    log.info("未获取到部门用户信息, deptId:{}, cursor:{}, errorMsg:{}", deptId, cursor, rsp.getErrmsg());
                    break;
                }

                // 添加当前页数据到结果列表
                if (CollectionUtils.isNotEmpty(rsp.getResult().getList())) {
                    List<DingdingUserVO> pageUsers = rsp.getResult().getList().stream()
                            .map(user -> {
                                DingdingUserVO vo = new DingdingUserVO();
                                vo.setUserId(user.getUserid());
                                vo.setName(user.getName());
                                return vo;
                            })
                            .collect(Collectors.toList());
                    dingdingUserVOList.addAll(pageUsers);
                }

                // 检查是否还有更多数据
                if (rsp.getResult().getHasMore() != null && rsp.getResult().getHasMore()) {
                    cursor = rsp.getResult().getNextCursor();
                } else {
                    break;
                }

                // 防止无限循环的保险措施
                if (dingdingUserVOList.size() > 2000) {
                    log.error("获取部门用户数据异常，已获取超过10000条数据，deptId:{}", deptId);
                    break;
                }
            }
        } catch (ApiException e) {
            log.error("获取钉钉部门用户信息异常, deptId:{}", deptId, e);
        }
        return dingdingUserVOList;
    }

    /**
     * 获取整个公司全部部门信息（包括所有子部门）
     */
    public List<DingdingDeptVO> getAllDeptInfo(String token) {
        // 1. 先从缓存中获取
        String allDeptInfoStr = redisUtil.get(CacheConstant.DINGDING_ACCESS_ALL_DEPT_KEY);
        if (StringUtils.isNotBlank(allDeptInfoStr)) {
            try {
                List<DingdingDeptVO> cachedDepts = JSON.parseArray(allDeptInfoStr, DingdingDeptVO.class);
                if (CollectionUtils.isNotEmpty(cachedDepts)) {
                    log.info("从缓存获取钉钉全部部门信息成功，部门数量：{}", cachedDepts.size());
                    return cachedDepts;
                }
            } catch (Exception e) {
                log.error("解析缓存部门信息异常,从钉钉API获取部门信息", e);
                redisUtil.del(CacheConstant.DINGDING_ACCESS_ALL_DEPT_KEY);
            }
        }

        // 2. 缓存不存在或无效，从钉钉API获取
        List<DingdingDeptVO> allDepts = getSubDeptsRecursively(1L, token, "");
        log.info("从钉钉API获取部门信息成功，部门数量：{}",allDepts.size());

        // 3. 存入缓存（设置30分钟有效期）
        if (CollectionUtils.isNotEmpty(allDepts)) {
            redisUtil.set(CacheConstant.DINGDING_ACCESS_ALL_DEPT_KEY, JSON.toJSONString(allDepts), 1800);
            log.info("钉钉部门信息已缓存，部门数量：{}", allDepts.size());
        }
        return allDepts;
    }



    private List<DingdingDeptVO> getSubDeptsRecursively(Long deptId, String token, String parentPath) {
        List<DingdingDeptVO> allDepts = new ArrayList<>();
        try {
            List<DingdingDeptVO> subDepts = getDirectSubDepts(deptId, token);
            for (DingdingDeptVO dept : subDepts) {
                // 生成当前部门路径（父路径为空时为一级部门，直接使用自身名称）
                String currentPath = parentPath.isEmpty() ? dept.getName() : parentPath + "-" + dept.getName();
                dept.setName(currentPath);
                allDepts.add(dept);

                // 递归处理子部门（传递当前路径）
                allDepts.addAll(getSubDeptsRecursively(dept.getId(), token, currentPath));
            }
        } catch (ApiException e) {
            log.error("递归获取部门失败, deptId:{}", deptId, e);
        }

        return allDepts;
    }


    private List<DingdingDeptVO> getDirectSubDepts(Long deptId, String token) throws ApiException {
        OapiV2DepartmentListsubResponse rsp = null;
        try {
            DingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/topapi/v2/department/listsub");
            OapiV2DepartmentListsubRequest req = new OapiV2DepartmentListsubRequest();
            req.setDeptId(deptId);
            req.setLanguage("zh_CN");
            rsp = client.execute(req, token);

            if (!rsp.isSuccess() || CollectionUtils.isEmpty(rsp.getResult())) {
                log.info("未获取到子部门信息, deptId:{}, errorMsg:{}", deptId, rsp.getErrmsg());
                return Lists.newArrayList();
            }
        } catch (ApiException e) {
            log.error("根据部门id获取子部门信息失败，deptId:{}", deptId, e);
            return Lists.newArrayList();
        }

        // 封装当前子部门信息到结果列表
        List<DingdingDeptVO> result = rsp.getResult().stream()
                    .map(dept -> {
                        DingdingDeptVO vo = new DingdingDeptVO();
                        vo.setId(dept.getDeptId());
                        vo.setName(dept.getName());
                        vo.setParentId(deptId);
                        return vo;
                    }).collect(Collectors.toList());

        return result;
    }


}
