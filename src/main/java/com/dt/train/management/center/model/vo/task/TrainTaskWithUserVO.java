package com.dt.train.management.center.model.vo.task;

import java.util.List;

import com.dt.train.management.center.model.dto.project.TrainCourseUserRelationDTO;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
public class TrainTaskWithUserVO extends TrainTaskVO {

    @ApiModelProperty(value = "支援人列表")
    private List<TrainCourseUserRelationDTO> supportUsers;

    @ApiModelProperty(value = "执行人列表")
    private List<TrainCourseUserRelationDTO> executeUsers;

}
