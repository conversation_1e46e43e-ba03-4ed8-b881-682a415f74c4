package com.dt.train.management.center.model.dto.task;

import java.util.Date;
import java.util.List;

import org.springframework.format.annotation.DateTimeFormat;

import com.dt.framework.business.util.page.PaginationParams;

import io.swagger.annotations.ApiModelProperty;

/**
 * 
 * 表名: train_check_task
 *
 * @mbg.generated
 */
@lombok.Data
public class TrainCheckTaskQueryDTO implements PaginationParams {

    @ApiModelProperty(value = "部门ID")
    private Integer deptId;

    @ApiModelProperty(value = "用户ID", hidden = true)
    private List<Long> userIds;


    /**
     * 字段描述: 项目ID
     *
     * 字段名: train_check_task.project_id
     *
     * @mbg.generated
     */
    private Integer projectId;

    @ApiModelProperty(value = "项目名称")
    private String projectName;

    @ApiModelProperty(value = "班次名称")
    private String courseName;

    /**
     * 字段描述: 班次ID
     *
     * 字段名: train_check_task.course_id
     *
     * @mbg.generated
     */
    private Integer courseId;

    private List<Integer> courseIds;

    /**
     * 字段描述: 任务ID
     *
     * 字段名: train_check_task.task_id
     *
     * @mbg.generated
     */
    private Integer taskId;

    /**
     * 字段描述: 用户ID
     *
     * 字段名: train_check_task.user_id
     *
     * @mbg.generated
     */
    private Long userId;

    @ApiModelProperty(value = "用户姓名")
    private String userName;

    /**
     * 字段描述: 上班打卡状态,0-未打卡，1-正常打卡，2-迟到打卡,3-早退打卡
     *
     * 字段名: train_check_task.check_in_status
     *
     * @mbg.generated
     */
    private Integer checkInStatus;

    /**
     * 字段描述: 下班打卡状态,0-未打卡，1-正常打卡，2-迟到打卡,3-早退打卡
     *
     * 字段名: train_check_task.check_out_status
     *
     * @mbg.generated
     */
    private Integer checkOutStatus;

    /**
     * 字段描述: 现场签到状态,0-未签到，1-已签到
     *
     * 字段名: train_check_task.check_on_site_status
     *
     * @mbg.generated
     */
    private Integer checkOnSiteStatus;

    /**
     * 字段描述: 打卡状态,0-正常，1-异常
     *
     * 字段名: train_check_task.check_status
     *
     * @mbg.generated
     */
    private Integer checkStatus;

    /**
     * 字段描述: 打卡日期
     *
     * 字段名: train_check_task.check_date
     *
     * @mbg.generated
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date startCheckDate;

    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date endCheckDate;

    @ApiModelProperty(value = "页码")
    private Integer pageIndex;

    @ApiModelProperty(value = "每页条数")
    private Integer pageSize;

}