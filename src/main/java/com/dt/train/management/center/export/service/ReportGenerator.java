package com.dt.train.management.center.export.service;

import java.util.List;

import com.dt.train.management.center.export.enums.ReportFormat;
import com.dt.train.management.center.export.enums.ReportType;
import com.dt.train.management.center.export.exception.ReportGenerationException;

/**
 * 报表生成器接口。
 * 负责将数据转换为指定格式的报表内容（字节数组）。
 */
public interface ReportGenerator {
    /**
     * 判断当前报表生成器是否支持指定报表格式。
     * @param format 报表格式
     * @return true 如果支持，否则 false
     */
    boolean supports(ReportFormat format);

    /**
     * 根据 Excel 导出 DTO 列表和报表参数生成报表内容。
     * @param data 从数据获取器获取的 Excel 导出 DTO 列表 (List<?>)
     * @param parameters 报表生成参数（如报表标题、列配置等）
     * @return 生成的报表内容的url
     * @throws ReportGenerationException 如果生成过程中发生错误
     */
    String generateReport(List<?> data, ReportType reportType) throws ReportGenerationException;
}