package com.dt.train.management.center.model.vo.project;

import java.time.LocalDate;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonFormat;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class HalfDayCalendarVO {

    /**
     * 上午班次列表
     */
    @ApiModelProperty(value = "上午班次列表")
    private List<TrainCourseCalendarVO> amCourseList;

    /**
     * 下午班次列表
     */
    @ApiModelProperty(value = "下午班次列表")
    private List<TrainCourseCalendarVO> pmCourseList;

    /**
     * 日期
     */
    @ApiModelProperty(value = "日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate date;

    /**
     * 班次数量
     */
    @ApiModelProperty(value = "班次数量")
    private Integer courseCount;
}
