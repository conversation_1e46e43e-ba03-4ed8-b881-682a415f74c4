package com.dt.train.management.center.mapper;

import com.dt.train.management.center.model.dao.SysRoleDO;
import java.util.List;

public interface SysRoleDOMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(SysRoleDO row);

    int insertSelective(SysRoleDO row);

    SysRoleDO selectByPrimaryKey(Integer id);

    List<SysRoleDO> selectAll();

    int updateByPrimaryKeySelective(SysRoleDO row);

    int updateByPrimaryKey(SysRoleDO row);

    SysRoleDO selectByRoleName(String roleName);
}
