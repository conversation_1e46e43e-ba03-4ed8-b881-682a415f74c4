package com.dt.train.management.center.config;

import java.util.concurrent.ThreadPoolExecutor;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

@Configuration
@EnableAsync
public class AsyncConfig {

    @Bean("reportTaskExecutor")
    public ThreadPoolTaskExecutor getAsyncExecutor() {

        ThreadPoolTaskExecutor threadPoolTaskExecutor = new ThreadPoolTaskExecutor();
        threadPoolTaskExecutor.setThreadNamePrefix("async-reportTask-");
        //线程池核心线程数
        threadPoolTaskExecutor.setCorePoolSize(8);
        //线程池最大线程数 等待队列满之后开启
        threadPoolTaskExecutor.setMaxPoolSize(16);
        //队列数，默认 Integer.MAX 大于0为LinkedBlockingQueue 小于等于0为 SynchronousQueue
        threadPoolTaskExecutor.setQueueCapacity(10000);
        //达到最大线程数后续任务丢弃
        threadPoolTaskExecutor.setRejectedExecutionHandler(new ThreadPoolExecutor.AbortPolicy());
        //开启核心线程数支持空闲释放
        threadPoolTaskExecutor.setAllowCoreThreadTimeOut(false);
        //设置等待任务完成后再关闭
        threadPoolTaskExecutor.setWaitForTasksToCompleteOnShutdown(true);
        return threadPoolTaskExecutor;
    }
}