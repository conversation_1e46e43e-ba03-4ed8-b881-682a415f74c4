package com.dt.train.management.center.web.dev;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.exception.ExcelAnalysisException;
import com.alibaba.excel.read.builder.ExcelReaderBuilder;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.dt.framework.business.constant.BusinessCode;
import com.dt.framework.business.constant.DtHeaders;
import com.dt.framework.business.dto.web.common.WebResult;
import com.dt.framework.core.exception.BusinessException;
import com.dt.train.management.center.enums.ManagementExceptionEnum;
import com.dt.train.management.center.handler.DingdingHandler;
import com.dt.train.management.center.manager.FXiaoKeManager;
import com.dt.train.management.center.mapper.TrainDeptDOMapper;
import com.dt.train.management.center.model.dao.TrainDeptDO;
import com.dt.train.management.center.model.dept.DingdingDeptVO;
import com.dt.train.management.center.model.dto.tools.RegionUploadDataDTO;
import com.dt.train.management.center.model.dto.tools.UserUploadDataDTO;
import com.dt.train.management.center.model.dto.user.UserAndDepartmentMaintDTO;
import com.dt.train.management.center.model.exception.ManagementBusinessException;
import com.dt.train.management.center.model.response.fxiaoke.FxiaokeUserQueryResponse;
import com.dt.train.management.center.model.user.DingdingUserVO;
import com.dt.train.management.center.model.vo.tools.BatchImportRegionVO;
import com.dt.train.management.center.service.excel.ExcelActionService;
import com.dt.train.management.center.service.excel.RegionUploadDataListener;
import com.dt.train.management.center.service.excel.UserUploadDataListener;
import com.dt.train.management.center.service.user.UserAndDepartmentMaintenanceService;
import com.google.common.collect.Lists;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.InputStream;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@RestController
@RequestMapping("/userAndDepartment/maint")
@Api(tags = "用户、部门基本信息初始化及维护工具")
public class InitializeUserAndDepartmentToolsController {
    @Resource
    private UserAndDepartmentMaintenanceService userAndDepartmentMaintenanceService;
    @Resource
    private DingdingHandler dingdingHandler;
    @Resource(name = "toolTextReadExecutor")
    private ThreadPoolTaskExecutor taskExecutor;
    @Resource
    private ExcelActionService excelActionService;
    @Resource
    private TrainDeptDOMapper trainDeptDOMapper;
    @Resource
    private FXiaoKeManager fXiaoKeManager;

    @ApiOperation("批量维护用户及所属部门(导入)")
    @PostMapping("/batchSaveOrUpdate")
    public WebResult<String> batchUpdate(@RequestParam("file") MultipartFile file,@RequestParam(value = "operationType") String operationType) {
        if (file == null || file.isEmpty() || StringUtils.isEmpty(operationType)) {
            throw new ManagementBusinessException(ManagementExceptionEnum.BAD_PARAMETER, "必传非空!");
        }
        log.info("批量导入用户信息开始");

        // 创建线程安全的集合
        List<UserUploadDataDTO> uploadDataDTOList;
        InputStream inputStream = null;
        try {
            inputStream = file.getInputStream();
            log.info("开始解析数据并校验字段, inputStream:{}", inputStream);
            ExcelReaderBuilder excelReaderBuilder = EasyExcel.read(inputStream, UserUploadDataDTO.class, new UserUploadDataListener());
            uploadDataDTOList = new ArrayList<>(excelReaderBuilder.sheet().doReadSync());

        } catch (Exception e) {
            log.info("文件解析失败", e);
            throw new ManagementBusinessException(ManagementExceptionEnum.BAD_PARAMETER, "文件解析失败!");
        }
        if (uploadDataDTOList.isEmpty()) {
            throw new ManagementBusinessException(ManagementExceptionEnum.BAD_PARAMETER, "解析上传文件后数据为空!");
        }
        // 从钉钉获取用户信息（获取不到可能是离职也可能是手机号错误，所以要拦截）
        List<String> mobileList = uploadDataDTOList.stream().map(UserUploadDataDTO::getMobile).collect(Collectors.toList());
        List<DingdingUserVO> dingdingUserVOS = dingdingHandler.getUserInfoByMobiles(mobileList);
        if (CollectionUtils.isEmpty(dingdingUserVOS)) {
            throw new BusinessException(BusinessCode.BAD_PARAMETER_CODE, "根据手机号查询钉钉系统用户信息为空，请检查手机号是否正确或该员工是否离职");
        }
        // 根据部门名称查询部门信息
        List<String> departmentNameList = uploadDataDTOList.stream().map(UserUploadDataDTO::getDeptName).distinct().collect(Collectors.toList());
        Map<String, String> mobileRealNameMap = uploadDataDTOList.stream().collect(Collectors.toMap(UserUploadDataDTO::getMobile, UserUploadDataDTO::getName));
        List<TrainDeptDO> trainDeptDOs = trainDeptDOMapper.selectByDeptNames(departmentNameList);
        if (CollectionUtils.isEmpty(trainDeptDOs)) {
            throw new BusinessException(BusinessCode.BAD_PARAMETER_CODE, "所有部门名称都不存在，请检查部门名称是否正确");
        }
        // 校验排班系统部门名称是否存在
        checkDeptIsExisit(trainDeptDOs, departmentNameList);
        // 用户姓名和手机号是否匹配
        checkRealNameAndMobileIsMatch(dingdingUserVOS, mobileRealNameMap);
        // 获取分享销客用户信息
        FxiaokeUserQueryResponse allUser = fXiaoKeManager.getAllUser();
        List<FxiaokeUserQueryResponse.Employee> empList = allUser.getEmployees();
        taskExecutor.execute(() -> {
            try {
                log.info("开始处理数据：总数{}", uploadDataDTOList.size());
                userAndDepartmentMaintenanceService.batchSaveOrUpdate(uploadDataDTOList,operationType, dingdingUserVOS, trainDeptDOs,mobileList,empList);
                log.info("批量插入数据成功");
            } catch (Exception e) {
                log.info("异步预约单批量上传未知异常", e);
                throw new ManagementBusinessException(ManagementExceptionEnum.BAD_PARAMETER, "异步预约单批量上传未知异常!");
            }
        });
        return WebResult.successData("上传成功, 请稍后查看结果");
    }

    private static void checkDeptIsExisit(List<TrainDeptDO> trainDeptDOs, List<String> departmentNameList) {
        if (trainDeptDOs.size() < departmentNameList.size()) {
            // 获取存在的部门名称集合
            Set<String> existDeptNames = trainDeptDOs.stream()
                    .map(TrainDeptDO::getDeptName)
                    .collect(Collectors.toSet());
            // 找出不存在的部门名称
            List<String> notExistDeptNames = departmentNameList.stream()
                    .filter(name -> !existDeptNames.contains(name))
                    .collect(Collectors.toList());
            throw new BusinessException(BusinessCode.BAD_PARAMETER_CODE, String.format("以下部门名称不存在：%s，请检查", notExistDeptNames));
        }
    }
    private static void checkRealNameAndMobileIsMatch(List<DingdingUserVO> dingdingUserVOS, Map<String, String> mobileRealNameMap) {
        List<String> mismatchInfos = new ArrayList<>();
        for (DingdingUserVO dingdingUserVO : dingdingUserVOS) {
            String expectedName = mobileRealNameMap.get(dingdingUserVO.getMobile());
            String dingName = dingdingUserVO.getName();

            if (expectedName == null) {
                // 手机号在映射表中不存在
                mismatchInfos.add(String.format("手机号[%s]对应的姓名不存在(钉钉名称:%s)",
                        dingdingUserVO.getMobile(),
                        dingName != null ? dingName : "无"));
            } else {
                // 核心修改：钉钉姓名存在且包含传入姓名时视为匹配
                boolean isMatch = dingName != null &&
                        dingName.toLowerCase().contains(expectedName.toLowerCase());

                if (!isMatch) {
                    mismatchInfos.add(String.format("手机号[%s]对应的姓名不匹配(传入名称:%s,钉钉名称:%s)",
                            dingdingUserVO.getMobile(),
                            expectedName,
                            dingName != null ? dingName : "无"));
                }
            }
        }

        if (!mismatchInfos.isEmpty()) {
            throw new BusinessException(BusinessCode.BAD_PARAMETER_CODE,
                    "以下用户的手机号和姓名不匹配:\n" + String.join("\n", mismatchInfos));
        }
    }

    @ApiOperation("测试通过手机号查询钉钉用户信息")
    @GetMapping("/getDingdingUserInfoByMobile")
    public WebResult<List<DingdingUserVO>> getAllDepts(@RequestParam(value = "mobileList", required = false) String mobile) {
        List<String> mobileList = Lists.newArrayList(mobile);
        List<DingdingUserVO> userInfoByMobiles = dingdingHandler.getUserInfoByMobiles(mobileList);
        return WebResult.successData(userInfoByMobiles);
    }



    @ApiOperation("批量导入CRM省市区信息同步到字典表")
    @PostMapping("/batchImportRegion")
    public WebResult<BatchImportRegionVO> batchImport(@RequestParam("file") MultipartFile file,@RequestParam(value = "isUpdate", defaultValue = "false") Boolean isUpdate) {
        if(file==null||file.isEmpty()||isUpdate==null){
            throw new ManagementBusinessException(ManagementExceptionEnum.BAD_PARAMETER, "必传非空!");
        }
        String batchId = "test123456";
        log.info("批量导入区域信息开始");

        // 创建线程安全的集合
        List<RegionUploadDataDTO> uploadDataDTOList =  new ArrayList<>();
        InputStream inputStream = null;
        try {
            inputStream = file.getInputStream();
            log.info("开始解析数据并校验字段, inputStream:{}", inputStream);
            ExcelReaderBuilder excelReaderBuilder = EasyExcel.read(inputStream, RegionUploadDataDTO.class,
                    new RegionUploadDataListener());
            uploadDataDTOList.addAll(excelReaderBuilder.sheet().doReadSync());

        } catch (Exception e) {
            log.info("文件解析失败", e);
            throw new ManagementBusinessException(ManagementExceptionEnum.BAD_PARAMETER, "文件解析失败!");
        }
        taskExecutor.execute(() -> {
            try {
                log.info("开始处理数据：总数{}", uploadDataDTOList.size());
                excelActionService.regionHandler(uploadDataDTOList, isUpdate);
                log.info("批量插入数据成功");
            } catch (Exception e) {
                log.info("异步预约单批量上传未知异常", e);
                throw new ManagementBusinessException(ManagementExceptionEnum.BAD_PARAMETER, "异步预约单批量上传未知异常!");
            }
        });

        BatchImportRegionVO reserveBatchImportVO = new BatchImportRegionVO();
        reserveBatchImportVO.setBatchId(batchId);
        reserveBatchImportVO.setIsSuccess(true);
        return WebResult.successData(reserveBatchImportVO);
    }

}
