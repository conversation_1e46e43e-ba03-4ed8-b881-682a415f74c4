package com.dt.train.management.center.utils;

import com.dt.framework.core.exception.BusinessException;
import com.dt.train.management.center.enums.ManagementExceptionEnum;
import com.dt.train.management.center.model.exception.ManagementBusinessException;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class ExceptionUtil {

    /**
     * log message and return business exception
     *
     * @param exceptionEnum exception enum
     * @return {@link BusinessException }
     */
    public static BusinessException businessException(ManagementExceptionEnum exceptionEnum){
        log.info(exceptionEnum.getMessage());
        return new ManagementBusinessException(exceptionEnum);
    }

    /**
     * 记录由异常枚举描述的信息，并根据提供的枚举创建业务异常。
     *
     * @param exceptionEnum   决定异常类型和描述的枚举值
     * @param detail 异常的详细信息
     * @return 返回一个封装了枚举描述的 {@link BusinessException} 实例
     */
    public static ManagementBusinessException businessException(ManagementExceptionEnum exceptionEnum, String detail){
        log.info(exceptionEnum.getMessage());
        return new ManagementBusinessException(exceptionEnum, detail);
    }

    /**
     * 记录由异常枚举描述的信息，并根据提供的枚举创建业务异常。
     *
     * @param exceptionEnum   决定异常类型和描述的枚举值
     * @param detail 异常的详细信息
     * @param args 参数
     * @return 返回一个封装了枚举描述的 {@link BusinessException} 实例
     */
    public static ManagementBusinessException businessExceptionWithArg(ManagementExceptionEnum exceptionEnum, String detail, String... args){
        log.info(exceptionEnum.getMessage());
        // 检查message是否包含格式说明符且args不为空
        if (exceptionEnum.getMessage().contains("%s") && args != null && args.length > 0) {
            return new ManagementBusinessException(exceptionEnum, detail, args);
        } else {
            return new ManagementBusinessException(exceptionEnum, detail);
        }
    }
}
