package com.dt.train.management.center.manager;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.UUID;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import com.dt.framework.business.component.upload.CommonUploadService;
import com.dt.framework.business.constant.BusinessCode;
import com.dt.framework.core.exception.BusinessException;
import com.dt.train.management.center.config.BusinessConfig;

import lombok.extern.slf4j.Slf4j;

/**
 * 上传服务实现
 *
 * <AUTHOR> xianzilei
 * @date : 2020/6/15 14:29
 */
@Slf4j
@Service
public class UploadManager{

    private static final String OSS_UPLOAD_FILE_BASE_KEY = "basebusiness/train-management-center/";

    private final CommonUploadService commonUploadService;

    private final BusinessConfig businessConfig;

    public UploadManager(CommonUploadService commonUploadService, BusinessConfig businessConfig) {
        this.commonUploadService = commonUploadService;
        this.businessConfig = businessConfig;
    }

    private static final DateTimeFormatter DATE_PATH_FORMATTER = 
        DateTimeFormatter.ofPattern("yyyy/MM/dd/HH");

    private String generateFileKey(String filePath) {
        String fileNameSuffix = getFileExtension(filePath);
        if (StringUtils.isBlank(fileNameSuffix)) {
            fileNameSuffix = "file";
        }
        return OSS_UPLOAD_FILE_BASE_KEY + 
               LocalDateTime.now().format(DATE_PATH_FORMATTER) + "/" + 
               fileNameSuffix + "/" + 
               UUID.randomUUID() + "." + fileNameSuffix;
    }

    private String getFileExtension(String filePath) {
        if (StringUtils.isBlank(filePath)) {
            return "";
        }
        int lastDotIndex = filePath.lastIndexOf('.');
        if (lastDotIndex == -1 || lastDotIndex == filePath.length() - 1) {
            return "";
        }
        return filePath.substring(lastDotIndex + 1);
    }

    public String commonUploadFile(String localFileAbsolutePath, String downloadFileName) {
        if (StringUtils.isBlank(localFileAbsolutePath)) {
            throw new IllegalArgumentException("Local file path cannot be blank");
        }
        
        String fileKey = generateFileKey(localFileAbsolutePath);
        String downloadUrl = commonUploadService.uploadFile(
            localFileAbsolutePath, 
            fileKey, 
            downloadFileName, 
            businessConfig.getDefaultOssPlatform()
        );

        if (StringUtils.isBlank(downloadUrl)) {
            throw new BusinessException(
                BusinessCode.INTERNAL_INVALID_OPERATION, 
                "Failed to upload file to OSS: " + localFileAbsolutePath
            );
        }

        return downloadUrl;
    }
}
