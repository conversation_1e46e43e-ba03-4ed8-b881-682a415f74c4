package com.dt.train.management.center.export.service.impl;

import java.io.File;
import java.util.List;
import java.util.UUID;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import com.alibaba.excel.EasyExcel; // 引入 EasyExcel
import com.dt.train.management.center.export.enums.ReportFormat;
import com.dt.train.management.center.export.enums.ReportType;
import com.dt.train.management.center.export.exception.ReportGenerationException;
import com.dt.train.management.center.export.service.ReportGenerator;
import com.dt.train.management.center.manager.UploadManager;

import lombok.extern.slf4j.Slf4j;

@Component
@Slf4j
public class ExcelReportGenerator implements ReportGenerator {

    @Value("${excel.base.tmp.dir}")
    private String baseTempDir;

    @Autowired
    private UploadManager uploadManager;

    @Override
    public boolean supports(ReportFormat format) {
        return ReportFormat.EXCEL.equals(format);
    }

    @Override
    public String generateReport(List<?> data, ReportType reportType) throws ReportGenerationException {

        String uuid = UUID.randomUUID().toString();
        String baseDir = baseTempDir + uuid+ "/";
        File path = new File(baseDir);
        if (!path.exists()) {
            path.mkdirs();
        }
        String filePath = baseDir + reportType.getFileName();
        log.info("导出文件临时地址：{}", filePath);

        try {
            // 使用 EasyExcel 写入数据
            // EasyExcel 会根据 headClass 中的 @ExcelProperty 注解自动生成表头和映射数据
            EasyExcel.write(filePath, reportType.getHeadClass())
                    .sheet(reportType.getSheetName())
                    .doWrite(data);
            return uploadManager.commonUploadFile(filePath, reportType.getFileName());
        } catch (Exception e) { // EasyExcel 内部可能抛出各种异常，这里统一捕获
            throw new ReportGenerationException("生成Excel报表失败: " + e.getMessage(), e);
        }
    }

}
