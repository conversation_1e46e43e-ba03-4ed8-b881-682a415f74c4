package com.dt.train.management.center.config;

import com.alibaba.ttl.TransmittableThreadLocal;

public class UserRequestContextHolder {

    private static final TransmittableThreadLocal<Long> requestUserId = new TransmittableThreadLocal<>();

    private static final TransmittableThreadLocal<String> requestUserName = new TransmittableThreadLocal<>();

    private static final TransmittableThreadLocal<String> requestDeptId = new TransmittableThreadLocal<>();

    private static final TransmittableThreadLocal<String> requestManageDeptId = new TransmittableThreadLocal<>();

    public static void setRequestUserId(Long userId) {
        requestUserId.set(userId);
    }

    public static Long getRequestUserId() {
        return requestUserId.get();
    }

    public static void removeRequestUserId() {
        requestUserId.remove();
    }

    public static void setRequestUserName(String userName) {
        requestUserName.set(userName);
    }

    public static String getRequestUserName() {
        return requestUserName.get();
    }

    public static void removeRequestUserName() {
        requestUserName.remove();
    }

    public static void setRequestDeptId(String deptId) {
        requestDeptId.set(deptId);
    }

    public static String getRequestDeptId() {
        return requestDeptId.get();
    }

    public static void removeRequestDeptId() {
        requestDeptId.remove();
    }

    public static void setRequestManageDeptId(String manageDeptId) {
        requestManageDeptId.set(manageDeptId);
    }

    public static String getRequestManageDeptId() {
        return requestManageDeptId.get();
    }

    public static void removeRequestManageDeptId() {
        requestManageDeptId.remove();
    }

}
