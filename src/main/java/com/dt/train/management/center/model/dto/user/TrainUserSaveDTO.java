package com.dt.train.management.center.model.dto.user;

import com.dt.train.management.center.validation.ConditionalNotNull;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import javax.validation.constraints.NotBlank;


@Data
@ConditionalNotNull(field = "phone", conditionField = "userType", conditionValues = {"0"}, message = "正式员工手机号不能为空")
@ConditionalNotNull(field = "roleId", conditionField = "userType", conditionValues = {"0"}, message = "正式员工角色不能为空")
public class TrainUserSaveDTO {

    private Integer id;

    @ApiModelProperty(value = "姓名")
    private String userName;

    @ApiModelProperty(value = "所属区域ID（多个区域分号分隔）")
    @NotBlank(message = "所属区域不能为空")
    private String deptId;

    @ApiModelProperty(value = "管理区域ID（多个区域分号分隔）")
    private String manageDeptId;

    @ApiModelProperty(value = "职位")
    private String userPosition;

    @ApiModelProperty(value = "角色id")
    private Integer roleId;

    @ApiModelProperty(value = "手机号码")
    private String phone;

    @ApiModelProperty(value = "办公地点")
    private String workPlace;

    @ApiModelProperty(value = "成员类型 0-正式员工 1-兼职人员")
    private Integer userType;
}
