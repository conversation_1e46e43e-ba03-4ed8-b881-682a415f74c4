package com.dt.train.management.center.model.dto.project;

import com.dt.train.management.center.model.common.HalfDayTime;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 
 * 表名: train_course_user_relation
 */
@Data
public class TrainCourseUserRelationSaveOrUpdateDTO {
    /**
     * 字段描述: 主键ID
     *
     * 字段名: train_course_user_relation.id
     *
     */
    @ApiModelProperty(value = "主键ID")
    private Integer id;

    /**
     * 字段描述: 项目ID
     *
     * 字段名: train_course_user_relation.project_id
     *
     */
    @ApiModelProperty(value = "项目ID")
    private Integer projectId;

    @ApiModelProperty(value = "项目区域ID")
    private Integer projectAreaId;

    /**
     * 字段描述: 班次ID
     *
     * 字段名: train_course_user_relation.course_id
     *
     */
    @ApiModelProperty(value = "班次ID")
    private Integer courseId;

    @ApiModelProperty(value = "班次区域ID")
    private Integer courseAreaId;

    /**
     * 字段描述: 用户ID
     *
     * 字段名: train_course_user_relation.user_id
     *
     */
    @ApiModelProperty(value = "用户ID")
    private Long userId;

    @ApiModelProperty(value = "用户名称")
    private String userName;

    @ApiModelProperty(value = "原始用户名称")
    private String originUserName;

    @ApiModelProperty(value = "用户部门ID")
    private String userDeptId;

    @ApiModelProperty(value = "用户部门名称")
    private String userDeptName;

    /**
     * 字段描述: 关系类型,1-支援人，2-执行人
     *
     * 字段名: train_course_user_relation.relation_type
     *
     */
    @ApiModelProperty(value = "关系类型,1-支援人，2-执行人")
    private Integer relationType;

    /**
     * 字段描述: 开始时间
     *
     * 字段名: train_course_user_relation.start_date
     *
     */
    @ApiModelProperty(value = "开始时间")
    private HalfDayTime startDate;

    /**
     * 字段描述: 结束时间
     *
     * 字段名: train_course_user_relation.end_date
     *
     */
    @ApiModelProperty(value = "结束时间")
    private HalfDayTime endDate;
}