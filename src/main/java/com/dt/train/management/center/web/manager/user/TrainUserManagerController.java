package com.dt.train.management.center.web.manager.user;

import javax.validation.Valid;

import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.dt.framework.business.dto.web.common.WebResult;
import com.dt.train.management.center.manager.TrainUserManager;
import com.dt.train.management.center.manager.UserStatusManager;
import com.dt.train.management.center.model.dto.user.TrainUserSaveDTO;
import com.dt.train.management.center.model.vo.user.TrainUserVO;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;

@RestController
@RequestMapping("/manager/user")
@Api(tags = "用户管理")
@Slf4j
public class TrainUserManagerController {

    private final TrainUserManager trainUserManager;

    private final UserStatusManager userStatusManager;

    public TrainUserManagerController(TrainUserManager trainUserManager, UserStatusManager userStatusManager) {
       this.trainUserManager = trainUserManager;
       this.userStatusManager = userStatusManager;
    }

    @GetMapping("/getByUserId")
    @ApiOperation("获取指定用户信息")
    public WebResult<TrainUserVO> getByUserId(@RequestParam String userId) {
        TrainUserVO trainUserVO = trainUserManager.getUserInfoByUserId(userId);
        return WebResult.successData(trainUserVO);
    }

    @PostMapping("")
    @ApiOperation("新增或编辑成员")
    public WebResult<Void> saveOrUpdateUser(@Valid @RequestBody TrainUserSaveDTO saveDTO) {
        trainUserManager.saveOrUpdateUser(saveDTO);
        return WebResult.success();
    }



    @PostMapping("/changeStatus")
    @ApiOperation("账号启用/停用")
    public WebResult<Void> changeUserStatus(
        @ApiParam("用户ID") @RequestParam(required = true) String userId
        , @ApiParam("用户状态") @RequestParam(required = true) Integer userStatus) {
        userStatusManager.changeUserStatus(userId, userStatus);
        return WebResult.success();
    }

    @DeleteMapping("/_delete")
    @ApiOperation("删除成员")
    public WebResult<Void> deleteUser(
        @ApiParam("用户ID") @RequestParam(required = true) String userId) {
        trainUserManager.deleteUserLogical(userId);
        return WebResult.success();
    }

    @DeleteMapping("/_delete/preCheck")
    @ApiOperation("删除成员前置校验")
    public WebResult<Void> preCheckDeleteUser(
        @ApiParam("用户ID") @RequestParam(required = true) String userId) {
        trainUserManager.precheck(userId);
        return WebResult.success();
    }

    
}
