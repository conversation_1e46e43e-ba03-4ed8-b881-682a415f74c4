package com.dt.train.management.center.service.log;


import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSONObject;
import com.dt.train.management.center.enums.LogTypeEnum;
import com.dt.train.management.center.model.dto.log.CommonLogDTO;
import com.dt.train.management.center.model.dto.log.LogRecord;

import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
public class AuditLogService {

    private final CommonLogService commonLogService;

    public AuditLogService(CommonLogService commonLogService) {
        this.commonLogService = commonLogService;
    }

    /**
     * 保存审计日志
     * 
     * @param logRecord
     */
    public void saveLog(LogRecord logRecord) {
        log.info("审计日志: " + logRecord.getTimestamp() +
                " [" + logRecord.getOperatorName() + " (" + logRecord.getOperatorId() + ")]" +
                " 模块: " + logRecord.getBusinessModule() +
                " 操作: " + logRecord.getAction() + // 此处为解析后的操作描述
                " 内容: " + logRecord.getContent() +
                " IP: " + logRecord.getIpAddress() +
                " 状态: " + logRecord.getStatus() +
                (logRecord.getErrorDetails() != null ? " 错误: " + logRecord.getErrorDetails() : "") +
                (logRecord.getParameters() != null ? " 参数: " + logRecord.getParameters() : "")
        );
        CommonLogDTO commonLogDTO = record2CommonLogDTO(logRecord);
        commonLogService.save(commonLogDTO);
    }

    private CommonLogDTO record2CommonLogDTO(LogRecord logRecord) {
        CommonLogDTO commonLogDTO = new CommonLogDTO();
        commonLogDTO.setLogType(LogTypeEnum.AUDIT_LOG);
        commonLogDTO.setLogSecondType(logRecord.getBusinessModule());
        commonLogDTO.setIp(logRecord.getIpAddress());
        commonLogDTO.setUserId(logRecord.getOperatorId().toString());
        commonLogDTO.setUserName(logRecord.getOperatorName());
        commonLogDTO.setBusinessId(logRecord.getBusinessId());
        commonLogDTO.setBusinessType(logRecord.getBusinessModule());
        commonLogDTO.setExtInfo(logRecord.getParameters());
        commonLogDTO.setDetail(JSONObject.toJSONString(logRecord));
        return commonLogDTO;
    }
}