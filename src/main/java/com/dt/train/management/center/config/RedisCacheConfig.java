package com.dt.train.management.center.config;

import com.dt.train.management.center.constant.CacheConstant;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.cache.RedisCacheConfiguration;
import org.springframework.data.redis.cache.RedisCacheManager;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.serializer.RedisSerializationContext;
import org.springframework.data.redis.serializer.RedisSerializer;

import java.time.Duration;
import java.util.HashMap;
import java.util.Map;

/**
 * redis cache config
 *
 * <AUTHOR>
 * @date 2024/07/25
 */
@EnableCaching
@Configuration
public class RedisCacheConfig {

    @Bean
    public RedisCacheManager cacheManager(RedisConnectionFactory redisConnectionFactory) {
        RedisCacheConfiguration configuration = redisCacheConfiguration();

        Map<String, RedisCacheConfiguration> cacheConfigurations = getCacheConfigurationMap(configuration);

        // Create and return a RedisCacheManager
        return RedisCacheManager.builder(redisConnectionFactory)
                .initialCacheNames(cacheConfigurations.keySet())
                .cacheDefaults(configuration)
                .withInitialCacheConfigurations(cacheConfigurations)
                .transactionAware()
                .build();
    }

    private Map<String, RedisCacheConfiguration> getCacheConfigurationMap(RedisCacheConfiguration configuration) {
        Map<String, RedisCacheConfiguration> cacheConfigurations = new HashMap<>();
        cacheConfigurations.put(CacheConstant.CONFIG_KEY, configuration.entryTtl(Duration.ofMinutes(10)));
        return cacheConfigurations;
    }

    /**
     * 自定义 RedisCacheConfiguration
     *
     * @return {@link RedisCacheConfiguration}
     */
    @Bean
    RedisCacheConfiguration redisCacheConfiguration(){
        // Define a default cache configuration
        return RedisCacheConfiguration.defaultCacheConfig()
                .entryTtl(Duration.ofHours(1)) // Set default cache expiration to 1 hour
                .serializeKeysWith(RedisSerializationContext.SerializationPair.fromSerializer(RedisSerializer.string()))
                .serializeValuesWith(RedisSerializationContext.SerializationPair.fromSerializer(RedisSerializer.json()))
                .disableCachingNullValues(); // Do not cache null values
    }
}
