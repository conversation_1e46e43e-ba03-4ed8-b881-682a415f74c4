package com.dt.train.management.center.model.common;

import lombok.Data;

@Data
public class HalfDayTimeWithUser {

    private HalfDayTime halfDayTime;

    private Long userId;

    private Integer courseId;

    @Override
    public boolean equals(Object obj) {
        if (obj instanceof HalfDayTimeWithUser) {
            HalfDayTimeWithUser other = (HalfDayTimeWithUser) obj;
            return halfDayTime.equals(other.halfDayTime) && userId.equals(other.userId) && courseId.equals(other.courseId);
        }
        return false;
    }

    @Override
    public int hashCode() {
        return halfDayTime.hashCode() + userId.hashCode() + courseId.hashCode();
    }

}
