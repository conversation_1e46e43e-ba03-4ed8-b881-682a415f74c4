package com.dt.train.management.center.utils;

import java.io.IOException;
import java.io.InputStream;
import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.PostConstruct;

import org.apache.commons.lang3.StringUtils;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Component;

import com.dt.train.management.center.model.dto.project.TrainProjectCRMDTO;
import com.dt.train.management.center.model.dto.project.TrainProjectCRMStartDTO;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;

/**
 * 字段映射工具类
 */
@Component
public class CRMProjectFieldMappingUtil {

    private static final ObjectMapper objectMapper = new ObjectMapper();
    
    // 存储字段映射关系
    private Map<String, String> fieldMappings = new HashMap<>();

    private Map<String, String> fieldMappingsStart = new HashMap<>();
    
    @PostConstruct
    public void init() {
        try {
            // 从classpath加载映射配置文件
            ClassPathResource resource = new ClassPathResource("templates/fxiaoke_crm_project.json");
            ClassPathResource resourceStart = new ClassPathResource("templates/fxiaoke_crm_project_start.json");
            try (InputStream inputStream = resource.getInputStream()) {
                // 解析JSON为Map
                fieldMappings = objectMapper.readValue(inputStream, new TypeReference<Map<String, String>>() {});
                try (InputStream inputStreamStart = resourceStart.getInputStream()) {
                    fieldMappingsStart = objectMapper.readValue(inputStreamStart, new TypeReference<Map<String, String>>() {});
                } catch (IOException e) {
                    throw new RuntimeException("加载字段映射配置文件失败", e);
                }
            }
        } catch (IOException e) {
            throw new RuntimeException("加载字段映射配置文件失败", e);
        }
    }

    public void mapToTargetProject(Map<String, Object> source, TrainProjectCRMDTO target) {
        mapToTarget(source, target, fieldMappings);
    }

    public void mapToTargetStart(Map<String, Object> source, TrainProjectCRMStartDTO target) {
        mapToTarget(source, target, fieldMappingsStart);
    }
    
    /**
     * 将源对象映射到目标对象(CRM数据到DTO)
     * @param source 源对象（包含CRM系统的数据）
     * @param target 目标对象（TrainProjectDTO实例）
     */
    private <T> void mapToTarget(Map<String, Object> source, T target, Map<String, String> fieldMappings) {
        if (source == null || target == null) {
            return;
        }
        
        try {
            Class<?> targetClass = target.getClass();
            
            // 遍历映射关系
            for (Map.Entry<String, String> entry : fieldMappings.entrySet()) {
                if(entry.getValue() == null) {
                    continue;
                }
                String targetField = entry.getKey();   // DTO字段名
                String sourceField = entry.getValue(); // CRM字段名
                
                // 获取源字段值
                Object value = getNestedValue(source, sourceField);
                if (value == null) {
                    continue;
                }
                
                // 设置目标字段值
                Field field = targetClass.getDeclaredField(targetField);
                field.setAccessible(true);
                
                // 类型转换处理
                Object convertedValue = convertValueType(value, field.getType());
                field.set(target, convertedValue);
            }
        } catch (Exception e) {
            throw new RuntimeException("字段映射失败", e);
        }
    }
    
    /**
     * 获取嵌套Map中的值
     * 支持三种格式：
     * 1. 普通嵌套字段："field1.field2"
     * 2. 数组索引字段："Field6__c[0]" 
     * 3. 数组嵌套字段："field_h26Mv__c__l.name"（获取数组中每个元素的name字段）
     */
    private Object getNestedValue(Map<String, Object> map, String key) {
        if (StringUtils.isEmpty(key) || map == null) {
            return null;
        }
        
        // 处理数组字段格式如"Field6__c[0]"
        if (key.matches(".+\\[\\d+\\]$")) {
            int bracketIndex = key.indexOf('[');
            String fieldName = key.substring(0, bracketIndex);
            int index = Integer.parseInt(key.substring(bracketIndex + 1, key.length() - 1));
            
            Object arrayObj = map.get(fieldName);
            if (arrayObj != null && arrayObj instanceof List) {
                List<?> list = (List<?>) arrayObj;
                return index < list.size() ? list.get(index) : null;
            }
            return null;
        }
        
        // 处理数组嵌套字段格式如"field_h26Mv__c__l.name"
        if (key.contains("__c__l.")) {
            String[] arrayParts = key.split("\\.");
            if (arrayParts.length != 2) {
                return null;
            }
            
            String arrayField = arrayParts[0];
            String nestedField = arrayParts[1];
            
            Object arrayObj = map.get(arrayField);
            if (!(arrayObj instanceof List)) {
                return null;
            }
            
            List<?> list = (List<?>) arrayObj;
            List<Object> result = new ArrayList<>();
            for (Object item : list) {
                if (item instanceof Map) {
                    @SuppressWarnings("unchecked")
                    Map<String, Object> itemMap = (Map<String, Object>) item;
                    Object value = itemMap.get(nestedField);
                    if (value != null) {
                        result.add(value);
                    }
                }
            }
            return result.isEmpty() ? null : result;
        }
        
        // 处理普通嵌套字段
        String[] parts = key.split("\\.");
        Object current = map;
        for (String part : parts) {
            if (!(current instanceof Map)) {
                return null;
            }
            @SuppressWarnings("unchecked")
            Map<String, Object> currentMap = (Map<String, Object>) current;
            current = currentMap.get(part);
            if (current == null) {
                return null;
            }
        }
        return current;
    }
    
    /**
     * 转换值类型
     */
    private Object convertValueType(Object value, Class<?> targetType) {
        if (value == null) {
            return null;
        }
        
        // 字符串转换
        if (targetType == String.class) {
            return value.toString();
        }
        
        // 整数转换
        if (targetType == Integer.class || targetType == int.class) {
            if (value instanceof Number) {
                return ((Number) value).intValue();
            }
            return Integer.parseInt(value.toString());
        }
        
        // 长整数转换
        if (targetType == Long.class || targetType == long.class) {
            if (value instanceof Number) {
                return ((Number) value).longValue();    
            }
            return Long.parseLong(value.toString());
        }

        // 长整数转换
        if (targetType == Float.class || targetType == float.class) {
            if (value instanceof Number) {
                return ((Number) value).floatValue();
            }
            return Float.parseFloat(value.toString());
        }

        //List<String>
        if (targetType == List.class) {
             if(value instanceof List){
                return value;
            }
            String[] values = ((String) value).split(";");
            return Arrays.asList(values);
        }
        
        // 其他类型转换可以根据需要添加
        
        return value;
    }
}
