package com.dt.train.management.center.model.dto.message;

import java.util.Date;

import com.dt.framework.business.util.page.PaginationParams;

import io.swagger.annotations.ApiModelProperty;

/**
 * 系统消息VO
 */
@lombok.Data
public class SystemMessageQueryDTO implements PaginationParams{


    /**
     * 用户中心-用户ID
     */
    @ApiModelProperty(value = "用户中心-用户ID")
    private Long userId;

    /**
     * 消息标题
     */
    @ApiModelProperty(value = "消息标题")
    private String messageTitle;

    /**
     * 消息内容
     */
    @ApiModelProperty(value = "消息内容")
    private String messageContent;

    /**
     * 消息类型
     */
    @ApiModelProperty(value = "消息类型")
    private Integer messageType;

    /**
     * 是否已读，0-否，1-是
     */
    @ApiModelProperty(value = "是否已读，0-否，1-是")
    private Integer isRead;

    /**
     * 是否无效，0-否，1-是
     */
    @ApiModelProperty(value = "是否无效，0-否，1-是", hidden = true)
    private Integer invalid;

    /**
     * 消息ID
     */
    @ApiModelProperty(value = "查询开始时间")
    private Date startDate;

    /**
     * 查询结束时间
     */
    @ApiModelProperty(value = "查询结束时间")
    private Date endDate;

    private Integer pageIndex;

    private Integer pageSize;

    /**
     * 业务类型
     */
    @ApiModelProperty(value = "业务类型")
    private Integer businessType;

    /**
     * 业务ID
     */
    @ApiModelProperty(value = "业务ID")
    private Long businessId;
}