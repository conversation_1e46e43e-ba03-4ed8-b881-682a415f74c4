package com.dt.train.management.center.model.dto.dict;

/**
 * 
 * 表名: dict_mapping
 *
 * @mbg.generated
 */
@lombok.Data
public class DictMappingDTO {
    /**
     * 字段描述: 主键ID
     *
     * 字段名: dict_mapping.id
     *
     * @mbg.generated
     */
    private Integer id;

    /**
     * 字段描述: 字典编码
     *
     * 字段名: dict_mapping.dict_code
     *
     * @mbg.generated
     */
    private String dictCode;

    /**
     * 字段描述: 第三方字典编码
     *
     * 字段名: dict_mapping.third_dict_code
     *
     * @mbg.generated
     */
    private String thirdDictCode;

    /**
     * 字段描述: 第三方系统
     *
     * 字段名: dict_mapping.third_system
     *
     * @mbg.generated
     */
    private String thirdSystem;

    /**
     * 字段描述: 字典类型
     *
     * 字段名: dict_mapping.dict_type
     *
     * @mbg.generated
     */
    private String dictType;

    /**
     * 字段描述: 字典值
     *
     * 字段名: dict_mapping.dict_value
     *
     * @mbg.generated
     */
    private String dictValue;

    /**
     * 字段描述: 字典描述
     *
     * 字段名: dict_mapping.dict_desc
     *
     * @mbg.generated
     */
    private String dictDesc;
}