package com.dt.train.management.center.model.dto.user;

import java.time.LocalDate;

import org.springframework.format.annotation.DateTimeFormat;

import com.dt.train.management.center.enums.HalfDayEnum;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class TrainUserWithQueryDTO extends TrainUserQueryDTO {

    @ApiModelProperty(value = "查询开始日期")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDate startTime;

    @ApiModelProperty(value = "查询开始日期段")
    private HalfDayEnum startHalfDay;

    @ApiModelProperty(value = "查询结束日期")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDate endTime;

    @ApiModelProperty(value = "查询结束日期段")
    private HalfDayEnum endHalfDay;

    @ApiModelProperty(value = "班次 - 排除")
    private Integer courseId;

}
