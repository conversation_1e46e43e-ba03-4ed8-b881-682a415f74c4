package com.dt.train.management.center.manager;

import java.time.LocalDate;
import java.time.LocalTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import com.alibaba.fastjson.JSONObject;
import com.dt.framework.business.dto.page.Pagination;
import com.dt.train.management.center.annotation.AuditLog;
import com.dt.train.management.center.config.BusinessConfig;
import com.dt.train.management.center.config.UserRequestContextHolder;
import com.dt.train.management.center.enums.AuditLogEnum;
import com.dt.train.management.center.enums.CheckDimensionEnum;
import com.dt.train.management.center.enums.CheckStatusEnum;
import com.dt.train.management.center.enums.CheckSummaryStatusEnum;
import com.dt.train.management.center.enums.CheckTypeEnum;
import com.dt.train.management.center.enums.HalfDayEnum;
import com.dt.train.management.center.enums.ManagementExceptionEnum;
import com.dt.train.management.center.enums.MessageEnum;
import com.dt.train.management.center.enums.TrainCourseAreaRelationTypeEnum;
import com.dt.train.management.center.enums.TrainCourseUserRelationTypeEnum;
import com.dt.train.management.center.enums.TrainTaskTypeEnum;
import com.dt.train.management.center.model.common.HalfDayTime;
import com.dt.train.management.center.model.dto.message.SystemMessageDTO;
import com.dt.train.management.center.model.dto.project.TrainCourseAreaRelationDTO;
import com.dt.train.management.center.model.dto.project.TrainCourseUserRelationDTO;
import com.dt.train.management.center.model.dto.task.TrainCheckRecordQueryDTO;
import com.dt.train.management.center.model.dto.task.TrainCheckRecordSaveDTO;
import com.dt.train.management.center.model.dto.task.TrainCheckTaskDTO;
import com.dt.train.management.center.model.dto.task.TrainCheckTaskQueryDTO;
import com.dt.train.management.center.model.dto.task.TrainTaskQueryDTO;
import com.dt.train.management.center.model.dto.user.TrainUserQueryDTO;
import com.dt.train.management.center.model.vo.check.AllocatedDayVO;
import com.dt.train.management.center.model.vo.check.TrainCheckRecordVO;
import com.dt.train.management.center.model.vo.check.TrainCheckTaskCalendarVO;
import com.dt.train.management.center.model.vo.check.TrainCheckTaskVO;
import com.dt.train.management.center.model.vo.check.TrainCheckTaskVOWithCourse;
import com.dt.train.management.center.model.vo.check.TrainCheckWorkbenchVO;
import com.dt.train.management.center.model.vo.dept.TrainDeptVO;
import com.dt.train.management.center.model.vo.project.CalendarUserUnitVO;
import com.dt.train.management.center.model.vo.project.TrainCourseVO;
import com.dt.train.management.center.model.vo.project.TrainCourseVOWithCheck;
import com.dt.train.management.center.model.vo.project.TrainCourseVOWithRecord;
import com.dt.train.management.center.model.vo.task.TrainTaskVO;
import com.dt.train.management.center.model.vo.user.TrainUserVO;
import com.dt.train.management.center.service.dept.TrainDeptService;
import com.dt.train.management.center.service.message.SystemMessageService;
import com.dt.train.management.center.service.project.TrainCourseAreaRelationService;
import com.dt.train.management.center.service.project.TrainCourseService;
import com.dt.train.management.center.service.project.TrainCourseUserRelationService;
import com.dt.train.management.center.service.task.TrainCheckRecordService;
import com.dt.train.management.center.service.task.TrainCheckTaskService;
import com.dt.train.management.center.service.task.TrainTaskService;
import com.dt.train.management.center.service.user.TrainUserService;
import com.dt.train.management.center.utils.DateTimeUtils;
import com.dt.train.management.center.utils.ExceptionUtil;

import lombok.extern.slf4j.Slf4j;

@Component
@Slf4j
public class TrainCheckTaskManager {

    private final TrainCourseUserRelationService trainCourseUserRelationService;

    private final TrainCourseAreaRelationService trainCourseAreaRelationService;

    private final TrainCourseService trainCourseService;

    private final TrainCheckTaskService trainCheckTaskService;

    private final TrainCheckRecordService trainCheckRecordService;

    private final TrainTaskService trainTaskService;

    private final BusinessConfig businessConfig;

    private final TrainDeptService trainDeptService;

    private final TrainUserService trainUserService;

    private final SystemMessageService systemMessageService;

    public TrainCheckTaskManager(TrainCourseUserRelationService trainCourseUserRelationService, TrainCourseService trainCourseService, TrainCheckTaskService trainCheckTaskService, TrainTaskService trainTaskService, TrainCheckRecordService trainCheckRecordService, BusinessConfig businessConfig, TrainCourseAreaRelationService trainCourseAreaRelationService, TrainDeptService trainDeptService, TrainUserService trainUserService, SystemMessageService systemMessageService) {
        this.trainCourseUserRelationService = trainCourseUserRelationService;
        this.trainCourseService = trainCourseService;
        this.trainCheckTaskService = trainCheckTaskService;
        this.trainCheckRecordService = trainCheckRecordService;
        this.trainTaskService = trainTaskService;
        this.businessConfig = businessConfig;
        this.trainCourseAreaRelationService = trainCourseAreaRelationService;
        this.trainDeptService = trainDeptService;
        this.trainUserService = trainUserService;
        this.systemMessageService = systemMessageService;
    }

    /**
     * 分页查询打卡任务
     * @param query
     * @return
     */
    public Pagination<TrainCheckTaskVOWithCourse> getCheckTaskPage(TrainCheckTaskQueryDTO query) {
        TrainDeptVO trainDeptVO = trainDeptService.getDeptVoFromDbById(query.getDeptId());
        if(trainDeptVO != null && trainDeptVO.getDeptType() != null && trainDeptVO.getDeptType() != 0) {
            TrainUserQueryDTO trainUserQueryDTO = new TrainUserQueryDTO();
            trainUserQueryDTO.setDeptId(query.getDeptId());
            trainUserQueryDTO.setPageIndex(0);
            trainUserQueryDTO.setPageSize(500);
            Pagination<TrainUserVO> pagination = trainUserService.pageQuery(trainUserQueryDTO);
            List<Long> userIds = pagination.getRows().stream().map(TrainUserVO::getUserId).collect(Collectors.toList());
            if(userIds.isEmpty()) {
                return new Pagination<>();
            }
            query.setUserIds(userIds);
        }
        Pagination<TrainCheckTaskVOWithCourse> pagination = trainCheckTaskService.page(query);
        return pagination;
    }

    /**
     * 获取打卡任务
     * 
     * @param userId
     * @param courseIds
     * @return
     */
    public TrainCheckWorkbenchVO getCheckWorkbench(Long userId, Integer courseId, LocalDate startDate, LocalDate endDate) {
        LocalDate today = LocalDate.now();
        HalfDayTime start = new HalfDayTime(startDate, HalfDayEnum.AM);
        HalfDayTime end = new HalfDayTime(endDate, HalfDayEnum.PM);
        List<CalendarUserUnitVO> userUnitVOList = trainCourseUserRelationService.listCalendarByTime(start.toFullDate(), end.toFullDate(), userId, courseId);
        List<Integer> courseIds = userUnitVOList.stream().map(CalendarUserUnitVO::getCourseId).distinct().collect(Collectors.toList());
        if(courseIds.isEmpty() && courseId != null) {
            courseIds.add(courseId);
        }
        if(courseIds.isEmpty()) {
            return new TrainCheckWorkbenchVO();
        }

        // List<TrainTaskVO> trainTaskVOList = trainTaskService.getTasksByCourseIds(courseIds);
        // List<Integer> courseHasTask = trainTaskVOList.stream().map(TrainTaskVO::getCourseId).collect(Collectors.toList());
        List<Integer> courseHasTask = courseIds;
        if(courseHasTask.isEmpty()) {
            return new TrainCheckWorkbenchVO();
        }

        List<TrainCheckTaskCalendarVO> checkTaskCalendarVOs = userUnitVOList.stream()
            .filter(vo -> courseHasTask.contains(vo.getCourseId()))
            .map(vo -> vo.getAtDate().getDate())
            .distinct()
            .map(date -> {
                TrainCheckTaskCalendarVO trainCheckTaskCalendarVO = new TrainCheckTaskCalendarVO();
                trainCheckTaskCalendarVO.setUserId(userId);
                trainCheckTaskCalendarVO.setCheckDate(date);
                trainCheckTaskCalendarVO.setCheckStatus(date.isBefore(today) ? CheckSummaryStatusEnum.ABNORMAL.getCode() : CheckSummaryStatusEnum.NOT_CHECKED.getCode());
                return trainCheckTaskCalendarVO;
            })
            .collect(Collectors.toList());
        
        Map<LocalDate, TrainCheckTaskCalendarVO> checkTaskCalendarVOsMap = 
            checkTaskCalendarVOs.stream().collect(Collectors.toMap(TrainCheckTaskCalendarVO::getCheckDate, Function.identity(), (a, b) -> {
                if(a.getCheckStatus() == CheckSummaryStatusEnum.ABNORMAL.getCode()) {
                    return a;
                }
                return b;
            }));
        
        List<TrainCourseVO> trainCourseVOList = trainCourseService.listByIds(courseHasTask, false);

        List<TrainCourseAreaRelationDTO> trainCourseAreaRelationVOList = trainCourseAreaRelationService.listByCourseIds(courseHasTask);
        Map<Integer, List<TrainCourseAreaRelationDTO>> trainCourseAreaRelationVOsMap = trainCourseAreaRelationVOList.stream().collect(Collectors.groupingBy(TrainCourseAreaRelationDTO::getCourseId));
        List<TrainCourseUserRelationDTO> trainCourseUserRelationVOList = trainCourseUserRelationService.listByCourseIds(courseHasTask);
        Map<Integer, List<TrainCourseUserRelationDTO>> trainCourseUserRelationVOsMap = trainCourseUserRelationVOList.stream().collect(Collectors.groupingBy(TrainCourseUserRelationDTO::getCourseId));
        trainCourseVOList.forEach(vo -> {
            List<TrainCourseAreaRelationDTO> trainCourseAreaRelationVOs = trainCourseAreaRelationVOsMap.get(vo.getId());
            if(trainCourseAreaRelationVOs != null) {
                vo.setCourseAreaList(trainCourseAreaRelationVOs.stream().filter(relation -> relation.getRelationType() == TrainCourseAreaRelationTypeEnum.EXECUTE.getCode()).collect(Collectors.toList()));
                vo.setCourseStudentAreaList(trainCourseAreaRelationVOs.stream().filter(relation -> relation.getRelationType() == TrainCourseAreaRelationTypeEnum.STUDENT.getCode()).collect(Collectors.toList()));
            }
            List<TrainCourseUserRelationDTO> trainCourseUserRelationVOs = trainCourseUserRelationVOsMap.get(vo.getId());
            if(trainCourseUserRelationVOs != null) {
                vo.setExecuteUsers(trainCourseUserRelationVOs.stream().filter(relation -> relation.getRelationType() == TrainCourseUserRelationTypeEnum.EXECUTE.getCode()).collect(Collectors.toList()));
                vo.setSupportUsers(trainCourseUserRelationVOs.stream().filter(relation -> relation.getRelationType() == TrainCourseUserRelationTypeEnum.SUPPORT.getCode()).collect(Collectors.toList()));
            }
        });
        TrainCheckWorkbenchVO trainCheckWorkbenchVO = new TrainCheckWorkbenchVO();
        trainCheckWorkbenchVO.setUserId(userId);
        trainCheckWorkbenchVO.setCourses(trainCourseVOList);
        trainCheckWorkbenchVO.setAllCheckDay(checkTaskCalendarVOs.size());

        TrainCheckTaskQueryDTO trainCheckTaskQueryDTO = new TrainCheckTaskQueryDTO();
        trainCheckTaskQueryDTO.setUserId(userId);
        trainCheckTaskQueryDTO.setCourseId(courseId);
        trainCheckTaskQueryDTO.setStartCheckDate(start.toFullDate());
        trainCheckTaskQueryDTO.setEndCheckDate(end.toFullDate());
        List<TrainCheckTaskDTO> trainCheckTaskDTOs = trainCheckTaskService.listByQuery(trainCheckTaskQueryDTO);
        trainCheckTaskDTOs.forEach(dto -> {
            LocalDate checkDate = dto.getCheckDate().toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
            TrainCheckTaskCalendarVO checkTaskCalendarVO = checkTaskCalendarVOsMap.get(checkDate);
            if(checkTaskCalendarVO == null){
                return;
            }
            if(dto.getCheckStatus() == CheckSummaryStatusEnum.NORMAL.getCode() && !checkDate.equals(today)) {
                checkTaskCalendarVO.setCheckStatus(dto.getCheckStatus());
            }
        });
        trainCheckWorkbenchVO.setNormalCheckedDay(checkTaskCalendarVOs.stream().filter(vo -> vo.getCheckStatus() == CheckSummaryStatusEnum.NORMAL.getCode()).count());
        trainCheckWorkbenchVO.setAbnormalCheckedDay(checkTaskCalendarVOs.stream().filter(vo -> vo.getCheckStatus() == CheckSummaryStatusEnum.ABNORMAL.getCode()).count());
        trainCheckWorkbenchVO.setCheckTasks(checkTaskCalendarVOs);
        trainCheckWorkbenchVO.setAllocatedDay(calculateAllocatedDay(userUnitVOList));
        return trainCheckWorkbenchVO;
    }

    private AllocatedDayVO calculateAllocatedDay(List<CalendarUserUnitVO> userUnitVOList) {
        if(userUnitVOList.isEmpty()) {
            return new AllocatedDayVO();
        }
        AllocatedDayVO allocatedDayVO = new AllocatedDayVO();
        List<HalfDayTime> halfDayList = userUnitVOList.stream()
            .map(CalendarUserUnitVO::getAtDate)
            .distinct()
            .collect(Collectors.toList());
        allocatedDayVO.setAllocatedDay(halfDayList.size() / 2f);
        HalfDayTime now = HalfDayTime.now();
        List<HalfDayTime> allocatedDates = halfDayList.stream()
            .filter(halfDay -> halfDay.isBefore(now))
            .collect(Collectors.toList());
        allocatedDayVO.setPastDay(allocatedDates.size() / 2f);
        allocatedDayVO.setFutureDay(allocatedDayVO.getAllocatedDay() - allocatedDayVO.getPastDay());
        LocalDate today = LocalDate.now();
        List<TrainCheckTaskCalendarVO> allocatedDatesVO = halfDayList.stream()
            .map(HalfDayTime::getDate)
            .distinct()
            .map(d -> {
                TrainCheckTaskCalendarVO trainCheckTaskCalendarVO = new TrainCheckTaskCalendarVO();
                trainCheckTaskCalendarVO.setCheckDate(d);
                trainCheckTaskCalendarVO.setCheckStatus(d.isBefore(today) ? CheckSummaryStatusEnum.NORMAL.getCode() : CheckSummaryStatusEnum.NOT_CHECKED.getCode());
                return trainCheckTaskCalendarVO;
            })
            .collect(Collectors.toList());
        allocatedDayVO.setAllocatedDates(allocatedDatesVO);
        return allocatedDayVO;
    }

    @Transactional(rollbackFor = Exception.class)
    public TrainCheckRecordVO checkIn(TrainCheckRecordSaveDTO trainCheckRecordSaveDTO) {
        if(trainCheckRecordSaveDTO.getCheckTypes() != null && !trainCheckRecordSaveDTO.getCheckTypes().isEmpty()) {
            List<TrainCheckRecordVO> trainCheckRecordVOList = new ArrayList<>();
            trainCheckRecordSaveDTO.getCheckTypes().forEach(checkType -> {
                trainCheckRecordSaveDTO.setCheckType(checkType);
                TrainCheckRecordVO trainCheckRecordVO = checkInSingle(trainCheckRecordSaveDTO);
                trainCheckRecordVOList.add(trainCheckRecordVO);
            });
            return trainCheckRecordVOList.get(0);
        } else {
            return checkInSingle(trainCheckRecordSaveDTO);
        }
    }

    /**
     * 签到打卡
     * 
     * @param trainCheckRecordSaveDTO
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public TrainCheckRecordVO checkInSingle(TrainCheckRecordSaveDTO trainCheckRecordSaveDTO) {
        LocalTime checkInTime = trainCheckRecordSaveDTO.getCheckTime().toInstant()
            .atZone(ZoneId.systemDefault())
            .toLocalTime();
        log.info("签到打卡时间: {}", checkInTime);
        TrainCheckTaskDTO checkTaskDTO = precheck(trainCheckRecordSaveDTO);

        int checkType = trainCheckRecordSaveDTO.getCheckType();
        CheckStatusEnum checkStatus = processCheckStatus(checkType, checkInTime);
        
        if(checkType == CheckTypeEnum.CHECK_IN.getCode()) {
            checkTaskDTO.setCheckInStatus(checkStatus.getCode());
        } else if (checkType == CheckTypeEnum.CHECK_OUT.getCode()) {
            checkTaskDTO.setCheckOutStatus(checkStatus.getCode());
        } else {
            checkTaskDTO.setCheckOnSiteStatus(checkStatus.getCode());
        }
        TrainCheckTaskVO checkTaskVO = trainCheckTaskService.saveOrUpdate(checkTaskDTO);

        trainCheckRecordSaveDTO.setTaskId(checkTaskVO.getTaskId());
        trainCheckRecordSaveDTO.setCheckTaskId(checkTaskVO.getId());
        trainCheckRecordSaveDTO.setCheckStatus(checkStatus.getCode());
        TrainCheckRecordVO recordVO = trainCheckRecordService.save(trainCheckRecordSaveDTO);
        recordVO.setIsValid(1);
        return recordVO;
    }

    private CheckStatusEnum processCheckStatus(int checkType, LocalTime now) {
        if(checkType == CheckTypeEnum.CHECK_IN.getCode()) {
            Integer checkInTimeNormalSecond = businessConfig.getCheckInTimeNormalSecond();
            if(now.isAfter(LocalTime.ofSecondOfDay(checkInTimeNormalSecond))) {
                return CheckStatusEnum.LATE;
            }
        } else if (checkType == CheckTypeEnum.CHECK_OUT.getCode()) {
            Integer checkOutTimeNormalSecond = businessConfig.getCheckOutTimeNormalSecond();
            if(now.isBefore(LocalTime.ofSecondOfDay(checkOutTimeNormalSecond))) {
                return CheckStatusEnum.EARLY;
            } 
        }
        return CheckStatusEnum.NORMAL;
    }

    private TrainCheckTaskDTO precheck(TrainCheckRecordSaveDTO trainCheckRecordSaveDTO) {
        LocalDate checkDay = trainCheckRecordSaveDTO.getCheckTime().toInstant()
            .atZone(ZoneId.systemDefault())
            .toLocalDate();
        LocalTime checkTime = trainCheckRecordSaveDTO.getCheckTime().toInstant()
            .atZone(ZoneId.systemDefault())
            .toLocalTime();
        // 1. 校验当前班次是否存在打卡任务
        // 2. 校验当前用户、当前班次、当前时间是否存在排期
        // 3. 校验当前时间是否支持指定类型的打卡签到
        // 4. 校验当前打卡任务是否存在打卡记录
        TrainTaskVO task = precheckCheckTask(trainCheckRecordSaveDTO);

        // 2. 校验当前用户、当前班次、当前时间是否存在排期
        List<CalendarUserUnitVO> calendarUserUnits = checkCalendar(trainCheckRecordSaveDTO, checkDay);

        // 3. 校验当前时间是否支持指定类型的打卡签到
        Integer checkType = trainCheckRecordSaveDTO.getCheckType();
        if (checkType == CheckTypeEnum.CHECK_IN.getCode()) {
            // 校验当前时间是否在打卡开始时间之后
            Integer checkInTimeEndSecond = businessConfig.getCheckInTimeEndSecond();
            if (checkTime.isAfter(LocalTime.ofSecondOfDay(checkInTimeEndSecond))) {
                throw ExceptionUtil.businessException(ManagementExceptionEnum.CHECK_IN_TIME_AFTER_END);
            }
        }

        // 4. 校验当前打卡任务是否存在打卡记录
        TrainCheckTaskQueryDTO trainCheckTaskQueryDTO = new TrainCheckTaskQueryDTO();
        trainCheckTaskQueryDTO.setTaskId(task.getId());
        trainCheckTaskQueryDTO.setUserId(trainCheckRecordSaveDTO.getUserId());
        trainCheckTaskQueryDTO.setStartCheckDate(DateTimeUtils.getStartOfDay(checkDay));
        trainCheckTaskQueryDTO.setEndCheckDate(DateTimeUtils.getEndOfDay(checkDay));
        List<TrainCheckTaskDTO> trainCheckTaskDTOs = trainCheckTaskService.listByQuery(trainCheckTaskQueryDTO);
        if (trainCheckTaskDTOs.isEmpty()) {
            List<HalfDayEnum> halfDayList = 
                calendarUserUnits.stream()
                    .map(CalendarUserUnitVO::getAtDate)
                    .map(HalfDayTime::getHalfDay)
                    .distinct()
                    .collect(Collectors.toList());

            return initTrainCheckTaskDTO(trainCheckRecordSaveDTO, checkDay, task, halfDayList);
        }
        TrainCheckTaskDTO trainCheckTaskDTO = trainCheckTaskDTOs.get(0);
        if (trainCheckTaskDTO.getCheckInStatus() == CheckStatusEnum.NORMAL.getCode() 
            && checkType == CheckTypeEnum.CHECK_IN.getCode()) {
            throw ExceptionUtil.businessException(ManagementExceptionEnum.CHECK_IN_ALREADY_CHECKED);
        }
        return trainCheckTaskDTO;
    }

    private TrainCheckTaskDTO initTrainCheckTaskDTO(TrainCheckRecordSaveDTO trainCheckRecordSaveDTO, LocalDate today,
            TrainTaskVO task, List<HalfDayEnum> halfDayList) {
        TrainCheckTaskDTO trainCheckTaskDTO = new TrainCheckTaskDTO();
        trainCheckTaskDTO.setTaskId(task.getId());
        trainCheckTaskDTO.setUserId(trainCheckRecordSaveDTO.getUserId());
        trainCheckTaskDTO.setUserName(trainCheckRecordSaveDTO.getUserName());
        trainCheckTaskDTO.setCourseId(trainCheckRecordSaveDTO.getCourseId());
        trainCheckTaskDTO.setProjectId(trainCheckRecordSaveDTO.getProjectId());
        trainCheckTaskDTO.setCheckDate(DateTimeUtils.getStartOfDay(today));
        trainCheckTaskDTO.setCheckInStatus(CheckStatusEnum.NOT_CHECKED.getCode());
        trainCheckTaskDTO.setCheckOutStatus(CheckStatusEnum.NOT_CHECKED.getCode());
        trainCheckTaskDTO.setCheckOnSiteStatus(CheckStatusEnum.NOT_CHECKED.getCode());
        trainCheckTaskDTO.setCheckStatus(CheckSummaryStatusEnum.ABNORMAL.getCode());
        trainCheckTaskDTO.setCheckDimension(CheckDimensionEnum.getByHalfDay(halfDayList).getCode());
        return trainCheckTaskDTO;
    }

    private List<CalendarUserUnitVO> checkCalendar(TrainCheckRecordSaveDTO trainCheckRecordSaveDTO, LocalDate checkDay) {
        Date startDate = DateTimeUtils.getStartOfDay(checkDay);
        Date endDate = DateTimeUtils.getEndOfDay(checkDay);
        List<CalendarUserUnitVO> calendarUserUnitVOs = 
            trainCourseUserRelationService.listCalendarByTime(startDate, endDate
                , trainCheckRecordSaveDTO.getUserId(), trainCheckRecordSaveDTO.getCourseId());
        if (calendarUserUnitVOs.isEmpty()) {
            throw ExceptionUtil.businessException(ManagementExceptionEnum.NO_SCHEDULE_FOR_USER);
        }
        return calendarUserUnitVOs;
    }

    private TrainTaskVO precheckCheckTask(TrainCheckRecordSaveDTO trainCheckRecordSaveDTO) {
        TrainTaskQueryDTO trainTaskQueryDTO = new TrainTaskQueryDTO();
        trainTaskQueryDTO.setCourseId(trainCheckRecordSaveDTO.getCourseId());
        trainTaskQueryDTO.setProjectId(trainCheckRecordSaveDTO.getProjectId());
        trainTaskQueryDTO.setTaskType(TrainTaskTypeEnum.CHECK_IN.getCode());
        TrainTaskVO task = trainTaskService.getTask(trainTaskQueryDTO);
        if (task == null) {
            throw ExceptionUtil.businessException(ManagementExceptionEnum.TASK_NOT_FOUND);
        }
        return task;
    }

    /**
     * 获取班次信息 + 打卡记录
     * 
     * @param userId
     * @param startDate
     * @param endDate
     * @return
     */
    public List<TrainCourseVOWithRecord> getCourseWithRecord(Long userId, LocalDate atDate) {
        HalfDayTime start = new HalfDayTime(atDate, HalfDayEnum.AM);
        HalfDayTime end = new HalfDayTime(atDate, HalfDayEnum.PM);
        List<CalendarUserUnitVO> userUnitVOList = trainCourseUserRelationService.listCalendarByTime(start.toFullDate(), end.toFullDate(), userId, null);
        List<Integer> courseIds = userUnitVOList.stream().map(CalendarUserUnitVO::getCourseId).distinct().collect(Collectors.toList());
        // List<TrainTaskVO> trainTaskVOList = trainTaskService.getTasksByCourseIds(courseIds);
        // courseIds = trainTaskVOList.stream().map(TrainTaskVO::getCourseId).collect(Collectors.toList());
        List<TrainCourseVO> trainCourseVOList = trainCourseService.listByIds(courseIds, false);

        TrainCheckRecordQueryDTO trainCheckRecordQueryDTO = new TrainCheckRecordQueryDTO();
        trainCheckRecordQueryDTO.setUserId(userId);
        trainCheckRecordQueryDTO.setCourseIds(courseIds);
        trainCheckRecordQueryDTO.setStartCheckTime(DateTimeUtils.getStartOfDay(atDate));
        trainCheckRecordQueryDTO.setEndCheckTime(DateTimeUtils.getEndOfDay(atDate));
        List<TrainCheckRecordVO> trainCheckRecordVOList = trainCheckRecordService.listByQuery(trainCheckRecordQueryDTO);

        TrainCheckTaskQueryDTO trainCheckTaskQueryDTO = new TrainCheckTaskQueryDTO();
        trainCheckTaskQueryDTO.setUserId(userId);
        trainCheckTaskQueryDTO.setStartCheckDate(start.toFullDate());
        trainCheckTaskQueryDTO.setEndCheckDate(end.toFullDate());
        List<TrainCheckTaskDTO> trainCheckTaskDTOs = trainCheckTaskService.listByQuery(trainCheckTaskQueryDTO);
        Map<Integer, TrainCheckTaskDTO> trainCheckTaskDTOsMap = trainCheckTaskDTOs.stream().collect(Collectors.toMap(TrainCheckTaskDTO::getCourseId, Function.identity(), (a, b) -> {
            if(a.getCheckStatus() == CheckSummaryStatusEnum.ABNORMAL.getCode()) {
                return a;
            }
            return b;
        }));
        List<TrainCourseAreaRelationDTO> trainCourseAreaRelationVOList = trainCourseAreaRelationService.listByCourseIds(courseIds);
        Map<Integer, List<TrainCourseAreaRelationDTO>> trainCourseAreaRelationVOsMap = trainCourseAreaRelationVOList.stream().collect(Collectors.groupingBy(TrainCourseAreaRelationDTO::getCourseId));
        Map<Integer, List<TrainCheckRecordVO>> trainCheckRecordVOsMap = trainCheckRecordVOList.stream().collect(Collectors.groupingBy(TrainCheckRecordVO::getCourseId));
        List<TrainCourseVOWithRecord> trainCourseVOWithRecordList = trainCourseVOList.stream().map(vo -> {
            TrainCourseVOWithRecord trainCourseVOWithRecord = new TrainCourseVOWithRecord();
            BeanUtils.copyProperties(vo, trainCourseVOWithRecord);
            TrainCheckTaskDTO trainCheckTaskDTO = trainCheckTaskDTOsMap.get(vo.getId());
            if(trainCheckTaskDTO != null) {
                trainCourseVOWithRecord.setCheckSummaryStatus(trainCheckTaskDTO.getCheckStatus());
            } else {
                trainCourseVOWithRecord.setCheckSummaryStatus(CheckSummaryStatusEnum.NOT_CHECKED.getCode());
            }
            trainCourseVOWithRecord.setCheckRecords(trainCheckRecordVOsMap.get(vo.getId()));
            List<TrainCourseAreaRelationDTO> trainCourseAreaRelationVOs = trainCourseAreaRelationVOsMap.get(vo.getId());
            if(trainCourseAreaRelationVOs != null) {
                trainCourseVOWithRecord.setCourseAreaList(trainCourseAreaRelationVOs.stream().filter(relation -> relation.getRelationType() == TrainCourseAreaRelationTypeEnum.EXECUTE.getCode()).collect(Collectors.toList()));
                trainCourseVOWithRecord.setCourseStudentAreaList(trainCourseAreaRelationVOs.stream().filter(relation -> relation.getRelationType() == TrainCourseAreaRelationTypeEnum.STUDENT.getCode()).collect(Collectors.toList()));
            }
            trainCourseVOWithRecord.setAllocatedDay(calculateAllocatedDay(trainCourseUserRelationService.listCalendarByTime(null, null, userId, vo.getId())));
            return trainCourseVOWithRecord;
        }).sorted(Comparator.comparing(TrainCourseVOWithRecord::getCheckSummaryStatus))
        .collect(Collectors.toList());
        return trainCourseVOWithRecordList;
    }


    /**
     * 获取班次及打卡记录
     * 
     * @param projectId
     * @param courseId
     * @return
     */
    public TrainCourseVOWithCheck getCourseCheckTask(Integer projectId, Integer courseId) {
        LocalDate today = LocalDate.now();
        TrainCourseVO trainCourseVO = trainCourseService.getById(courseId);
        if(trainCourseVO == null) {
            throw ExceptionUtil.businessException(ManagementExceptionEnum.COURSE_NOT_EXIST);
        }
        if(projectId == null){
            projectId = trainCourseVO.getProjectId();
        }
        TrainCheckTaskQueryDTO trainCheckTaskQueryDTO = new TrainCheckTaskQueryDTO();
        trainCheckTaskQueryDTO.setCourseId(courseId);
        trainCheckTaskQueryDTO.setProjectId(projectId);
        List<TrainCheckTaskDTO> trainCheckTaskDTOs = trainCheckTaskService.listByQuery(trainCheckTaskQueryDTO);
        List<TrainCheckTaskVO> trainCheckTaskVOs = trainCheckTaskDTOs
            .stream()
            .filter(dto -> !dto.getCheckDate().after(DateTimeUtils.getStartOfDay(today)) )
            .map(trainCheckTaskService::dto2vo)
            .collect(Collectors.toList());
        TrainCourseVOWithCheck trainCourseVOWithCheck = new TrainCourseVOWithCheck();
        BeanUtils.copyProperties(trainCourseVO, trainCourseVOWithCheck);
        trainCourseVOWithCheck.setCheckTasks(trainCheckTaskVOs);

        HalfDayTime now = HalfDayTime.now();

        List<TrainCourseUserRelationDTO> trainCourseUserRelationDTOs = trainCourseUserRelationService.listByCourseIds(Arrays.asList(courseId));
        trainCourseVOWithCheck.setShouldCheckDays(trainCourseUserRelationDTOs.stream().map(TrainCourseUserRelationDTO::getFullDays).reduce(0, Integer::sum));
        trainCourseVOWithCheck.setNormalCheckedDays((int)trainCheckTaskVOs.stream().filter(vo -> vo.getCheckStatus() == CheckSummaryStatusEnum.NORMAL.getCode()).count());
        trainCourseVOWithCheck.setAbnormalCheckedDays((int)trainCheckTaskVOs.stream().filter(vo -> vo.getCheckStatus() == CheckSummaryStatusEnum.ABNORMAL.getCode()).count());
        trainCourseVOWithCheck.setPastDay(trainCourseUserRelationDTOs.stream().map(dto -> 
            now.isBeforeEqual(dto.getStartDate()) ? 0f : 
            now.isAfter(dto.getEndDate()) ? dto.getEndDate().daysBetweenWithHalf(dto.getStartDate()) : 
            now.daysBetweenWithHalf(dto.getStartDate()) -0.5f).reduce(0f, Float::sum));
        trainCourseVOWithCheck.setFutureDay(trainCourseVOWithCheck.getPlanUserDays() - trainCourseVOWithCheck.getPastDay());
        return trainCourseVOWithCheck;
    }

    /**
     * 修改打卡状态
     * 
     * @param courseId 班次ID
     * @param checkTaskId 打卡任务ID
     * @param checkStatus 打卡状态
     */
    @AuditLog(action = AuditLogEnum.MODIFY_CHECK_STATUS)
    public TrainCheckTaskDTO updateCheckStatus(Integer courseId, Integer checkTaskId, Integer checkStatus) {
        TrainCheckTaskDTO oldTrainCheckTaskDTO = trainCheckTaskService.getById(checkTaskId);
        if(oldTrainCheckTaskDTO == null || oldTrainCheckTaskDTO.getCourseId() != courseId) {
            throw ExceptionUtil.businessException(ManagementExceptionEnum.CHECK_TASK_NOT_FOUND);
        }
        trainCheckTaskService.updateCheckStatus(checkTaskId, checkStatus);
         log.info("{}将{}{}的打卡状态由{}变更为{}", UserRequestContextHolder.getRequestUserName(), oldTrainCheckTaskDTO.getUserName(), oldTrainCheckTaskDTO.getCheckDate(), oldTrainCheckTaskDTO.getCheckStatus(), checkStatus);
         return oldTrainCheckTaskDTO;
    }

    /**
     * 处理未打卡状态
     * 
     * @param day
     */
    public void processUnCheckStatus(LocalDate day) {
        Date startDate = DateTimeUtils.getStartOfDay(day);
        Date endDate = DateTimeUtils.getEndOfDay(day);
        List<TrainCheckTaskDTO> checkTaskDTOs = trainCourseUserRelationService.listCheckTaskByTime(startDate, endDate, null, null);
        TrainCheckTaskQueryDTO trainCheckTaskQueryDTO = new TrainCheckTaskQueryDTO();
        trainCheckTaskQueryDTO.setStartCheckDate(startDate);
        trainCheckTaskQueryDTO.setEndCheckDate(endDate);
        List<TrainCheckTaskDTO> trainCheckTaskDTOs = trainCheckTaskService.listByQuery(trainCheckTaskQueryDTO);
        List<String> courseUserIds = trainCheckTaskDTOs.stream().map(dto -> dto.getCourseId() + "_" + dto.getUserId()).collect(Collectors.toList());
        List<TrainCheckTaskDTO> saveTrainCheckTaskDTOs = checkTaskDTOs.stream()
            .filter(dto -> !courseUserIds.contains(dto.getCourseId() + "_" + dto.getUserId())
                && dto.getTaskId() != null)
            .collect(Collectors.toList());
        trainCheckTaskService.saveBatch(saveTrainCheckTaskDTOs);
    }

    /**
     * 处理打卡提醒
     * 
     * @param messageEnum
     */
    public void processMessage(MessageEnum messageEnum) {
        log.info("生成{}打卡提醒", messageEnum.getDesc());
        LocalDate today = LocalDate.now();
        Date startDate = DateTimeUtils.getStartOfDay(today);
        Date endDate = DateTimeUtils.getEndOfDay(today);
        List<TrainCheckTaskDTO> checkTaskDTOs = trainCourseUserRelationService.listCheckTaskByTime(startDate, endDate, null, null);
        TrainCheckTaskQueryDTO trainCheckTaskQueryDTO = new TrainCheckTaskQueryDTO();
        trainCheckTaskQueryDTO.setStartCheckDate(startDate);
        trainCheckTaskQueryDTO.setEndCheckDate(endDate);
        List<TrainCheckTaskDTO> trainCheckTaskDTOs = trainCheckTaskService.listByQuery(trainCheckTaskQueryDTO);
        List<String> courseUserIds = trainCheckTaskDTOs.stream().map(dto -> dto.getCourseId() + "_" + dto.getUserId()).collect(Collectors.toList());
        List<TrainCheckTaskDTO> saveTrainCheckTaskDTOs = checkTaskDTOs.stream()
            .filter(dto -> !courseUserIds.contains(dto.getCourseId() + "_" + dto.getUserId()))
            .collect(Collectors.toList());
        log.info("生成{}打卡提醒数量: {}", messageEnum.getDesc(), saveTrainCheckTaskDTOs.size());
        if(saveTrainCheckTaskDTOs.isEmpty()) {
            return;
        }
        List<TrainCourseVO> trainCourseVOs = trainCourseService.listByIds(saveTrainCheckTaskDTOs.stream().map(TrainCheckTaskDTO::getCourseId).collect(Collectors.toList()), false);
        Map<Integer, TrainCourseVO> trainCourseVOsMap = trainCourseVOs.stream().collect(Collectors.toMap(TrainCourseVO::getId, Function.identity()));
        List<SystemMessageDTO> systemMessageDTOs = saveTrainCheckTaskDTOs.stream()
            .map(dto -> checkTask2MessageDTO(dto, messageEnum, trainCourseVOsMap.get(dto.getCourseId())))
            .filter(Objects::nonNull)
            .collect(Collectors.toList());
        systemMessageService.saveBatch(systemMessageDTOs);
    }

    private SystemMessageDTO checkTask2MessageDTO(TrainCheckTaskDTO trainCheckTaskDTO, MessageEnum messageEnum, TrainCourseVO trainCourseVO) {
        if(trainCourseVO == null) {
            return null;
        }
        LocalTime now = LocalTime.now();
        if(trainCheckTaskDTO.getCheckDimension() == CheckDimensionEnum.AFTERNOON.getCode()) {
            if(now.isBefore(LocalTime.NOON)) {
                return null;
            }
        } else {
            if(now.isAfter(LocalTime.NOON)) {
                return null;
            }
        }
        
        SystemMessageDTO systemMessageDTO = new SystemMessageDTO();
        systemMessageDTO.setUserId(trainCheckTaskDTO.getUserId());
        systemMessageDTO.setMessageTitle(trainCourseVO.getCourseName());
        systemMessageDTO.setMessageContent(StringUtils.EMPTY);
        systemMessageDTO.setMessageType(messageEnum.getCode());
        systemMessageDTO.setIsRead(0);
        systemMessageDTO.setBusinessId(Long.valueOf(trainCourseVO.getId()));
        systemMessageDTO.setBusinessType(1);
        JSONObject extInfo = new JSONObject();
        extInfo.put("courseForm", trainCourseVO.getCourseForm());
        extInfo.put("checkDimension", trainCheckTaskDTO.getCheckDimension());
        systemMessageDTO.setExtInfo(extInfo);
        systemMessageDTO.setGmtCreate(new Date());
        return systemMessageDTO;
    }

    /**
     * 修改上下班打卡状态
     * @param courseId 班次ID
     * @param checkTaskId 打卡任务ID
     * @param checkType 打卡类型
     */
    @Transactional(rollbackFor = Exception.class)
    @AuditLog(action = AuditLogEnum.MODIFY_CHECK_DETAIL_STATUS)
    public TrainCheckTaskDTO updateDetailCheckStatus(Integer courseId, Integer checkTaskId, CheckTypeEnum checkType) {
        TrainCheckTaskDTO oldTrainCheckTaskDTO = trainCheckTaskService.getById(checkTaskId);
        if(oldTrainCheckTaskDTO == null || oldTrainCheckTaskDTO.getCourseId() != courseId) {
            throw ExceptionUtil.businessException(ManagementExceptionEnum.CHECK_TASK_NOT_FOUND);
        }
        TrainCheckRecordSaveDTO trainCheckRecordSaveDTO = new TrainCheckRecordSaveDTO();
        trainCheckRecordSaveDTO.setProjectId(oldTrainCheckTaskDTO.getProjectId());
        trainCheckRecordSaveDTO.setCourseId(courseId);
        trainCheckRecordSaveDTO.setCheckType(checkType.getCode());
        trainCheckRecordSaveDTO.setUserId(oldTrainCheckTaskDTO.getUserId());
        trainCheckRecordSaveDTO.setUserName(oldTrainCheckTaskDTO.getUserName());
        switch (checkType) {
            case CHECK_IN:
                trainCheckRecordSaveDTO.setCheckTime(new Date(oldTrainCheckTaskDTO.getCheckDate().getTime() + (businessConfig.getCheckInTimeNormalSecond() - 60) * 1000));
                oldTrainCheckTaskDTO.setCheckOldStatus(oldTrainCheckTaskDTO.getCheckInStatus());
                break;
            case CHECK_OUT:
                trainCheckRecordSaveDTO.setCheckTime(new Date(oldTrainCheckTaskDTO.getCheckDate().getTime() + businessConfig.getCheckOutTimeNormalSecond() * 1000));
                oldTrainCheckTaskDTO.setCheckOldStatus(oldTrainCheckTaskDTO.getCheckOutStatus());
                break;
            default:
                trainCheckRecordSaveDTO.setCheckTime(new Date(oldTrainCheckTaskDTO.getCheckDate().getTime() + businessConfig.getCheckInTimeNormalSecond() * 1000));
                oldTrainCheckTaskDTO.setCheckOldStatus(oldTrainCheckTaskDTO.getCheckOnSiteStatus());
                break;
        }
        checkInSingle(trainCheckRecordSaveDTO);
        log.info("{}将{}{}的{}打卡状态由{}变更为{}", UserRequestContextHolder.getRequestUserName(), oldTrainCheckTaskDTO.getUserName(), oldTrainCheckTaskDTO.getCheckDate(), checkType.getDesc(), oldTrainCheckTaskDTO.getCheckStatus(), CheckStatusEnum.NORMAL.getDesc());
        return oldTrainCheckTaskDTO;
    }
}
