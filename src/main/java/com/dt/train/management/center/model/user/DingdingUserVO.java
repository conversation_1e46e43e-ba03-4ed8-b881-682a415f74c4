package com.dt.train.management.center.model.user;

import lombok.Data;

import java.util.List;
@Data
public class DingdingUserVO {
    // 钉钉用户id
    private String userId;
    // 钉钉用户姓名（汉字）
    private String name;
    // 钉钉用户手机号
    private String mobile;
    // 钉钉用户邮箱
    private String email;
    // 企业邮箱
    private String  orgEmail;
    // 办公地点
    private String workPlace;
    // 所属部门ids
    private List<Long> deptIds;
    // 钉钉职位
    private String userPosition;
}
