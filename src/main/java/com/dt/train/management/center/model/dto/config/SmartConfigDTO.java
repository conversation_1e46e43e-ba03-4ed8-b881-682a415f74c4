package com.dt.train.management.center.model.dto.config;

import io.swagger.annotations.ApiModelProperty;

import java.util.Date;

/**
 * 
 * 表名: smart_config
 *
 */
@lombok.Data
public class SmartConfigDTO {
    /**
     * 字段描述: 主键
     *
     * 字段名: smart_config.id
     *
     */
    @ApiModelProperty("id")
    private Integer id;

    /**
     * 字段描述: 配置键
     *
     * 字段名: smart_config.config_key
     *
     */
    @ApiModelProperty("配置键")
    private String configKey;

    /**
     * 字段描述: 配置键
     *
     * 字段名: smart_config.second_key
     *
     */
    @ApiModelProperty("配置二级键")
    private String secondKey;

    /**
     * 字段描述: 排序
     *
     * 字段名: smart_config.config_order
     *
     */
    @ApiModelProperty("排序")
    private Integer configOrder;

    /**
     * 字段描述: 扩展信息
     *
     * 字段名: smart_config.ext_info
     *
     */
    @ApiModelProperty("扩展信息")
    private String extInfo;

    /**
     * 字段描述: 配置类型 0 single, 1 chunk
     *
     * 字段名: smart_config.config_type
     *
     * @mbg.generated
     */
    @ApiModelProperty("配置类型")
    private Integer configType;

    /**
     * 字段描述: 是否已删除 0 否 1 是
     *
     * 字段名: smart_config.invalid
     *
     */
    @ApiModelProperty("是否已删除 0 否 1 是")
    private Integer invalid;

    /**
     * 字段描述: 创建人
     *
     * 字段名: smart_config.created_by
     *
     */
    @ApiModelProperty("创建人")
    private Long createdBy;

    /**
     * 字段描述: 修改人
     *
     * 字段名: smart_config.updated_by
     *
     */
    @ApiModelProperty("修改人")
    private Long updatedBy;

    /**
     * 字段描述: 创建时间
     *
     * 字段名: smart_config.gmt_create
     *
     */
    @ApiModelProperty("创建时间")
    private Date gmtCreate;

    /**
     * 字段描述: 修改时间
     *
     * 字段名: smart_config.gmt_modified
     *
     */
    @ApiModelProperty("修改时间")
    private Date gmtModified;

    /**
     * 字段描述: 配置值
     *
     * 字段名: smart_config.config_value
     *
     */
    @ApiModelProperty("配置值")
    private String configValue;
}