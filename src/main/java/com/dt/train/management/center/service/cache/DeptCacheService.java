package com.dt.train.management.center.service.cache;

import com.dt.train.management.center.mapper.TrainDeptDOMapper;
import com.dt.train.management.center.model.dao.TrainDeptDO;
import com.dt.train.management.center.model.vo.dept.DeptCacheVO;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.core.type.TypeReference;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.time.Duration;
import java.util.*;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Slf4j
public class DeptCacheService {

    private final TrainDeptDOMapper deptMapper;
    private final RedisTemplate<String, String> redisTemplate;
    private final ObjectMapper objectMapper;

    // 缓存键名
    private static final String DEPT_CACHE_KEY = "train_dept_full_list";
    private static final String DEPT_RELATION_MAP_KEY = "train_dept_relation_map";


    @PostConstruct
    public void initDeptCache() {
        try {
            List<DeptCacheVO> deptCacheVOS = buildFullDeptList();
            // 缓存部门列表和部门全名称
            cacheDeptList(deptCacheVOS);

            // 预构建父子部门映射
            buildAndCacheParentChildMap(deptCacheVOS);

            log.info("部门信息缓存成功，共 {} 个部门", deptCacheVOS.size());
        } catch (Exception e) {
            log.error("部门信息缓存失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 构建完整的部门列表（含完整路径名称,剔除隐藏部门）
     */
    private List<DeptCacheVO> buildFullDeptList() {
        List<DeptCacheVO> resultList = new ArrayList<>();
        // 获取所有有效部门
        List<TrainDeptDO> allDepts = deptMapper.selectAllDepts();
        // 剔除隐藏部门
        allDepts = allDepts.stream().filter(d -> d.getHidden() == 0).collect(Collectors.toList());
        Map<Integer, TrainDeptDO> allDeptMap = allDepts.stream().collect(Collectors.toMap(TrainDeptDO::getId, t -> t));

        for (TrainDeptDO deptDO : allDepts) {
            DeptCacheVO deptCacheVO = new DeptCacheVO();
            deptCacheVO.setDeptId(deptDO.getId());
            deptCacheVO.setParentDeptId(deptDO.getParentId());
            deptCacheVO.setTrainDeptName(deptDO.getDeptName());

            // 构建完整部门名称路径
            String completeName = buildCompleteDeptName(deptDO, allDeptMap);
            deptCacheVO.setCompleteDeptName(completeName);

            resultList.add(deptCacheVO);
        }
        return resultList;
    }

    /**
     * 递归构建完整部门名称路径
     * 格式：
     *   - 1级2级部门：直接返回部门名称
     *   - 3级部门：父部门shortName + "-" + 当前部门名称（无shortName时用父部门名称）
     *   - 多级部门：递归拼接完整路径
     */
    private String buildCompleteDeptName(TrainDeptDO dept, Map<Integer, TrainDeptDO> deptMap) {
        // 顶级部门（1级）直接返回部门名称
        if (dept.getParentId() == null || dept.getParentId() == -1) {
            return dept.getDeptName();
        }

        // 获取父部门
        TrainDeptDO parent = deptMap.get(dept.getParentId());
        if (parent == null) {
            return dept.getDeptName();
        }

        // 判断当前部门级别
        if (parent.getParentId() == null || parent.getParentId() == -1) {
            // 二级部门：直接返回部门名称
            return dept.getDeptName();
        } else {
            // 三级部门：需要获取二级部门的shortName
            TrainDeptDO grandParent = deptMap.get(parent.getParentId());
            if (grandParent == null) {
                return dept.getDeptName();
            }

            // 获取二级部门的显示名称（优先使用shortName）
            String grandParentName = StringUtils.isNotBlank(parent.getDeptNameShort())
                    ? parent.getDeptNameShort()
                    : parent.getDeptName();

            return grandParentName + "-" + dept.getDeptName();
        }
    }

    /**
     * 将部门列表缓存到Redis
     */
    private void cacheDeptList(List<DeptCacheVO> deptList) throws JsonProcessingException {
        // 转换为JSON字符串
        String json = objectMapper.writeValueAsString(deptList);

        // 缓存到Redis，设置过期时间（1天）
        redisTemplate.opsForValue().set(DEPT_CACHE_KEY, json, Duration.ofDays(1));
    }


    private void buildAndCacheParentChildMap(List<DeptCacheVO> deptList) throws JsonProcessingException {
        // 初始化映射表
        Map<Integer, List<Integer>> directChildrenMap = new HashMap<>();

        for (DeptCacheVO dept : deptList) {
            // 只处理有父部门的节点（排除根节点）
            Integer parentId = dept.getParentDeptId();
            if (parentId == null) {
                continue;
            }

            // 添加到父部门的直接子部门列表
            directChildrenMap.computeIfAbsent(parentId, k -> new ArrayList<>())
                    .add(dept.getDeptId());
        }

        // 序列化并存入Redis
        String json = objectMapper.writeValueAsString(directChildrenMap);
        redisTemplate.opsForValue().set(DEPT_RELATION_MAP_KEY, json, Duration.ofDays(1));

        log.debug("直接子部门映射已缓存，共 {} 个父节点", directChildrenMap.size());
    }

    /**
     * 从Redis获取父子部门关系映射
     */
    public Map<Integer, List<Integer>> getParentChildMap() {
        try {
            String json = redisTemplate.opsForValue().get(DEPT_RELATION_MAP_KEY);
            if (json == null || json.isEmpty()) {
                refreshDeptCache();
                json = redisTemplate.opsForValue().get(DEPT_CACHE_KEY);
                if (json == null || json.isEmpty()) {
                    log.warn("刷新缓存后Redis中仍然没有父子部门关系映射");
                    return Collections.emptyMap();
                }
            }
            TypeReference<Map<Integer, List<Integer>>> typeRef = new TypeReference<Map<Integer, List<Integer>>>() {};
            return objectMapper.readValue(json, typeRef);
        } catch (JsonProcessingException e) {
            log.error("解析父子部门映射失败: {}", e.getMessage(), e);
            return Collections.emptyMap();
        } catch (Exception e) {
            log.error("获取父子部门映射失败: {}", e.getMessage(), e);
            return Collections.emptyMap();
        }
    }

    /**
     * 根据父部门id获取所有子孙部门id集合(包含当前部门id)
     */
    public List<Integer> getAllChildrenDeptIds(Integer deptId) {
        if (deptId == null) {
            return Collections.emptyList();
        }

        Map<Integer, List<Integer>> parentChildMap = this.getParentChildMap();
        List<Integer> result = new ArrayList<>();
        result.add(deptId);

        // 递归获取所有子部门ID
        this.getRecursiveChildIds(deptId, parentChildMap, result);

        return result;
    }

    /**
     * 递归获取所有子部门ID（包括子级、孙级等）
     *
     * @param parentId 当前父部门ID
     * @param parentChildMap 部门父子关系映射表
     * @param result 结果集合
     */
    private void getRecursiveChildIds(Integer parentId,
                                      Map<Integer, List<Integer>> parentChildMap,
                                      List<Integer> result) {
        if (parentChildMap.containsKey(parentId)) {
            for (Integer childId : parentChildMap.get(parentId)) {
                // 避免循环依赖
                if (!result.contains(childId)) {
                    result.add(childId);
                    // 递归获取孙子部门
                    getRecursiveChildIds(childId, parentChildMap, result);
                }
            }
        }
    }


    /**
     * 删除部门缓存（仅操作Redis）
     */
    public void deleteDeptCache() {
        try {
            // 删除部门列表缓存
            redisTemplate.delete(DEPT_CACHE_KEY);

            // 删除父子映射缓存
            redisTemplate.delete(DEPT_RELATION_MAP_KEY);

            log.info("Redis中的部门缓存及父子映射缓存已删除");
        } catch (Exception e) {
            log.error("删除部门缓存失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 刷新部门缓存（仅操作Redis）
     */
    public void refreshDeptCache() {
        log.info("开始刷新Redis部门缓存");
        try {
            // 获取最新部门列表
            List<DeptCacheVO> deptCacheVOS = buildFullDeptList();

            // 更新部门列表缓存
            cacheDeptList(deptCacheVOS);

            // 更新父子部门映射缓存
            buildAndCacheParentChildMap(deptCacheVOS);

            log.info("Redis部门缓存刷新成功，共 {} 个部门", deptCacheVOS.size());
        } catch (Exception e) {
            log.error("刷新部门缓存失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 从Redis获取部门缓存
     */
    public List<DeptCacheVO> getCachedDeptList() {
        List<DeptCacheVO> deptCacheVOS = new ArrayList<>();
        try {
            String json = redisTemplate.opsForValue().get(DEPT_CACHE_KEY);
            if (json == null || json.isEmpty()) {
                refreshDeptCache();
                json = redisTemplate.opsForValue().get(DEPT_CACHE_KEY);
                if (json == null || json.isEmpty()) {
                    log.warn("刷新缓存后Redis中仍然没有部门缓存");
                    return Collections.emptyList();
                }
            }
            deptCacheVOS = objectMapper.readValue(json, new TypeReference<List<DeptCacheVO>>() {});
        } catch (JsonProcessingException e) {
            log.error("获取部门缓存失败: {}", e.getMessage(), e);
        }
        return deptCacheVOS;
    }

    /**
     * 根据部门id获取部门名称
     * @param deptIds 部门id
     * @return 部门名称
     */
    public String getDeptName(String deptIds) {
        if (StringUtils.isBlank(deptIds)) return "";
        
        List<DeptCacheVO> deptCacheList = getCachedDeptList();
        String[] deptIdArray = deptIds.split(";");
        
        return Arrays.stream(deptIdArray)
            .map(deptId -> deptCacheList.stream()
                .filter(dept -> String.valueOf(dept.getDeptId()).equals(deptId))
                .findFirst()
                .map(DeptCacheVO::getCompleteDeptName)
                .orElse(""))
            .filter(name -> !name.isEmpty())
            .collect(Collectors.joining(";"));
    }
}