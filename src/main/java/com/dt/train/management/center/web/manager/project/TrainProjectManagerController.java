package com.dt.train.management.center.web.manager.project;

import java.util.List;

import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.dt.framework.business.dto.web.common.WebResult;
import com.dt.train.management.center.manager.ProjectSyncManager;
import com.dt.train.management.center.model.vo.project.TrainProjectVO;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;

@RestController
@RequestMapping("/manager/project")
@Api(tags = "项目管理")
@Slf4j
public class TrainProjectManagerController {

    private final ProjectSyncManager projectSyncManager;

    public TrainProjectManagerController(ProjectSyncManager projectSyncManager) {
        this.projectSyncManager = projectSyncManager;
    }

    @GetMapping("/crm")
    @ApiOperation("根据项目编号查询项目")
    public WebResult<List<TrainProjectVO>> getByCrmId(@ApiParam("项目编号") @RequestParam(required = true) String projectCode) {
        List<TrainProjectVO> trainProjectVOList = projectSyncManager.getCRMByProjectCode(projectCode);
        return WebResult.successData(trainProjectVOList);
    }

    @GetMapping("/crm/sync")
    @ApiOperation("同步单个CRM项目")
    public WebResult<Void> syncSingleProject(@ApiParam("项目编号") @RequestParam(required = true) String projectCode
            ,@ApiParam("当前部门") @RequestParam(required = false) String currentDeptId) {
        projectSyncManager.syncSingleProject(projectCode, currentDeptId);
        return WebResult.success();
    }

    @GetMapping("/crm/sync/incremental")
    @ApiOperation("增量同步所有CRM项目")
    public WebResult<String> incrementalSyncAllProjects(@ApiParam("当前部门") @RequestParam(required = false) String currentDeptId) {
        String syncRequestId = projectSyncManager.incrementalSyncProject(currentDeptId);
        return WebResult.successData(syncRequestId);
    }

    @GetMapping("/crm/sync/process")
    @ApiOperation("获取同步项目进度")
    public WebResult<Integer> getSyncCrmProjectProcess(@ApiParam("同步请求ID") @RequestParam(required = true) String syncRequestId) {
        Integer process = projectSyncManager.getSyncCrmProjectProcess(syncRequestId);
        return WebResult.successData(process);
    }

    @DeleteMapping("/delete")
    @ApiOperation("逻辑删除项目")
    public WebResult<Void> deleteProject(@ApiParam("项目ID") @RequestParam(required = true) Integer id) {
        projectSyncManager.deleteProjectById(id);
        return WebResult.success();
    }

}
