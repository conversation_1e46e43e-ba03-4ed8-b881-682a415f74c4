package com.dt.train.management.center.web.dept;

import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.dt.framework.business.constant.DtHeaders;
import com.dt.framework.business.dto.web.common.WebResult;
import com.dt.train.management.center.config.BusinessConfig;
import com.dt.train.management.center.model.dept.UserDeptVO;
import com.dt.train.management.center.service.dept.TrainDeptService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

@RestController
@RequestMapping("/dept")
@Api(tags = "部门管理")
public class TrainDeptController {
    @Resource
    private TrainDeptService trainDeptService;
    @Resource
    private BusinessConfig businessConfig;

    @GetMapping("/getByUserId")
    @ApiOperation("查询当前用户所属部门及子部门")
    public WebResult<List<UserDeptVO>> getByUserId( @RequestHeader(DtHeaders.USER_ID) Long userId,
                                                    @RequestParam(value = "isAll", required = false, defaultValue = "false") Boolean isAll) {
        List<UserDeptVO> allDeptInfo = trainDeptService.getByUserId(userId,isAll);
        return WebResult.successData(allDeptInfo);
    }

    @GetMapping("/getUserById")
    @ApiOperation("查询当前部门及子部门的用户信息")
    public WebResult<List<UserDeptVO>> getUserById(@RequestParam(value = "deptId", required = true) Integer deptId,@RequestParam(value = "userName", required = false) String userName) {
        List<UserDeptVO> allDeptInfo = trainDeptService.getUserById(deptId,userName);
        return WebResult.successData(allDeptInfo);
    }

    @GetMapping("/getColor")
    @ApiOperation("获取部门ID和颜色映射关系")
    public WebResult<Map<String,String>> queryColor() {
       return WebResult.successData(businessConfig.getDeptColorMapping());
    }

}
