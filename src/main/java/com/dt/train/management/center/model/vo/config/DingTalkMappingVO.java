package com.dt.train.management.center.model.vo.config;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class DingTalkMappingVO {
    @ApiModelProperty(value = "钉钉部门名称")
    private String dingdingDeptName;
    @ApiModelProperty(value = "排班管理系统部门ID")
    private Integer trainDeptId;
    @ApiModelProperty(value = "该部门下所需职位，用于过滤当前部门下特定职位用户")
    private List<String> needPositions;
}
