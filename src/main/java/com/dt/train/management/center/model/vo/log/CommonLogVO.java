package com.dt.train.management.center.model.vo.log;

import com.alibaba.fastjson.JSONObject;
import com.dt.train.management.center.enums.LogTypeEnum;

/**
 * 
 * 表名: common_log
 *
 * @mbg.generated
 */
@lombok.Data
public class CommonLogVO {
    /**
     * 字段描述: 主键
     *
     * 字段名: common_log.id
     *
     * @mbg.generated
     */
    private Integer id;

    /**
     * 字段描述: 日志类型
     *
     * 字段名: common_log.log_type
     *
     * @mbg.generated
     */
    private LogTypeEnum logType;

    /**
     * 字段描述: 日志二级类型
     *
     * 字段名: common_log.log_second_type
     *
     * @mbg.generated
     */
    private String logSecondType;

    /**
     * 字段描述: ip地址
     *
     * 字段名: common_log.ip
     *
     * @mbg.generated
     */
    private String ip;

    /**
     * 字段描述: 用户id
     *
     * 字段名: common_log.user_id
     *
     * @mbg.generated
     */
    private String userId;

    /**
     * 字段描述: 用户名称
     *
     * 字段名: common_log.user_name
     *
     * @mbg.generated
     */
    private String userName;

    /**
     * 字段描述: 业务id
     *
     * 字段名: common_log.business_id
     *
     * @mbg.generated
     */
    private String businessId;

    /**
     * 字段描述: 业务类型
     *
     * 字段名: common_log.business_type
     *
     * @mbg.generated
     */
    private String businessType;

    /**
     * 字段描述: 扩展信息
     *
     * 字段名: common_log.ext_info
     *
     * @mbg.generated
     */
    private String extInfo;

    /**
     * 字段描述: 详细信息
     *
     * 字段名: common_log.detail
     *
     * @mbg.generated
     */
    private JSONObject detail;
}