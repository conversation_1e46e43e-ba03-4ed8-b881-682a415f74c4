package com.dt.train.management.center.model.vo.message;

import java.util.Date;

import com.dt.train.management.center.enums.MessageEnum;
import org.springframework.beans.BeanUtils;

import com.alibaba.fastjson.JSONObject;
import com.dt.train.management.center.model.dao.SystemMessageDO;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;

import io.swagger.annotations.ApiModelProperty;

/**
 * 系统消息VO
 */
@lombok.Data
public class SystemMessageVO {

    /**
     * 主键ID，仅做主键，业务字段统一使用user_id
     */
    @ApiModelProperty(value = "主键ID")
    private Integer id;

    /**
     * 用户中心-用户ID
     */
    @ApiModelProperty(value = "用户中心-用户ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long userId;

    /**
     * 消息标题
     */
    @ApiModelProperty(value = "消息标题")
    private String messageTitle;

    /**
     * 消息内容
     */
    @ApiModelProperty(value = "消息内容")
    private String messageContent;

    /**
     * 消息类型
     */
    @ApiModelProperty(value = "消息类型，0-其他，1-上班签到，2-下班签到，3-现场签到")
    private Integer messageType;

    /**
     * 消息类型描述
     */
    @ApiModelProperty(value = "消息类型描述")
    private String messageTypeDesc;

    /**
     * 是否已读，0-否，1-是
     */
    @ApiModelProperty(value = "是否已读，0-否，1-是")
    private Integer isRead;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private Date gmtCreate;

    /**
     * 扩展信息
     */
    @ApiModelProperty(value = "扩展信息")
    private JSONObject extInfo;

    /**
     * 字段描述: 业务ID
     *
     * 字段名: system_message.business_id
     *
     * @mbg.generated
     */
    @ApiModelProperty(value = "业务ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long businessId;

    /**
     * 字段描述: 业务类型, 1-课程
     *
     * 字段名: system_message.business_type
     *
     * @mbg.generated
     */
    @ApiModelProperty(value = "业务类型, 1-课程")
    private Integer businessType;

    @ApiModelProperty(value = "是否重要通知，false-否，true-是")
    private Boolean isImportant = Boolean.FALSE;


    public static SystemMessageVO fromDO(SystemMessageDO systemMessageDO) {
        SystemMessageVO systemMessageVO = new SystemMessageVO();
        BeanUtils.copyProperties(systemMessageDO, systemMessageVO);
        systemMessageVO.setExtInfo(JSONObject.parseObject(systemMessageDO.getExtInfo()));
        if(systemMessageDO.getMessageType().equals(MessageEnum.NONE_COURSE_PROJECT.getCode())
                || systemMessageDO.getMessageType().equals(MessageEnum.COURSE_INFO_CHANGE.getCode())
                || systemMessageDO.getMessageType().equals(MessageEnum.COURSE_ASSIGN_PERSON.getCode())
                || systemMessageDO.getMessageType().equals(MessageEnum.COURSE_PERSON_REMOVE.getCode())
                || systemMessageDO.getMessageType().equals(MessageEnum.COURSE_DELETE.getCode())){
            systemMessageVO.setIsImportant(Boolean.TRUE);
        }
        return systemMessageVO;
    }
}