package com.dt.train.management.center.mapper;

import java.util.List;

import com.dt.train.management.center.model.dao.TrainCheckRecordDO;
import com.dt.train.management.center.model.dto.task.TrainCheckRecordQueryDTO;

public interface TrainCheckRecordDOMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(TrainCheckRecordDO row);

    int insertSelective(TrainCheckRecordDO row);

    TrainCheckRecordDO selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(TrainCheckRecordDO row);

    int updateByPrimaryKey(TrainCheckRecordDO row);

    List<TrainCheckRecordDO> selectByQuery(TrainCheckRecordQueryDTO queryDTO);
}