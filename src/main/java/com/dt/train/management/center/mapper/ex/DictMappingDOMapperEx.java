package com.dt.train.management.center.mapper.ex;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.dt.train.management.center.mapper.DictMappingDOMapper;
import com.dt.train.management.center.model.dao.DictMappingDO;

public interface DictMappingDOMapperEx extends DictMappingDOMapper{

    List<DictMappingDO> selectByDictType(String dictType);

    List<DictMappingDO> selectByThirdDictCode(@Param("dictType") String dictType, @Param("thirdDictCode") String thirdDictCode);
}
