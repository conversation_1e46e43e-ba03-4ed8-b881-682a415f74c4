package com.dt.train.management.center.web.front.config;

import java.util.ArrayList;
import java.util.List;

import org.checkerframework.checker.units.qual.s;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.dt.framework.business.constant.DtHeaders;
import com.dt.framework.business.dto.web.common.WebResult;
import com.dt.train.management.center.manager.SmartConfigManager;
import com.dt.train.management.center.model.vo.config.SmartConfigVO;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;

@Api(tags = "前端配置")
@RestController
@RequestMapping("/front/config")
public class SmartConfigFrontController {

    private final SmartConfigManager smartConfigManager;

    public SmartConfigFrontController(SmartConfigManager smartConfigManager) {
        this.smartConfigManager = smartConfigManager;
    }


    @ApiOperation("获取个人配置")
    @GetMapping("/personal/single")
    public WebResult<SmartConfigVO> getPersonalSmartConfig(
        @RequestHeader(DtHeaders.USER_ID) Long userId,
        @ApiParam(value = "配置key", required = true) @RequestParam String configKey) {
        List<String> secondKey = new ArrayList<>();
        secondKey.add(userId.toString());
        SmartConfigVO config = smartConfigManager.getSingleConfig(configKey, secondKey);
        if(config == null){
            return WebResult.successData(null);
        }
        return WebResult.successData(config);
    }

    @ApiOperation("保存个人配置")
    @PostMapping("/personal")
    public WebResult<JSONObject> savePersonalSmartConfig(
        @RequestHeader(DtHeaders.USER_ID) Long userId,
        @RequestBody SmartConfigVO smartConfigVO) {
        smartConfigVO.setSecondKey(userId.toString());
        smartConfigVO.setConfigType(1);
        smartConfigVO.setConfigOrder(1);
        smartConfigManager.saveOrUpdateByKey(smartConfigVO);
        return WebResult.successData(null);
    }
}
