package com.dt.train.management.center.enums;

import java.util.Arrays;

import lombok.Getter;

@Getter
public enum MessageEnum {

    OTHER(0, "其他"),
    SIGN_IN(1, "上班签到"),
    SIGN_OUT(2, "下班签到"),
    SIGN_ON_SITE(3, "现场签到"),
    NONE_COURSE_PROJECT(4, "项目未配置班次"),
    COURSE_INFO_CHANGE(5, "班次重要信息变更"),
    COURSE_ASSIGN_PERSON(6, "班次人员分配"),
    COURSE_PERSON_CHANGE(7, "班次人员变更"),
    COURSE_PERSON_REMOVE(8, "班次人员移除"),
    COURSE_START(9, "班次开始"),
    COURSE_DELETE(10, "班次删除"),
    ;

    private final int code;

    private final String desc;

    MessageEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    /**
     * 根据code获取枚举
     * 
     * @param code
     * @return
     */
    public static MessageEnum getByCode(int code) {
        return Arrays.stream(MessageEnum.values())
                .filter(messageEnum -> messageEnum.getCode() == code)
                .findFirst()
                .orElse(OTHER);
    }
}
