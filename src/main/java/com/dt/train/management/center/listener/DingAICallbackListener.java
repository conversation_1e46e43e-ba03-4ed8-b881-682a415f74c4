package com.dt.train.management.center.listener;

import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSONObject;
import com.dingtalk.open.app.api.callback.OpenDingTalkCallbackListener;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@Component
public class DingAICallbackListener implements OpenDingTalkCallbackListener<JSONObject, JSONObject> {

    @Override
    public JSONObject execute(JSONObject params) {
        String agentCode = params.getString("agentCode");
        String accountToken = params.getString("accountToken");
        log.info("DingAICallback, agentCode:{} accountToken:{}", agentCode, accountToken);
        
        JSONObject result = new JSONObject();
        result.put("resultCode", "200");
        result.put("account", accountToken);
        result.put("message", accountToken);
        return result;
    }

}
