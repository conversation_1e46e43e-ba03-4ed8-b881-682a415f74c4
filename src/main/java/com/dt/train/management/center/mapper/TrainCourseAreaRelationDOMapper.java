package com.dt.train.management.center.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.dt.train.management.center.model.dao.TrainCourseAreaRelationDO;
import com.dt.train.management.center.model.dto.project.TrainCourseAreaRelationQueryDTO;

public interface TrainCourseAreaRelationDOMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(TrainCourseAreaRelationDO row);

    int insertSelective(TrainCourseAreaRelationDO row);

    TrainCourseAreaRelationDO selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(TrainCourseAreaRelationDO row);

    int updateByPrimaryKey(TrainCourseAreaRelationDO row);

    void deleteByCourseId(Integer courseId);

    List<TrainCourseAreaRelationDO> selectByQuery(TrainCourseAreaRelationQueryDTO queryDTO);

    List<TrainCourseAreaRelationDO> selectByProjectIds(@Param("projectIds") List<Integer> projectIds);

    List<TrainCourseAreaRelationDO> selectByCourseIds(@Param("courseIds") List<Integer> courseIds);
}