package com.dt.train.management.center.service.region.Impl;

import com.dt.train.management.center.mapper.ex.DictMappingDOMapperEx;
import com.dt.train.management.center.model.dao.DictMappingDO;
import com.dt.train.management.center.model.dto.region.RegionInfoDTO;
import com.dt.train.management.center.service.region.RegionService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Service
@Slf4j
public class RegionServiceImpl implements RegionService {
    @Resource
    private DictMappingDOMapperEx dictMappingDOMapperEx;

    /**
     * 根据CRM的地区唯一id查询地区信息
     *
     * @param crmId  crm的地区唯一id
     * @return regionInfoDTO
     */
    @Override
    public RegionInfoDTO getRegionInfoByCrmIdAndType(String dictType, String crmId) {
        List<DictMappingDO>  dictMappingDOList = dictMappingDOMapperEx.selectByThirdDictCode(dictType, crmId);
        if (CollectionUtils.isEmpty(dictMappingDOList) || dictMappingDOList.get(0) == null){
            return  new RegionInfoDTO();
        }
        DictMappingDO dictMappingDO = dictMappingDOList.get(0);
        RegionInfoDTO regionInfoDTO = new RegionInfoDTO();
        regionInfoDTO.setCrmId(dictMappingDO.getThirdDictCode());
        regionInfoDTO.setUserCenterId(dictMappingDO.getDictCode());
        regionInfoDTO.setName(dictMappingDO.getDictValue());
        regionInfoDTO.setType(dictMappingDO.getDictType());
        return regionInfoDTO;
    }
}
