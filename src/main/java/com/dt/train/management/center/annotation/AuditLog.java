package com.dt.train.management.center.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

import com.dt.train.management.center.enums.AuditLogEnum;

/**
 * 自定义审计日志注解
 */
@Target(ElementType.METHOD) // 注解作用于方法
@Retention(RetentionPolicy.RUNTIME) // 注解在运行时可见
public @interface AuditLog {

    /**
     * 操作描述/类型 (例如: "用户登录", "修改订单状态")
     * 支持SpEL表达式
     */
    AuditLogEnum action();

    /**
     * 是否记录方法参数详情 (默认为false，如果为true，会将方法参数序列化为JSON存储)
     */
    boolean recordParameters() default false;
}
