package com.dt.train.management.center.feign.param;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 查账号请求体
 *
 * <AUTHOR>
 * @date 2021/09/17
 */
@Data
public class SelectUserRequest {

    /**
     * 客户端ID
     */
    @NotNull(message = "clientId不能为空")
    private String clientId;


    /**
     * 字段描述: 手机号
     */
    private List<String> mobiles;

    /**
     * 字段描述: id
     */
    private List<Long> ids;
}
