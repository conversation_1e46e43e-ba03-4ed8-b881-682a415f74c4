package com.dt.train.management.center.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.dt.train.management.center.model.dao.TrainCourseUserRelationDO;
import com.dt.train.management.center.model.dto.project.CourseUserRelationQueryDTO;
import com.dt.train.management.center.model.dto.task.TrainCourseUserRelationDTOWithCourse;

public interface TrainCourseUserRelationDOMapper {
    /**
     * 根据项目ID批量更新项目区域ID
     * @param projectId 项目ID
     * @param newAreaId 新的区域ID
     */
    void batchUpdateProjectAreaIdByProjectId(@Param("projectId") Integer projectId, @Param("newAreaId") Integer newAreaId);
    int deleteByPrimaryKey(Integer id);

    int insert(TrainCourseUserRelationDO row);

    int insertSelective(TrainCourseUserRelationDO row);

    TrainCourseUserRelationDO selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(TrainCourseUserRelationDO row);

    int updateByPrimaryKey(TrainCourseUserRelationDO row);

    void deleteByCourseIdAndRelationType(TrainCourseUserRelationDO trainCourseUserRelationDO);

    List<TrainCourseUserRelationDO> selectByProjectIds(@Param("projectIds") List<Integer> projectIds);

    List<TrainCourseUserRelationDO> selectByCourseIds(@Param("courseIds") List<Integer> courseIds);

    List<TrainCourseUserRelationDO> selectByQuery(CourseUserRelationQueryDTO queryDTO);

    List<TrainCourseUserRelationDTOWithCourse> listWithCourseByQuery(CourseUserRelationQueryDTO queryDTO);
}
