package com.dt.train.management.center.service.permission.impl;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.dt.train.management.center.enums.ManagementExceptionEnum;
import com.dt.train.management.center.mapper.SysRoleDOMapper;
import com.dt.train.management.center.mapper.SysRolePermissionDOMapper;
import com.dt.train.management.center.model.dao.SysRoleDO;
import com.dt.train.management.center.model.vo.permission.SysRoleVO;
import com.dt.train.management.center.model.vo.permission.SysRoleWithPermissionKeysVO;
import com.dt.train.management.center.service.permission.SysRoleService;
import com.dt.train.management.center.service.user.TrainUserService;
import com.dt.train.management.center.utils.ExceptionUtil;

@Service
public class SysRoleServiceImpl implements SysRoleService {

    private final SysRoleDOMapper sysRoleDOMapper;
    
    private final SysRolePermissionDOMapper sysRolePermissionDOMapper;

    private final TrainUserService trainUserService;

    public SysRoleServiceImpl(SysRoleDOMapper sysRoleDOMapper, SysRolePermissionDOMapper sysRolePermissionDOMapper, TrainUserService trainUserService) {
        this.sysRoleDOMapper = sysRoleDOMapper;
        this.sysRolePermissionDOMapper = sysRolePermissionDOMapper;
        this.trainUserService = trainUserService;
    }

    @Override
    public SysRoleVO createRole(SysRoleDO role) {
        // 校验是否有重名角色
        SysRoleDO existRole = sysRoleDOMapper.selectByRoleName(role.getRoleName());
        if (existRole != null) {
            throw ExceptionUtil.businessException(ManagementExceptionEnum.ROLE_NAME_EXISTS);
        }
        sysRoleDOMapper.insertSelective(role);
        return do2vo(role);
    }

    @Override
    public SysRoleVO updateRole(SysRoleDO role) {
        sysRoleDOMapper.updateByPrimaryKeySelective(role);
        return do2vo(role);
    }

    @Override
    public void deleteRole(Integer roleId) {
        Integer userCount = trainUserService.countUsersByRoleId(roleId);
        if (userCount > 0) {
            throw ExceptionUtil.businessException(ManagementExceptionEnum.ROLE_HAS_USERS);
        }
        // 删除角色权限关联
        sysRolePermissionDOMapper.deleteByRoleId(roleId);
        // 删除角色
        SysRoleDO role = sysRoleDOMapper.selectByPrimaryKey(roleId);
        if (role == null) {
            throw ExceptionUtil.businessException(ManagementExceptionEnum.ROLE_NOT_EXIST);
        }
        // 删除角色
        sysRoleDOMapper.deleteByPrimaryKey(roleId);
    }

    @Override
    public List<SysRoleVO> listAllRoles() {
        List<SysRoleDO> roleDOS = sysRoleDOMapper.selectAll();
        return roleDOS.stream().map(this::do2vo).collect(Collectors.toList());
    }

    @Override
    public SysRoleVO getRoleById(Integer roleId) {
        SysRoleDO roleDO = sysRoleDOMapper.selectByPrimaryKey(roleId);
        return do2vo(roleDO);
    }

    private SysRoleVO do2vo(SysRoleDO roleDO) {
        if (roleDO == null) {
            return null;
        }
        SysRoleVO roleVO = new SysRoleVO();
        BeanUtils.copyProperties(roleDO, roleVO);
        return roleVO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean assignPermissions(Integer roleId, List<String> permissionKeys) {
        // 先删除原有权限
        sysRolePermissionDOMapper.deleteByRoleId(roleId);
        
        // 添加新权限
        if (permissionKeys != null && !permissionKeys.isEmpty()) {
            sysRolePermissionDOMapper.batchInsert(roleId, permissionKeys);
        }
        return true;
    }

    /**
     * 获取角色的权限key列表
     * 
     * @param roleId 角色ID
     * @return 权限key列表
     */
    @Override
    public SysRoleWithPermissionKeysVO getRolePermissions(Integer roleId) {
        SysRoleVO role = getRoleById(roleId);
        if(role == null) {
            throw ExceptionUtil.businessException(ManagementExceptionEnum.ROLE_NOT_EXIST);
        }
        SysRoleWithPermissionKeysVO roleWithPermissionTree = new SysRoleWithPermissionKeysVO();
        BeanUtils.copyProperties(role, roleWithPermissionTree);
        roleWithPermissionTree.setUserCount(trainUserService.countUsersByRoleId(roleId));
        
        List<String> assignedPermissionKeys = getPermissionKeys(roleId);
        roleWithPermissionTree.setPermissionKeys(assignedPermissionKeys);

        return roleWithPermissionTree;
    }

    private List<String> getPermissionKeys(Integer roleId) {
        return sysRolePermissionDOMapper.getPermissionKeysByRoleId(roleId);
    }


    /**
     * 批量获取所有父级权限key并去重
     * 父权限为子权限冒号分隔的前缀
     * @param keys 权限key集合
     * @return 去重后的父权限集合
     */
    private Set<String> getAllParentKeys(Collection<String> keys) {
        Set<String> parentKeys = new HashSet<>();
        for (String key : keys) {
            String current = key;
            while (true) {
                current = StringUtils.substringBeforeLast(current, ":");
                if (StringUtils.isBlank(current)) break;
                if(parentKeys.contains(current)) {
                    break; // 如果已经包含则跳出循环
                }
                parentKeys.add(current);
            }
        }
        return parentKeys;
    }
    
    @Override
    public List<String> getAssignedPermissionKeys(Integer roleId) {
        List<String> keys = getPermissionKeys(roleId);
        if (keys == null || keys.isEmpty()) {
            return Collections.emptyList();
        }
        
        // 获取直接分配的权限+所有父权限
        Set<String> allKeys = new HashSet<>(keys);
        allKeys.addAll(getAllParentKeys(keys));
        return new ArrayList<>(allKeys);
    }
}
