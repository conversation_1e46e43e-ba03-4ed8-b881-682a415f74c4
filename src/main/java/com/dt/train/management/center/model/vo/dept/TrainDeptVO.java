package com.dt.train.management.center.model.vo.dept;

import io.swagger.annotations.ApiModelProperty;

import java.util.Date;

/**
 * 
 * 表名: train_dept
 *
 * @mbg.generated
 */
@lombok.Data
public class TrainDeptVO {
    @ApiModelProperty(value = "主键ID", example = "1")
    private Integer id;

    @ApiModelProperty(value = "父级部门ID,0为顶级部门", example = "0")
    private Integer parentId;

    @ApiModelProperty(value = "部门名称", example = "项目管理部")
    private String deptName;

    @ApiModelProperty(value = "部门名称简称", example = "项目")
    private String deptNameShort;

    @ApiModelProperty(value = "父级部门名称", example = "项目管理部")
    private String parentDeptName;

    @ApiModelProperty(value = "父级部门名称简称", example = "项目管理部")
    private String parentDeptNameShort;

    @ApiModelProperty(value = "部门类型,0-超管部门，1-普通部门", example = "1")
    private Integer deptType;

    @ApiModelProperty(value = "部门层级", example = "1")
    private Integer deptLevel;

    @ApiModelProperty(value = "是否隐藏，0-不隐藏，1-隐藏", example = "0")
    private Integer hidden;

    @ApiModelProperty(value = "是否已删除 0 否 1 是", example = "0")
    private Integer invalid;

    @ApiModelProperty(value = "创建人", example = "10001")
    private Long createdBy;

    @ApiModelProperty(value = "更新人", example = "10001")
    private Long updatedBy;

    @ApiModelProperty(value = "创建时间", example = "2023-01-01 00:00:00")
    private Date gmtCreate;

    @ApiModelProperty(value = "修改时间", example = "2023-01-01 00:00:00")
    private Date gmtModified;
}