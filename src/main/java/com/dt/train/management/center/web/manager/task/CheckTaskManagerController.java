package com.dt.train.management.center.web.manager.task;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.dt.framework.business.dto.page.Pagination;
import com.dt.framework.business.dto.web.common.WebResult;
import com.dt.train.management.center.enums.CheckTypeEnum;
import com.dt.train.management.center.manager.TrainCheckTaskManager;
import com.dt.train.management.center.model.dto.task.TrainCheckTaskQueryDTO;
import com.dt.train.management.center.model.vo.check.TrainCheckTaskVOWithCourse;
import com.dt.train.management.center.model.vo.project.TrainCourseVOWithCheck;
import com.dt.train.management.center.utils.DateTimeUtils;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;

@RestController
@RequestMapping("/manager/task/check")
@Api(tags = "打卡任务管理")
@Slf4j
public class CheckTaskManagerController {


    private final TrainCheckTaskManager trainCheckTaskManager;

    public CheckTaskManagerController(TrainCheckTaskManager trainCheckTaskManager) {
        this.trainCheckTaskManager = trainCheckTaskManager;
    }

    @ApiOperation(value = "获取打卡列表分页")
    @GetMapping("/page")
    public WebResult<Pagination<TrainCheckTaskVOWithCourse>> getCheckTaskPage(TrainCheckTaskQueryDTO query) {
        if(query.getEndCheckDate() != null){
            query.setEndCheckDate(DateTimeUtils.getEndOfDay(query.getEndCheckDate()));
        }
        Pagination<TrainCheckTaskVOWithCourse> pagination = trainCheckTaskManager.getCheckTaskPage(query);
        return WebResult.successData(pagination);
    }

    @ApiOperation(value = "获取班次及打卡列表")
    @GetMapping("/course")
    public WebResult<TrainCourseVOWithCheck> getCourseCheckTask(
        @ApiParam("项目ID") @RequestParam(required = false) Integer projectId,
        @ApiParam("班次ID") @RequestParam(required = true) Integer courseId
    ) {
        TrainCourseVOWithCheck trainCourseVOWithCheck = trainCheckTaskManager.getCourseCheckTask(projectId, courseId);
        return WebResult.successData(trainCourseVOWithCheck);
    }

    @ApiOperation(value = "修改打卡状态")
    @PostMapping("/status")
    public WebResult<Void> updateCheckStatus(
        @ApiParam("班次ID") @RequestParam(required = true) Integer courseId,
        @ApiParam("打卡ID") @RequestParam(required = true) Integer checkTaskId,
        @ApiParam("打卡状态, -1:待打卡, 0:正常, 1:异常") @RequestParam(required = true) Integer checkStatus) {
        trainCheckTaskManager.updateCheckStatus(courseId, checkTaskId, checkStatus);
        return WebResult.success();
    }

    @ApiOperation(value = "修改上下班打卡状态")
    @PostMapping("/status/{checkType}")
    public WebResult<Void> updateCheckStatus(
        @ApiParam("打卡类型") @PathVariable(required = true) CheckTypeEnum checkType,
        @ApiParam("班次ID") @RequestParam(required = true) Integer courseId,
        @ApiParam("打卡ID") @RequestParam(required = true) Integer checkTaskId) {
        trainCheckTaskManager.updateDetailCheckStatus(courseId, checkTaskId, checkType);
        return WebResult.success();
    }
}
