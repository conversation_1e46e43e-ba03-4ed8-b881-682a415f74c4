package com.dt.train.management.center.model.vo.project;

import java.util.List;

import com.dt.train.management.center.model.common.HalfDayTime;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class TrainCourseCalendarVO {

     /**
     * 字段描述: 主键ID
     *
     * 字段名: train_course.id
     *
     */
    @ApiModelProperty(value = "主键ID")
    private Integer id;

    /**
     * 字段描述: 项目ID
     *
     * 字段名: train_course.project_id
     *
     */
    @ApiModelProperty(value = "项目ID")
    private Integer projectId;

    /**
     * 字段描述: 项目名称
     *
     * 字段名: train_course.project_name
     *
     */
    @ApiModelProperty(value = "项目名称")
    private String projectName;

    /**
     * 字段描述: 项目编码
     *
     * 字段名: train_course.project_code
     *
     */
    @ApiModelProperty(value = "项目编码")
    private String projectCode;

    /**
     * 字段描述: 班次名称
     *
     * 字段名: train_course.course_name
     *
     */
    @ApiModelProperty(value = "班次名称")
    private String courseName;

    /**
     * 字段描述: 班次状态,0-未开始，1-进行中，2-已完成
     *
     * 字段名: train_course.course_status
     *
     */
    @ApiModelProperty(value = "班次状态,0-未开始，1-进行中，2-已完成")
    private Integer courseStatus;

    /**
     * 字段描述: 班次类型,train_course_type
     *
     * 字段名: train_course.course_type
     *
     */
    @ApiModelProperty(value = "班次类型,train_course_type")
    private Integer courseType;

    /**
     * 字段描述: 班次人数
     *
     * 字段名: train_course.course_people_num
     *
     */
    @ApiModelProperty(value = "班次人数")
    private Integer coursePeopleNum;

    /**
     * 字段描述: 服务对象
     *
     * 字段名: train_course.service_object
     *
     */
    @ApiModelProperty(value = "服务对象")
    private String serviceObject;

    /**
     * 字段描述: 是否异地，0-否，1-是
     *
     * 字段名: train_course.is_remote
     *
     */
    @ApiModelProperty(value = "是否异地，0-否，1-是")
    private Integer isRemote;

    /**
     * 字段描述: 班次执行区域
     *
     * 字段名: train_course.course_area
     *
     */
    @ApiModelProperty(value = "班次执行区域")
    private String courseArea;

    /**
     * 字段描述: 班次执行区域id
     *
     * 字段名: train_course.course_area_id
     *
     */
    @ApiModelProperty(value = "班次执行区域id")
    private Integer courseAreaId;

    /**
     * 字段描述: 计划开始时间
     *
     * 字段名: train_course.plan_start_date
     *
     */
    @ApiModelProperty(value = "计划开始时间")
    private HalfDayTime planStartDate;

    /**
     * 字段描述: 计划结束时间
     *
     * 字段名: train_course.plan_end_date
     *
     */
    @ApiModelProperty(value = "计划结束时间")
    private HalfDayTime planEndDate;

    /**
     * 字段描述: 实际开始时间
     *
     * 字段名: train_course.real_start_date
     *
     */
    @ApiModelProperty(value = "实际开始时间")
    private HalfDayTime realStartDate;

    /**
     * 字段描述: 实际结束时间
     *
     * 字段名: train_course.real_end_date
     *
     */
    @ApiModelProperty(value = "实际结束时间")
    private HalfDayTime realEndDate;

    @ApiModelProperty(value = "班次形式，1-面授，2-远程直播，3-现场直播")
    private Integer courseForm;

    @ApiModelProperty(value = "执行人/支援人列表")
    private List<CalendarUserUnitVO> relationUsers;
    
}
