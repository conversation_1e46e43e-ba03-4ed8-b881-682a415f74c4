package com.dt.train.management.center.model.dto.task;

import java.util.Date;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 
 * 表名: train_course_user_relation
 *
 * @mbg.generated
 */
@Data
public class TrainCourseUserRelationDTOWithCourse{
    /**
     * 字段描述: 主键ID
     *
     * 字段名: train_course_user_relation.id
     *
     * @mbg.generated
     */
    private Integer id;

    /**
     * 字段描述: 项目ID
     *
     * 字段名: train_course_user_relation.project_id
     *
     * @mbg.generated
     */
    private Integer projectId;

    /**
     * 字段描述: 班次ID
     *
     * 字段名: train_course_user_relation.course_id
     *
     * @mbg.generated
     */
    private Integer courseId;

    /**
     * 字段描述: 用户ID
     *
     * 字段名: train_course_user_relation.user_id
     *
     * @mbg.generated
     */
    private Long userId;

    @ApiModelProperty(value = "用户名称")
    private String userName;

    @ApiModelProperty(value = "原始用户名称")
    private String originUserName;

    @ApiModelProperty(value = "用户部门ID")
    private String userDeptId;

    @ApiModelProperty(value = "用户部门名称")
    private String userDeptName;

    /**
     * 字段描述: 关系类型,1-支援人，2-执行人
     *
     * 字段名: train_course_user_relation.relation_type
     *
     * @mbg.generated
     */
    private Integer relationType;

    /**
     * 字段描述: 开始时间
     *
     * 字段名: train_course_user_relation.start_date
     *
     * @mbg.generated
     */
    private Date startDate;

    /**
     * 字段描述: 结束时间
     *
     * 字段名: train_course_user_relation.end_date
     *
     * @mbg.generated
     */
    private Date endDate;

    /**
     * 字段描述: 班次名称
     *
     * 字段名: train_course.course_name
     *
     */
    @ApiModelProperty(value = "班次名称")
    private String courseName;

    @ApiModelProperty(value = "班次形式，1-面授，2-远程直播，3-现场直播")
    private Integer courseForm;

    @ApiModelProperty(value = "班次状态，1-待执行，2-执行中，3-已完成")
    private Integer courseStatus;
}