package com.dt.train.management.center.service.user.impl;

import com.alibaba.fastjson.JSON;
import com.dt.framework.business.constant.BusinessCode;
import com.dt.framework.core.exception.BusinessException;
import com.dt.train.management.center.config.BusinessConfig;
import com.dt.train.management.center.feign.client.AuthCenterFeignClient;
import com.dt.train.management.center.feign.param.SelectUserRequest;
import com.dt.train.management.center.feign.result.UserPassportDO;
import com.dt.train.management.center.handler.DingdingHandler;
import com.dt.train.management.center.manager.FXiaoKeManager;
import com.dt.train.management.center.mapper.TrainDeptDOMapper;
import com.dt.train.management.center.mapper.TrainUserDOMapper;
import com.dt.train.management.center.model.dao.TrainDeptDO;
import com.dt.train.management.center.model.dao.TrainUserDO;
import com.dt.train.management.center.model.dto.tools.UserUploadDataDTO;
import com.dt.train.management.center.model.response.fxiaoke.FxiaokeUserQueryResponse;
import com.dt.train.management.center.model.user.DingdingUserVO;
import com.dt.train.management.center.service.user.UserAndDepartmentMaintenanceService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
public class UserAndDepartmentMaintenanceServiceImpl implements UserAndDepartmentMaintenanceService {
    @Resource
    private AuthCenterFeignClient authCenterFeignClient;
    @Resource
    private TrainUserDOMapper trainUserDOMapper;
    @Resource
    private TrainDeptDOMapper trainDeptDOMapper;
    @Resource
    private DingdingHandler dingdingHandler;
    @Resource
    private BusinessConfig businessConfig;
    @Resource
    private FXiaoKeManager fXiaoKeManager;
    /**
     * 新增指定用户和部门关系（支持批量）
     *
     * @param uploadDataDTOList 用户上传数据dto列表
     * @param operationType 操作类型
     */
    @Override
    @Transactional
    public void batchSaveOrUpdate(List<UserUploadDataDTO> uploadDataDTOList, String operationType, List<DingdingUserVO> dingdingUserVOS,
                                  List<TrainDeptDO> trainDeptDOs,List<String> mobileList,List<FxiaokeUserQueryResponse.Employee> empList) {
        log.info("批量维护用户信息开始");
        if(CollectionUtils.isEmpty(uploadDataDTOList)){
            log.info("上传数据为空，不执行任何操作");
            return;
        }
        // 整理纷享销客数据
        Map<String,String> mobile2IdMap = new HashMap<>();
        Map<String,String> realName2IdMap = new HashMap<>();
        if(CollectionUtils.isNotEmpty(empList)){
            mobile2IdMap = empList.stream().collect(Collectors.toMap(
                    FxiaokeUserQueryResponse.Employee::getMobile,
                    FxiaokeUserQueryResponse.Employee::getOpenUserId,
                    (existing, replacement) -> existing
            ));
            realName2IdMap = empList.stream().collect(Collectors.toMap(
                            FxiaokeUserQueryResponse.Employee::getName,
                            FxiaokeUserQueryResponse.Employee::getOpenUserId,
                            (existing, replacement) -> existing // 取第一个遇到的值
                    ));
        }
        // 根据部门名称查询部门信息
        Map<String, TrainDeptDO> deptMap = trainDeptDOs.stream().collect(Collectors.toMap(TrainDeptDO::getDeptName, Function.identity()));
        // 保存或更新用户信息
        Map<String, DingdingUserVO> dingdingUserVOMap = dingdingUserVOS.stream().collect(Collectors.toMap(DingdingUserVO::getMobile, Function.identity()));
        List<TrainUserDO> existUsers = trainUserDOMapper.selectByPhones(mobileList);
        Map<String, TrainUserDO> existUserMap = existUsers == null ? new HashMap<>() :existUsers.stream().collect(Collectors.toMap(TrainUserDO::getPhone, Function.identity()));
        List<TrainUserDO> toInsert = new ArrayList<>();
        List<TrainUserDO> toUpdate = new ArrayList<>();
        long notExistUserId = -1L;
        for (UserUploadDataDTO userDTO : uploadDataDTOList) {
            TrainUserDO userDo = new TrainUserDO();
            DingdingUserVO dingdingUserVO = dingdingUserVOMap.get(userDTO.getMobile());
            // 从用户中心获取用户id
            UserPassportDO userCenterInfo = getUserCenterUserDTOList(userDTO.getMobile(), userDTO.getName());
            userDo.setUserId(userCenterInfo.getId() == null ? notExistUserId : userCenterInfo.getId());
            if(userCenterInfo.getId() == null){
                notExistUserId--;
            }
            String userIdFist = mobile2IdMap.get(userDTO.getMobile());
            String userIdSecond = realName2IdMap.getOrDefault(userDTO.getName(), "-1");
            if(StringUtils.isEmpty(userIdFist)){
                log.info("用户:{}的手机号:{},在纷享销客中不存在", userDTO.getName(),userDTO.getMobile());
            }
            log.info("查询纷享销客返回结果userIdFist:{},userIdSecond:{}", userIdFist, userIdSecond);
            userDo.setCrmUserId(StringUtils.isEmpty(userIdFist) ? userIdSecond : userIdFist);
            if(dingdingUserVO != null){
                userDo.setDingTalkUserId(dingdingUserVO.getUserId());
                userDo.setEmail(dingdingUserVO.getEmail());
                userDo.setWorkPlace(dingdingUserVO.getWorkPlace());
                userDo.setUserPosition(dingdingUserVO.getUserPosition());
                userDo.setUserStatus(0);
            }else{
                userDo.setDingTalkUserId("-1");
                userDo.setUserStatus(1);
            }
            userDo.setUserRole(userDTO.getRole());
            userDo.setPhone(userDTO.getMobile());
            userDo.setInvalid(0);
            userDo.setUserName(userDTO.getName());

            if (existUserMap.containsKey(userDTO.getMobile())) {
                log.info("用户已存在，更新用户信息");
                TrainUserDO existUser = existUserMap.get(userDTO.getMobile());
                userDo.setId(existUser.getId());
                if ("ADD".equals(operationType)) {
                    log.info("在用户已有部门基础上加添部门");
                    String mergedDeptIds = mergeDeptIds(existUser.getDeptIds(), Collections.singletonList(deptMap.get(userDTO.getDeptName()).getId()));
                    userDo.setDeptIds(mergedDeptIds);
                } else if ("UPDATE".equals(operationType)) {
                    log.info("修改用户已有部门为新部门");
                    userDo.setDeptIds(deptMap.get(userDTO.getDeptName()).getId().toString());
                } else {
                    throw new BusinessException(BusinessCode.BAD_PARAMETER_CODE, "操作类型不支持：" + operationType);
                }
                userDo.setGmtModified(new Date());
                userDo.setUpdatedBy(0L);
                toUpdate.add(userDo);
            } else {
                log.info("新增用户");
                userDo.setGmtCreate(new Date());
                userDo.setGmtModified(new Date());
                userDo.setDeptIds(deptMap.get(userDTO.getDeptName()).getId().toString());
                userDo.setUpdatedBy(0L);
                userDo.setCreatedBy(0L);
                toInsert.add(userDo);
            }
        }
        // 批量操作
        batchOperate(toInsert, toUpdate);
    }

    private String getFxiaokeUserId(String mobile,String realName) {
        FxiaokeUserQueryResponse userByMobile = fXiaoKeManager.getUserByMobile(mobile);
        if (userByMobile!= null && userByMobile.getEmpList()!= null && !userByMobile.getEmpList().isEmpty()) {
            FxiaokeUserQueryResponse.Employee employee = userByMobile.getEmpList().get(0);
            log.info("查询纷享销客返回结果employee:{}",JSON.toJSONString(employee));
            String name = employee.getName();
            if(StringUtils.isNotEmpty(name) && name.equals(realName)) {
                return employee.getOpenUserId();
            }
        }
        return null;
    }

    private void batchOperate(List<TrainUserDO> toInsert, List<TrainUserDO> toUpdate) {
        if (!toInsert.isEmpty()) {
            for (TrainUserDO trainUserDO : toInsert) {
                trainUserDOMapper.insertSelective(trainUserDO);
            }
            log.info("新增用户数量:{}", toInsert.size());
        }
        if (!toUpdate.isEmpty()) {
            toUpdate.forEach(trainUserDOMapper::updateByPrimaryKeySelective);
            log.info("更新用户数量:{}", toUpdate.size());
        }
    }

    /**
     * 定时任务同步用户在职状态及个人信息
     */
    @Override
    @Transactional
    public void syncUserStatusAndInfo() {
        log.info("定时任务同步用户在职状态及个人信息开始");
        // 查询数据库全量在职用户
        List<TrainUserDO> existUsers = trainUserDOMapper.selectValidUsers();
        if(CollectionUtils.isEmpty(existUsers)){
            log.info("数据库无有效用户，不执行任何操作");
            return;
        }
        List<TrainUserDO> validUsers = getValidUsers(existUsers);
        if(CollectionUtils.isEmpty(validUsers)){
            log.info("数据库无有效用户，不执行任何操作");
            return;
        }
        List<String> mobileList = validUsers.stream().map(TrainUserDO::getPhone).collect(Collectors.toList());
        List<TrainUserDO> toInsert = new ArrayList<>();
        List<TrainUserDO> toUpdate = new ArrayList<>();
        // 查询用户钉钉信息
        List<DingdingUserVO> dingdingUsers = dingdingHandler.getUserInfoByMobiles(mobileList);
        Map<String, DingdingUserVO> dingdingUserMap = dingdingUsers.stream().collect(Collectors.toMap(DingdingUserVO::getMobile, Function.identity()));
        // 处理离职用户（从钉钉查询不到的用户）
        for (TrainUserDO user : validUsers) {
            DingdingUserVO dingdingUser = dingdingUserMap.get(user.getPhone());
            if (dingdingUser == null) {
                log.info("用户:{}已离职，更新用户在职状态",user.getUserName());
                user.setUserStatus(1);
                user.setGmtModified(new Date());
                toUpdate.add(user);
                log.info("用户:{}已离职，更新用户在职状态", user.getUserName());
            } else {
                // 用户在职，更新用户信息
                user.setUserStatus(0); // 假设0为在职状态，根据实际情况调整
                user.setGmtModified(new Date());
                user.setEmail(dingdingUser.getEmail());
                user.setDingTalkUserId(dingdingUser.getUserId());
                user.setWorkPlace(dingdingUser.getWorkPlace());
                user.setUserPosition(dingdingUser.getUserPosition());
                toUpdate.add(user);
                log.info("用户:{}信息已更新", user.getUserName());
            }
        }
        // 7. 执行数据库操作
        batchOperate(toInsert, toUpdate);
        log.info("同步完成，总处理用户：新增 {} / 更新 {}", toInsert.size(), toUpdate.size());
    }

    /**
     * 根据用户手机号查询userId(查询不到则返回最小userId-1)
     *
     * @param mobile
     */
    @Override
    public Long queryUserIdByMobile(String mobile) {
        if (StringUtils.isNotBlank(mobile)) {
            UserPassportDO userPassportDO = this.getUserCenterUserDTOList(mobile, null);
            if (userPassportDO != null && userPassportDO.getId() != null) {
                return userPassportDO.getId();
            }
        }
       Long minUserId = trainUserDOMapper.selectMinUserId();
       return minUserId == null ? -1L : minUserId - 1;
    }

    /**
     * 同步更新所有之前没有在CRM注册过的用户
     */
    @Override
    public void syncUserNotExistsInCrm() {
        log.info("同步更新所有之前没有在CRM注册过的用户开始");
        // 查询数据库全量用户
        List<TrainUserDO> existUsers = trainUserDOMapper.selectValidUsers();
        if(CollectionUtils.isEmpty(existUsers)){
            log.info("查询不到用户信息");
            return;
        }
        // 过滤出crmId=-1的
        List<TrainUserDO> notExistsInCrm = existUsers.stream().filter(user -> user.getCrmUserId() == null || user.getCrmUserId().equals("-1")).collect(Collectors.toList());
        // 获取纷享销客数据
        FxiaokeUserQueryResponse allUser = fXiaoKeManager.getAllUser();
        List<FxiaokeUserQueryResponse.Employee> empList = allUser.getEmployees();
        Map<String,String> mobile2IdMap = new HashMap<>();
        Map<String,String> realName2IdMap = new HashMap<>();
        if(CollectionUtils.isNotEmpty(empList)){
            mobile2IdMap = empList.stream().collect(Collectors.toMap(
                    FxiaokeUserQueryResponse.Employee::getMobile,
                    FxiaokeUserQueryResponse.Employee::getOpenUserId,
                    (existing, replacement) -> existing
            ));

            realName2IdMap = empList.stream().collect(Collectors.toMap(
                    FxiaokeUserQueryResponse.Employee::getName,
                    FxiaokeUserQueryResponse.Employee::getOpenUserId,
                    (existing, replacement) -> existing // 取第一个遇到的值
            ));
        }
        if(!CollectionUtils.isEmpty(notExistsInCrm)){
            log.info("待同步用户数量:{}", notExistsInCrm.size());
            for (TrainUserDO trainUserDO : notExistsInCrm) {
                String userIdFist = mobile2IdMap.get(trainUserDO.getPhone());
                String userIdSecond = realName2IdMap.getOrDefault(trainUserDO.getUserName(), "-1");
                trainUserDO.setCrmUserId(StringUtils.isEmpty(userIdFist) ? userIdSecond : userIdFist);
                trainUserDOMapper.updateByPrimaryKeySelective(trainUserDO);
            }
        }
    }

    private @NotNull List<TrainUserDO> getValidUsers(List<TrainUserDO> existUsers) {
        // 剔除隐藏部门用户
        // 获取所有隐藏部门ID
        List<TrainDeptDO> trainDeptDOS = trainDeptDOMapper.selectAllDepts();
        List<Integer> hiddenDeptIds = trainDeptDOS.stream()
                .filter(dept -> dept.getHidden() == 1)
                .map(TrainDeptDO::getId)
                .collect(Collectors.toList());
        // 过滤掉“所属部门只有一个且为隐藏部门”的用户
        List<TrainUserDO> resultList = new ArrayList<>();
        for (TrainUserDO trainUserDO : existUsers) {
            List<Integer> userDeptIds = Arrays.stream(trainUserDO.getDeptIds().split(";")).map(Integer::parseInt).collect(Collectors.toList());
            // 判断是否满足剔除条件： 用户只有一个部门且为隐藏部门
            boolean shouldExclude = hiddenDeptIds.contains(userDeptIds.get(0));
            if (!shouldExclude) {
                resultList.add(trainUserDO);
            }
        }
        return resultList;
    }


    private UserPassportDO getUserCenterUserDTOList(String mobile, String realName) {
        log.info("获取用户中心用户信息入参：mobile:{},realName:{}",mobile,realName);
        SelectUserRequest userSearchParam = new SelectUserRequest();
        List<String> mobileList = Collections.singletonList(mobile);
        userSearchParam.setClientId("sanren-teacher-pc");
        userSearchParam.setMobiles(mobileList);
        List<UserPassportDO> authCenterUserDTOList = new ArrayList<>();
        try {
            log.info("从用户中心获取用户信息入参：mobile:{},realName:{},param:{}",mobile,realName, JSON.toJSONString(userSearchParam));
            authCenterUserDTOList = authCenterFeignClient.selectUserByMobiles(userSearchParam);
        } catch (Exception e) {
            log.error("从用户中心获取用户信息失败, userSearchParam:{},userCenterUserDTOList:{}", userSearchParam, authCenterUserDTOList, e);
        }
        if (CollectionUtils.isNotEmpty(authCenterUserDTOList) && authCenterUserDTOList.get(0) != null) {
            return authCenterUserDTOList.get(0);
        }else {
            log.error("从用户中心获取用户为空, userSearchParam:{},userCenterUserDTOList:{}", userSearchParam, authCenterUserDTOList);
        }
       return new UserPassportDO();
    }




    private String mergeDeptIds(String deptIds1, List<Integer> deptIds) {
        if (deptIds1 == null) {
            deptIds1 = "";
        }
        if (deptIds == null) {
            deptIds = new ArrayList<>();
        }
        List<String> existingDeptIds = deptIds1.isEmpty() ? new ArrayList<>() : new ArrayList<>(Arrays.asList(deptIds1.split(";")));
        List<String> newDeptIds = deptIds.stream().filter(Objects::nonNull).map(String::valueOf).collect(Collectors.toList());
        // 合并两个列表并去重
        List<String> mergedList = new ArrayList<>(existingDeptIds);
        for (String id : newDeptIds) {
            if (!mergedList.contains(id)) {
                mergedList.add(id);
            }
        }
        // 将合并后的列表用分号连接成字符串（处理空列表情况）
        return mergedList.isEmpty() ? "" : String.join(";", mergedList);
    }
}
