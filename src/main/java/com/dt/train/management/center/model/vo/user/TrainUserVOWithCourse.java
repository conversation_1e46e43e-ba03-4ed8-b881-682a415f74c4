package com.dt.train.management.center.model.vo.user;

import java.util.List;

import com.dt.train.management.center.model.vo.project.TrainCourseUserRelationVOWithCourse;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class TrainUserVOWithCourse extends TrainUserVO {

    @ApiModelProperty(value = "班次列表")
    private List<TrainCourseUserRelationVOWithCourse> relationList;
}
