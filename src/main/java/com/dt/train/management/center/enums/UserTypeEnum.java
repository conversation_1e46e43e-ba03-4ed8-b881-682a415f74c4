package com.dt.train.management.center.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 用户类型枚举
 */
@Getter
@AllArgsConstructor
public enum UserTypeEnum {
    
    /**
     * 正式员工
     */
    FULL_TIME(0, "正式员工"),
    
    /**
     * 兼职人员
     */
    PART_TIME(1, "兼职人员");
    
    private final Integer code;
    private final String desc;
    
    /**
     * 根据code获取枚举
     */
    public static UserTypeEnum getByCode(Integer code) {
        if (code == null) {
            return FULL_TIME; // 默认返回正式员工
        }
        for (UserTypeEnum userType : values()) {
            if (userType.getCode().equals(code)) {
                return userType;
            }
        }
        return FULL_TIME; // 默认返回正式员工
    }
    
    /**
     * 根据描述获取枚举
     */
    public static UserTypeEnum getByDesc(String desc) {
        if (desc == null || desc.trim().isEmpty()) {
            return FULL_TIME; // 默认返回正式员工
        }
        for (UserTypeEnum userType : values()) {
            if (userType.getDesc().equals(desc.trim())) {
                return userType;
            }
        }
        return FULL_TIME; // 默认返回正式员工
    }
}
