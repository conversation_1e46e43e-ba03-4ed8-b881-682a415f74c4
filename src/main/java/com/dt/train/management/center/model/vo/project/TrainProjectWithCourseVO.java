package com.dt.train.management.center.model.vo.project;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Optional;
import java.util.Set;

import com.dt.train.management.center.model.common.HalfDayTime;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
@Data
@EqualsAndHashCode(callSuper = true)
public class TrainProjectWithCourseVO extends TrainProjectVO{

    @ApiModelProperty(value = "已完成班次数")
    private Long finishedCourseCount;

    @ApiModelProperty(value = "班次数量")
    private Integer courseCount;

    @ApiModelProperty(value = "排班人天")
    private Float planUserDays;

    @ApiModelProperty(value = "班次排期天数")
    private Float totalDays;

    @ApiModelProperty(value = "已经过天数")
    private Float passedDays;

    @ApiModelProperty(value = "班次列表")
    private List<TrainCourseVO> courses;

    public Float getPlanUserDays() {
        return Optional.ofNullable(courses).orElse(new ArrayList<>()).stream().map(TrainCourseVO::getPlanUserDays).reduce(0f, Float::sum);
    }

    @ApiModelProperty(value = "排班人数")
    public Integer getScheduledPersonCount() {
        Set<Long> userIds = new HashSet<>();
        if (courses != null) {
            for (TrainCourseVO course : courses) {
                if (course.getExecuteUsers() != null) {
                    course.getExecuteUsers().forEach(user -> userIds.add(user.getUserId()));
                }
                if (course.getSupportUsers() != null) {
                    course.getSupportUsers().forEach(user -> userIds.add(user.getUserId()));
                }
            }
        }
        return userIds.size();
    }

    /**
     * 项目排班天数：
     * 项目下所有班次实际执行日期去重后的天数
     *
     * @return
     */
    public Float getTotalDays() {
        if (this.getCourses() == null || this.getCourses().isEmpty()) {
            return 0F;
        }

        Set<HalfDayTime> allHalfDays = new HashSet<>();
        for (TrainCourseVO course : this.getCourses()) {
            if (course.getRealStartDate() != null && course.getRealEndDate() != null &&
                course.getRealStartDate().getDate() != null && course.getRealEndDate().getDate() != null &&
                course.getRealStartDate().isBeforeEqual(course.getRealEndDate())) {

                HalfDayTime current = course.getRealStartDate();
                while (current.isBeforeEqual(course.getRealEndDate())) {
                    allHalfDays.add(current);
                    current = current.next();
                }
            }
        }

        return allHalfDays.stream().distinct().count()/2F;
    }
}
