package com.dt.train.management.center.model.dto.project;

import java.util.Date;
import java.util.List;

import lombok.Data;

/**
 * 
 * 表名: train_course_user_relation
 *
 * @mbg.generated
 */
@Data
public class CourseUserRelationQueryDTO {
   

    /**
     * 字段描述: 项目ID
     *
     * 字段名: train_course_user_relation.project_id
     *
     * @mbg.generated
     */
    private Integer projectId;

    /**
     * 字段描述: 班次ID
     *
     * 字段名: train_course_user_relation.course_id
     *
     * @mbg.generated
     */
    private Integer courseId;

    /**
     * 字段描述: 用户ID
     *
     * 字段名: train_course_user_relation.user_id
     *
     * @mbg.generated
     */
    private Long userId;

    private List<Long> userIds;

    /**
     * 字段描述: 开始时间
     *
     * 字段名: train_course_user_relation.start_date
     *
     * @mbg.generated
     */
    private Date startDate;

    /**
     * 字段描述: 结束时间
     *
     * 字段名: train_course_user_relation.end_date
     *
     * @mbg.generated
     */
    private Date endDate;
}