package com.dt.train.management.center.model.dto.user;

import com.dt.framework.business.util.page.PaginationParams;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class TrainUserQueryDTO implements PaginationParams {

    @ApiModelProperty(value = "页码")
    private Integer pageIndex = 1;

    @ApiModelProperty(value = "每页条数")
    private Integer pageSize = 10;

    @ApiModelProperty(value = "（用户中心）用户ID")
    private Long userId;

    @ApiModelProperty(value = "钉钉用户ID")
    private Integer dingTalkUserId;

    @ApiModelProperty(value = "CRM系统用户ID")
    private String crmUserId;

    @ApiModelProperty(value = "部门id")
    private Integer deptId;

    @ApiModelProperty(value = "部门id")
    private List<Integer> deptIds;

    @ApiModelProperty(value = "管理部门id列表")
    private List<Integer> manageDeptIds;

    @ApiModelProperty(value = "用户姓名（模糊查询）")
    private String userName;

    @ApiModelProperty(value = "用户姓名（精确查询）")
    private String  userNameExact;

    @ApiModelProperty(value = "电子邮箱")
    private String email;

    @ApiModelProperty(value = "手机号码")
    private String phone;

    @ApiModelProperty(value = "职位")
    private String userPosition;

    @ApiModelProperty(value = "角色id")
    private Integer roleId;

    @ApiModelProperty(value = "办公地点")
    private String workPlace;

    @ApiModelProperty(value = "用户状态 0-在职 1-离职")
    private Integer userStatus;

    @ApiModelProperty(value = "成员类型 0-正式员工 1-兼职人员")
    private Integer userType;

    @ApiModelProperty(value = "查询范围 0-本组织及下属组织 1-仅本组织")
    private Integer queryScope = 0;

}
