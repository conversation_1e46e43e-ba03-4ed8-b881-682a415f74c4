package com.dt.train.management.center.model.dto.log;

import com.dt.framework.business.util.page.PaginationParams;

/**
 * 
 * 表名: common_log
 *
 * @mbg.generated
 */
@lombok.Data
public class CommonLogQueryDTO implements PaginationParams {


    /**
     * 字段描述: 日志类型
     *
     * 字段名: common_log.log_type
     *
     * @mbg.generated
     */
    private String logType;

    /**
     * 字段描述: 日志二级类型
     *
     * 字段名: common_log.log_second_type
     *
     * @mbg.generated
     */
    private String logSecondType;

    /**
     * 字段描述: ip地址
     *
     * 字段名: common_log.ip
     *
     * @mbg.generated
     */
    private String ip;

    /**
     * 字段描述: 用户id
     *
     * 字段名: common_log.user_id
     *
     * @mbg.generated
     */
    private String userId;

    /**
     * 字段描述: 用户名称
     *
     * 字段名: common_log.user_name
     *
     * @mbg.generated
     */
    private String userName;

    /**
     * 字段描述: 业务id
     *
     * 字段名: common_log.business_id
     *
     * @mbg.generated
     */
    private String businessId;

    /**
     * 字段描述: 业务类型
     *
     * 字段名: common_log.business_type
     *
     * @mbg.generated
     */
    private String businessType;

    private Integer pageIndex = 0;

    private Integer pageSize = 10;
}