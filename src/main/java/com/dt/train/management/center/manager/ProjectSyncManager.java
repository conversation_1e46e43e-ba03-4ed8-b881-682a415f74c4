package com.dt.train.management.center.manager;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.UUID;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.function.Function;
import java.util.stream.Collectors;

import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.dt.train.management.center.config.FXiaoKeBusinessConfig;
import com.dt.train.management.center.constant.CacheConstant;
import com.dt.train.management.center.constant.ConfigConstant;
import com.dt.train.management.center.enums.ManagementExceptionEnum;
import com.dt.train.management.center.enums.ProjectActionType;
import com.dt.train.management.center.model.dto.config.SmartConfigDTO;
import com.dt.train.management.center.model.dto.project.TrainProjectCRMDTO;
import com.dt.train.management.center.model.dto.project.TrainProjectCRMStartDTO;
import com.dt.train.management.center.model.dto.project.TrainProjectDTO;
import com.dt.train.management.center.model.dto.region.RegionInfoDTO;
import com.dt.train.management.center.model.exception.ManagementBusinessException;
import com.dt.train.management.center.model.request.fxiaoke.FxiaokeDataQueryFilter;
import com.dt.train.management.center.model.response.fxiaoke.FxiaokeDataObjectQueryResponse;
import com.dt.train.management.center.model.vo.dept.TrainDeptVO;
import com.dt.train.management.center.model.vo.dict.DictMappingVO;
import com.dt.train.management.center.model.vo.project.TrainProjectVO;
import com.dt.train.management.center.model.vo.user.TrainUserVO;
import com.dt.train.management.center.service.cache.DeptCacheService;
import com.dt.train.management.center.service.config.SmartConfigService;
import com.dt.train.management.center.service.dept.TrainDeptService;
import com.dt.train.management.center.service.dict.DictMappingService;
import com.dt.train.management.center.service.project.TrainProjectService;
import com.dt.train.management.center.service.region.RegionService;
import com.dt.train.management.center.service.user.TrainUserService;
import com.dt.train.management.center.service.user.UserAndDepartmentMaintenanceService;
import com.dt.train.management.center.utils.CRMProjectFieldMappingUtil;
import com.dt.train.management.center.utils.DeptUtils;
import com.dt.train.management.center.utils.ExceptionUtil;
import com.dt.train.management.center.utils.RedisUtil;

import lombok.extern.slf4j.Slf4j;

@Component
@Slf4j
public class ProjectSyncManager {

    private final FXiaoKeManager fXiaoKeManager;

    private final CRMProjectFieldMappingUtil fieldMappingUtil;

    private final TrainProjectService trainProjectService;

    private final DictMappingService dictMappingService;

    private final FXiaoKeBusinessConfig fXiaoKeBusinessConfig;

    private final SmartConfigService smartConfigService;

    private final SmartConfigManager smartConfigManager;

    private final RedisUtil redisUtil;

    private final ThreadPoolExecutor projectSyncExecutor;

    private final TrainDeptService trainDeptService;

    private final RegionService regionService;

    private final TrainUserService trainUserService;
    private final UserAndDepartmentMaintenanceService userAndDepartmentMaintenanceService;

    private final DeptCacheService deptCacheService;

    public ProjectSyncManager(FXiaoKeManager fXiaoKeManager, CRMProjectFieldMappingUtil fieldMappingUtil
    , TrainProjectService trainProjectService, DictMappingService dictMappingService
    , FXiaoKeBusinessConfig fXiaoKeBusinessConfig, SmartConfigService smartConfigService, SmartConfigManager smartConfigManager, RedisUtil redisUtil, ThreadPoolExecutor projectSyncExecutor, TrainDeptService trainDeptService, RegionService regionService, TrainUserService trainUserService, UserAndDepartmentMaintenanceService userAndDepartmentMaintenanceService, DeptCacheService deptCacheService) {
        this.fXiaoKeManager = fXiaoKeManager;
        this.fieldMappingUtil = fieldMappingUtil;
        this.trainProjectService = trainProjectService;
        this.dictMappingService = dictMappingService;
        this.fXiaoKeBusinessConfig = fXiaoKeBusinessConfig;
        this.smartConfigService = smartConfigService;
        this.smartConfigManager = smartConfigManager;
        this.redisUtil = redisUtil;
        this.projectSyncExecutor = projectSyncExecutor;
        this.trainDeptService = trainDeptService;
        this.regionService = regionService;
        this.trainUserService = trainUserService;
        this.userAndDepartmentMaintenanceService = userAndDepartmentMaintenanceService;
        this.deptCacheService = deptCacheService;
    }

    /**
     * 根据项目编码获取CRM项目数据
     * @param projectCode 项目编码
     * @return 项目数据列表
     */
    public List<TrainProjectVO> getCRMByProjectCode(String projectCode) {
        List<FxiaokeDataQueryFilter> filters = new ArrayList<>();
        FxiaokeDataQueryFilter filter = new FxiaokeDataQueryFilter();
        filter.setField_name("field_v0Dml__c");
        filter.setOperator("EQ");
        filter.setField_values(Collections.singletonList(projectCode));
        filters.add(filter);
        FxiaokeDataObjectQueryResponse response = fXiaoKeManager.getProjectList(10, 0, filters, null);
        List<Map<String, Object>> dataList = response.getData().getDataList();
        List<TrainProjectVO> trainProjectVOList = dataList.stream()
            .map(this::mapFromCrmProject)
            .map(this::crmDTO2VO)
            .collect(Collectors.toList());
        return trainProjectVOList;
    }

    /**
     * 增量同步项目数据
     * 
     * 有两部分来源：
     * 1. 满足筛选条件，且在上次同步时间之后有更新的项目
     * 2. 手动同步的项目，且在上次同步时间之后有更新的项目
     */
    public String incrementalSyncProject(String currentDeptId) {
        log.info("incrementalSyncProject start");
        Long lastSyncTime = getLastSyncCrmProjectTime();
        FxiaokeDataQueryFilter extraFilter = new FxiaokeDataQueryFilter();
        extraFilter.setField_name("last_modified_time");
        extraFilter.setOperator("GT");
        extraFilter.setField_values(Collections.singletonList(String.valueOf(lastSyncTime)));
        String syncRequestId = fullSyncProject(extraFilter, false, currentDeptId);
        log.info("incrementalSyncProject end, syncRequestId: {}", syncRequestId);
        return syncRequestId;
    }

    /**
     * 获取同步项目进度
     * @param syncRequestId 同步请求ID
     * @return 同步项目进度
     */
    public Integer getSyncCrmProjectProcess(String syncRequestId) {
        String process = redisUtil.get(CacheConstant.getSyncCrmProjectProcessKey(syncRequestId));
        return process == null ? 0 : Integer.parseInt(process);
    }

    /**
     * 全量同步项目数据
     * 
     * 1. 迭代获取纷享销客项目列表
     * 2. 将项目列表转换为TrainProjectDTO列表
     * 3. 将TrainProjectDTO列表保存到数据库, 如果数据库中已存在, 则更新, 否则插入
     */
    public String fullSyncProject(FxiaokeDataQueryFilter extraFilter, boolean withManual, String currentDeptId) {
        // 获取纷享销客项目列表
        String syncRequestId = UUID.randomUUID().toString();
        
        projectSyncExecutor.submit(() -> {
            try{
                userAndDepartmentMaintenanceService.syncUserNotExistsInCrm();
                syncProject(syncRequestId, extraFilter, withManual, currentDeptId);
            } catch (Exception e) {
                log.error("fullSyncProject error, syncRequestId: {}", syncRequestId, e);
            }
        });
        return syncRequestId;
    }

    private void syncProject(String syncRequestId, FxiaokeDataQueryFilter extraFilter, boolean withManual, String currentDeptId) {
        long lastSyncTime = System.currentTimeMillis();
        List<FxiaokeDataQueryFilter> filters = new ArrayList<>(fXiaoKeBusinessConfig.getFXiaoKeProjectFullSyncFilters());
        if(extraFilter != null){
            filters.add(extraFilter);
        }
        Integer total = 0;
        Long currentDeptTotal = 0L;
        FxiaokeDataObjectQueryResponse response = fXiaoKeManager.getProjectList(50, 0, filters, null);
        while (response != null && response.getData().getDataList().size() > 0) {
            List<Map<String, Object>> dataList = response.getData().getDataList();
            List<TrainProjectCRMDTO> trainProjectCRMDTOList = mapCRMResponse(dataList, true);
            total += trainProjectCRMDTOList.size();

            FxiaokeDataQueryFilter filterStart = new FxiaokeDataQueryFilter();
            filterStart.setField_name("Field1__c");
            filterStart.setOperator("IN");
            filterStart.setField_values(dataList.stream().map(data -> data.get("_id").toString()).collect(Collectors.toList()));
            FxiaokeDataObjectQueryResponse responseStart = fXiaoKeManager.getProjectStartList(50, 0, Collections.singletonList(filterStart), null);
            if (responseStart != null && responseStart.getData().getDataList().size() > 0) {
                List<Map<String, Object>> dataListStart = responseStart.getData().getDataList();
                Map<String, TrainProjectCRMStartDTO> trainProjectCRMDTOMap = dataListStart.stream().map(this::mapStartFromCrmProject).collect(Collectors.toMap(TrainProjectCRMStartDTO::getCrmProjectId, Function.identity(), (a, b) -> a));
                trainProjectCRMDTOList.forEach(trainProjectCRMDTO -> {
                    TrainProjectCRMStartDTO trainProjectCRMDTOStart = trainProjectCRMDTOMap.get(trainProjectCRMDTO.getCrmProjectId());
                    if(trainProjectCRMDTOStart != null){
                        BeanUtils.copyProperties(trainProjectCRMDTOStart, trainProjectCRMDTO);
                    }
                });
                
            }
            List<TrainProjectDTO> trainProjectDTOList = trainProjectCRMDTOList.stream().map(dto -> saveTrainProject(dto, false, currentDeptId)).collect(Collectors.toList());
            // 同步用户
            if(currentDeptId != null){
                currentDeptTotal += trainProjectDTOList.stream().filter(dto -> DeptUtils.isEqualOrSub(dto.getAreaId(), currentDeptId)).count();
            }

            redisUtil.set(CacheConstant.getSyncCrmProjectProcessKey(syncRequestId), String.valueOf(currentDeptTotal == null ? total : currentDeptTotal), 60 * 10);
            // 更新页码
            FxiaokeDataQueryFilter filter = new FxiaokeDataQueryFilter();
            filter.setField_name("_id");
            filter.setOperator("GT");
            filter.setField_values(Collections.singletonList(dataList.get(dataList.size() - 1).get("_id").toString()));
            List<FxiaokeDataQueryFilter> newFilters = new ArrayList<>(filters);
            newFilters.add(filter);
            response = fXiaoKeManager.getProjectList(50, 0, newFilters, null);
        }
        if(withManual){
            total += updateManualSyncProject(extraFilter);
        }
        // 记录最后同步时间
        SmartConfigDTO smartConfigDTO = new SmartConfigDTO();
        smartConfigDTO.setConfigKey(ConfigConstant.FXIAOKE_PROJECT_SYNC_LAST_SYNC_TIME_KEY);
        smartConfigDTO.setConfigValue(String.valueOf(lastSyncTime));
        smartConfigDTO.setConfigType(1);
        smartConfigService.saveOrUpdateByKey(smartConfigDTO);
        log.info("fullSyncProject, lastSyncTime: {}, total: {}, currentDeptTotal: {}", lastSyncTime, total, currentDeptTotal);
        redisUtil.set(CacheConstant.getSyncCrmProjectProcessKey(syncRequestId), "-1", 60 * 10);
    }

    /**
     * 校验项目编码有效性
     * @param projectCode 项目编码
     * @throws ManagementBusinessException 如果项目编码无效
     */
    private boolean checkProjectCode(String projectCode) {
        if(projectCode == null || projectCode.isEmpty()){
            return false;
        }
        List<FxiaokeDataQueryFilter> filters = fXiaoKeBusinessConfig.getFXiaoKeProjectSingleSyncFilters();
        List<FxiaokeDataQueryFilter> projectCodeFilters = filters.stream().filter(filter -> filter.getField_name().equals("field_v0Dml__c")).collect(Collectors.toList());
        for (FxiaokeDataQueryFilter projectCodeFilter : projectCodeFilters) {
            if(projectCodeFilter.getOperator().equals("ENDWITH")){
                List<String> fieldValues = (List<String>) projectCodeFilter.getField_values();
                for (String fieldValue : fieldValues) {
                    if(projectCode.endsWith(fieldValue)){
                        return true;
                    }
                }
            }
        }
        return false;
    }

    /**
     * 更新手动同步的项目
     * 
     */
    private Integer updateManualSyncProject(FxiaokeDataQueryFilter extraFilter) {
        List<FxiaokeDataQueryFilter> filters = new ArrayList<>();
        if(extraFilter != null){
            filters.add(extraFilter);
        }
        // 获取手动同步的项目
        List<TrainProjectDTO> trainProjectDTOList = trainProjectService.getManualSyncProject();
        if(trainProjectDTOList.size() == 0){
            return 0;
        }
        List<String> projectCodeList = trainProjectDTOList.stream().map(TrainProjectDTO::getProjectCode).collect(Collectors.toList());
        FxiaokeDataQueryFilter filter = new FxiaokeDataQueryFilter();
        filter.setField_name("field_v0Dml__c");
        filter.setOperator("IN");
        filter.setField_values(projectCodeList);
        filters.add(filter);
        FxiaokeDataObjectQueryResponse response = fXiaoKeManager.getProjectList(500, 0, filters, null);
        // 更新手动同步的项目
        List<TrainProjectCRMDTO> trainProjectCRMDTOList = mapCRMResponse(response.getData().getDataList(), false);
        // 将TrainProjectDTO列表保存到数据库
        trainProjectCRMDTOList.forEach(dto -> saveTrainProject(dto, false, null));
        return trainProjectCRMDTOList.size();
    }

    private List<TrainProjectCRMDTO> mapCRMResponse(List<Map<String, Object>> dataList, boolean chechCode) {
        List<TrainProjectCRMDTO> trainProjectDTOList = dataList.stream()
            .map(this::mapFromCrmProject)
            .filter(trainProjectCRMDTO -> trainProjectCRMDTO != null && (chechCode ? checkProjectCode(trainProjectCRMDTO.getProjectCode()) : true))
            .collect(Collectors.toList());
            
        log.info("trainProjectDTOList: {}", JSON.toJSONString(trainProjectDTOList));
        
        return trainProjectDTOList;
    }

    private TrainProjectDTO saveTrainProject(TrainProjectCRMDTO trainProjectCRMDTO, boolean checkDept, String currentDeptId) {
        log.info("saveTrainProject, projectCode: {}", trainProjectCRMDTO.getProjectCode());
        
        TrainProjectDTO trainProjectDTO = crmDTO2DTO(trainProjectCRMDTO);
        if(checkDept && currentDeptId != null){
            if(!DeptUtils.isEqualOrSub(Objects.toString(trainProjectDTO.getProjectAreaId(), null), currentDeptId)){
                throw ExceptionUtil.businessExceptionWithArg(ManagementExceptionEnum.PROJECT_NOT_IN_CURRENT_DEPT, ""
                , deptCacheService.getDeptName(currentDeptId));
            }
        }
        trainProjectService.saveOrUpdateTrainProjectByCrmId(trainProjectDTO);
        return trainProjectDTO;
    }

    private TrainProjectDTO crmDTO2DTO(TrainProjectCRMDTO trainProjectCRMDTO) {
        TrainProjectDTO trainProjectDTO = new TrainProjectDTO();
        BeanUtils.copyProperties(trainProjectCRMDTO, trainProjectDTO);
        DictMappingVO projectType = dictMappingService.getByTypeAndThirdDictCode("project_type", trainProjectCRMDTO.getProjectType());
        trainProjectDTO.setProjectType(projectType == null ? null : Integer.parseInt(projectType.getDictCode()));

        DictMappingVO projectLevel = dictMappingService.getByTypeAndThirdDictCode("project_level", trainProjectCRMDTO.getProjectLevel());
        trainProjectDTO.setProjectLevel(projectLevel == null ? null : Integer.parseInt(projectLevel.getDictCode()));
        trainProjectDTO.setStartDate(trainProjectCRMDTO.getStartDate() == null ? null : new Date(trainProjectCRMDTO.getStartDate()));
        trainProjectDTO.setEndDate(trainProjectCRMDTO.getEndDate() == null ? null : new Date(trainProjectCRMDTO.getEndDate()));

        RegionInfoDTO regionInfoDTO = regionService.getRegionInfoByCrmIdAndType("省", trainProjectCRMDTO.getProvinceId());
        if(regionInfoDTO != null){
            trainProjectDTO.setProvince(regionInfoDTO.getName());
            trainProjectDTO.setProvinceId(regionInfoDTO.getUserCenterId());
        }
        regionInfoDTO = regionService.getRegionInfoByCrmIdAndType("市", trainProjectCRMDTO.getCityId());
        if(regionInfoDTO != null){
            trainProjectDTO.setCity(regionInfoDTO.getName());
            trainProjectDTO.setCityId(regionInfoDTO.getUserCenterId());
        }
        regionInfoDTO = regionService.getRegionInfoByCrmIdAndType("区", trainProjectCRMDTO.getAreaId());
        if(regionInfoDTO != null){
            trainProjectDTO.setArea(regionInfoDTO.getName());
            trainProjectDTO.setAreaId(regionInfoDTO.getUserCenterId());
        }
        
        TrainDeptVO trainDeptVO = trainDeptService.getDeptByCrmOrgIdAndCrmProvinceId(trainProjectCRMDTO.getProjectAreaId(), trainProjectCRMDTO.getProvinceId());
        if(trainDeptVO != null && trainDeptVO.getDeptLevel() != null){
            String deptName = trainDeptVO.getDeptNameShort();
            if(trainDeptVO.getDeptLevel() == 3){
                deptName = trainDeptVO.getParentDeptNameShort() + "-" + trainDeptVO.getDeptNameShort();
            }
            trainProjectDTO.setProjectArea(deptName);
            trainProjectDTO.setProjectAreaId(trainDeptVO.getId());
        }

        trainProjectDTO.setIsRemote(trainProjectCRMDTO.getIsRemote() == null ? null : trainProjectCRMDTO.getIsRemote().equals("是") ? 1 : 0);

        TrainUserVO projectManager = trainUserService.getUserInfoByCrmUserId(trainProjectCRMDTO.getProjectManagerId());
        TrainUserVO projectEditor = trainUserService.getUserInfoByCrmUserId(trainProjectCRMDTO.getProjectEditorId());
        if(projectManager != null){
            trainProjectDTO.setProjectManagerId(projectManager.getFirstDeptId() + "" + projectManager.getUserId());
            trainProjectDTO.setProjectManagerName(projectManager.getUserName());
        }else{
            trainProjectDTO.setProjectManagerId("");
            log.info("projectManager not found, projectManagerId: {}", trainProjectCRMDTO.getProjectManagerId());
        }
        if(projectEditor != null){
            trainProjectDTO.setProjectEditorId(projectEditor.getFirstDeptId() + "" + projectEditor.getUserId());
            trainProjectDTO.setProjectEditorName(projectEditor.getUserName());
        }else{
            trainProjectDTO.setProjectEditorId("");
            log.info("projectEditor not found, projectEditorId: {}", trainProjectCRMDTO.getProjectEditorId());
        }

        ProjectActionType projectActionType = ProjectActionType.getByThirdDictCode(trainProjectCRMDTO.getActionType());
        trainProjectDTO.setActionType(projectActionType == null ? null : projectActionType.getCode());

        if(trainProjectCRMDTO.getLiveEditorId() != null && trainProjectCRMDTO.getLiveEditorName() != null){
            List<TrainUserVO> liveEditorList = trainProjectCRMDTO.getLiveEditorId().stream().map(trainUserService::getUserInfoByCrmUserId).filter(Objects::nonNull).collect(Collectors.toList());
            trainProjectDTO.setLiveEditorId(String.join(";", liveEditorList.stream().map(editor -> editor.getFirstDeptId() + "" + editor.getUserId()).collect(Collectors.toList())));
            trainProjectDTO.setLiveEditorName(String.join(";", trainProjectCRMDTO.getLiveEditorName()));
        }

        if(trainProjectCRMDTO.getProjectHelperId() != null && trainProjectCRMDTO.getProjectHelperName() != null){
            List<TrainUserVO> projectHelperList = trainProjectCRMDTO.getProjectHelperId().stream().map(trainUserService::getUserInfoByCrmUserId).filter(Objects::nonNull).collect(Collectors.toList());
            trainProjectDTO.setProjectHelperId(String.join(";", projectHelperList.stream().map(editor -> editor.getFirstDeptId() + "" + editor.getUserId()).collect(Collectors.toList())));
            trainProjectDTO.setProjectHelperName(String.join(";", trainProjectCRMDTO.getProjectHelperName()));
        }

        trainProjectDTO.setSyncTime(new Date());
        
        return trainProjectDTO;
    }

    private TrainProjectVO crmDTO2VO(TrainProjectCRMDTO trainProjectCRMDTO) {
        TrainProjectVO trainProjectVO = new TrainProjectVO();
        BeanUtils.copyProperties(trainProjectCRMDTO, trainProjectVO);
        DictMappingVO projectType = dictMappingService.getByTypeAndThirdDictCode("project_type", trainProjectCRMDTO.getProjectType());
        trainProjectVO.setProjectType(projectType == null ? null : Integer.parseInt(projectType.getDictCode()));
        trainProjectVO.setProjectTypeDesc(projectType == null ? null : projectType.getDictValue());

        DictMappingVO projectLevel = dictMappingService.getByTypeAndThirdDictCode("project_level", trainProjectCRMDTO.getProjectLevel());
        trainProjectVO.setProjectLevel(projectLevel == null ? null : Integer.parseInt(projectLevel.getDictCode()));
        trainProjectVO.setProjectLevelDesc(projectLevel == null ? null : projectLevel.getDictValue());
        trainProjectVO.setStartDate(trainProjectCRMDTO.getStartDate() == null ? null : new Date(trainProjectCRMDTO.getStartDate()));
        trainProjectVO.setEndDate(trainProjectCRMDTO.getEndDate() == null ? null : new Date(trainProjectCRMDTO.getEndDate()));
        return trainProjectVO;
    }


    /**
     * 将CRM项目数据映射为TrainProjectDTO
     * @param crmProjectData CRM项目数据
     * @return 映射后的TrainProjectDTO对象
     */
    private TrainProjectCRMDTO mapFromCrmProject(Map<String, Object> crmProjectData) {
        if (crmProjectData == null) {
            return null;
        }
        
        TrainProjectCRMDTO trainProjectDTO = new TrainProjectCRMDTO();
        fieldMappingUtil.mapToTargetProject(crmProjectData, trainProjectDTO);
        return trainProjectDTO;
    }

    /**
     * 将CRM项目数据映射为TrainProjectDTO
     * @param crmProjectData CRM项目数据
     * @return 映射后的TrainProjectDTO对象
     */
    private TrainProjectCRMStartDTO mapStartFromCrmProject(Map<String, Object> crmProjectData) {
        if (crmProjectData == null) {
            return null;
        }
        
        TrainProjectCRMStartDTO trainProjectDTO = new TrainProjectCRMStartDTO();
        fieldMappingUtil.mapToTargetStart(crmProjectData, trainProjectDTO);
        return trainProjectDTO;
    }

    /**
     * 校验项目编码有效性
     * @param projectCode 项目编码
     * @throws ManagementBusinessException 如果项目编码无效
     */
    @SuppressWarnings("all")
    private void precheckProjectCode(String projectCode) {
        List<FxiaokeDataQueryFilter> filters = fXiaoKeBusinessConfig.getFXiaoKeProjectSingleSyncFilters();
        List<FxiaokeDataQueryFilter> projectCodeFilters = filters.stream().filter(filter -> filter.getField_name().equals("field_v0Dml__c")).collect(Collectors.toList());
        for (FxiaokeDataQueryFilter projectCodeFilter : projectCodeFilters) {
            if(projectCodeFilter.getOperator().equals("ENDWITH")){
                List<String> fieldValues = (List<String>) projectCodeFilter.getField_values();
                for (String fieldValue : fieldValues) {
                    if(projectCode.endsWith(fieldValue)){
                        return;
                    }
                }
                throw ExceptionUtil.businessException(ManagementExceptionEnum.PROJECT_NOT_SYNC_RULE);
            }
        }
        return;
    }

    /**
     * 同步单个项目
     * 
     * 1. 根据项目编码获取CRM项目数据
     * 2. 将CRM项目数据转换为TrainProjectCRMDTO
     * 3. 保存TrainProjectCRMDTO到数据库
     * 
     * @param projectCode 项目编码
     */
    public void syncSingleProject(String projectCode, String currentDeptId) {
        // precheckProjectCode(projectCode);
        userAndDepartmentMaintenanceService.syncUserNotExistsInCrm();
        List<FxiaokeDataQueryFilter> filters = new ArrayList<>();
        FxiaokeDataQueryFilter filter = new FxiaokeDataQueryFilter();
        filter.setField_name("field_v0Dml__c");
        filter.setOperator("EQ");
        filter.setField_values(Collections.singletonList(projectCode));
        filters.add(filter);
        FxiaokeDataObjectQueryResponse response = fXiaoKeManager.getProjectList(10, 0, filters, null);
        List<Map<String, Object>> dataList = response.getData().getDataList();
        List<TrainProjectCRMDTO> trainProjectCRMDTOList = dataList.stream()
            .map(this::mapFromCrmProject)
            .collect(Collectors.toList());
        if (trainProjectCRMDTOList.size() == 0) {
            throw ExceptionUtil.businessException(ManagementExceptionEnum.PROJECT_NOT_SYNC_RULE);
        }
        TrainProjectCRMDTO trainProjectCRMDTO = trainProjectCRMDTOList.get(0);
        trainProjectCRMDTO.setSyncType(1);

        FxiaokeDataQueryFilter filterStart = new FxiaokeDataQueryFilter();
        filterStart.setField_name("Field1__c");
        filterStart.setOperator("EQ");
        filterStart.setField_values(Collections.singletonList(trainProjectCRMDTO.getCrmProjectId()));
        FxiaokeDataObjectQueryResponse responseStart = fXiaoKeManager.getProjectStartList(10, 0, Collections.singletonList(filterStart), null);
        List<Map<String, Object>> dataListStart = responseStart.getData().getDataList();
        if(dataListStart.size() > 0){
            TrainProjectCRMStartDTO trainProjectCRMDTOStart = mapStartFromCrmProject(dataListStart.get(0));
            BeanUtils.copyProperties(trainProjectCRMDTOStart, trainProjectCRMDTO);
        }
        saveTrainProject(trainProjectCRMDTO, true, currentDeptId);
    }

    /**
     * 处理项目状态更新
     */
    public void processStatus() {
        trainProjectService.processStatus();
    }

    /**
     * 获取最后同步CRM项目的时间
     * @return 最后同步CRM项目的时间
     */
    public Long getLastSyncCrmProjectTime() {
        Long lastSyncTime = smartConfigManager.getSingleConfigValue(ConfigConstant.FXIAOKE_PROJECT_SYNC_LAST_SYNC_TIME_KEY, null, new TypeReference<Long>(){});
        return lastSyncTime;
    }

    /**
     * 逻辑删除项目
     * @param id 项目ID
     * @throws ManagementBusinessException 如果项目以MHZ结尾或项目下存在班次
     */
    public void deleteProjectById(Integer id) {
        // 获取完整项目信息
        TrainProjectVO project = trainProjectService.getProjectById(id);
        if (project == null) {
            throw ExceptionUtil.businessException(ManagementExceptionEnum.PROJECT_NOT_EXIST);
        }
        
        // 检查MHZ后缀
        if (project.getProjectCode().toUpperCase().endsWith("MHZ")) {
            throw ExceptionUtil.businessException(ManagementExceptionEnum.PROJECT_SUFFIX_MHZ_CANNOT_DELETE);
        }
        
        // 检查项目下是否有班次
        if (project.getScheduleStatus() == 1) {
            throw ExceptionUtil.businessException(ManagementExceptionEnum.PROJECT_HAS_COURSES_CANNOT_DELETE);
        }
        
        // 执行逻辑删除 (设置invalid=1)
        trainProjectService.logicDeleteProject(id);
    }
}
