package com.dt.train.management.center.mapper;

import java.util.List;

import com.dt.train.management.center.model.dao.CommonLogDO;
import com.dt.train.management.center.model.dto.log.CommonLogQueryDTO;

public interface CommonLogDOMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(CommonLogDO row);

    int insertSelective(CommonLogDO row);

    CommonLogDO selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(CommonLogDO row);

    int updateByPrimaryKeyWithBLOBs(CommonLogDO row);

    int updateByPrimaryKey(CommonLogDO row);

    List<CommonLogDO> selectByQuery(CommonLogQueryDTO commonLogQueryDTO);
}