package com.dt.train.management.center.config;

import java.util.List;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;
import org.springframework.validation.annotation.Validated;

import com.ctrip.framework.apollo.spring.annotation.ApolloJsonValue;
import com.dt.train.management.center.model.request.fxiaoke.FxiaokeDataQueryFilter;

import lombok.Data;
import lombok.ToString;

@Data
@Configuration
@Validated
@RefreshScope
@ToString
public class FXiaoKeBusinessConfig {

    @Value("${fxiaoke.appId:FSAID_131fd1c}")
    private String fXiaoKeAppId;

    @Value("${fxiaoke.appSecret:f699e00215e641ef8e52be9af3cb01b0}")
    private String fXiaoKeAppSecret;

    @Value("${fxiaoke.permanentCode:2C00A38C347BE62ACCB06E1BF37AFB9A}")
    private String fXiaoKePermanentCode;

    @Value("${fxiaoke.base.url:https://open.fxiaoke.com}")
    private String fXiaoKeBaseUrl;

    @Value("${fxiaoke.admin.open.user.id:FSUID_8A431B25D451953D8D42371B5435D957}")
    private String fXiaoKeAdminOpenUserId;

    @Value("${fxiaoke.corp.id:FSCID_77B0F5AC06EFEB5D839D784E5804FE72}")
    private String fXiaoKeCorpId;

    @Value("${fxiaoke.data.project.id:Account__c}")
    private String fXiaoKeDataProjectId;

    @Value("${fxiaoke.data.project.id:object_f23I6__c}")
    private String fXiaoKeDataProjectStartId;
    
    @ApolloJsonValue("${fxiaoke.project.full.sync.filters:[]}")
    private List<FxiaokeDataQueryFilter> fXiaoKeProjectFullSyncFilters;

    @ApolloJsonValue("${fxiaoke.project.single.sync.filters:[]}")
    private List<FxiaokeDataQueryFilter> fXiaoKeProjectSingleSyncFilters;
}
