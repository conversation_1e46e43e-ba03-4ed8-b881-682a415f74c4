package com.dt.train.management.center.model.dao;

import java.util.Date;

/**
 * 
 * 表名: train_user
 *
 * @mbg.generated
 */
@lombok.Data
public class TrainUserDO {
    /**
     * 字段描述: 主键ID，仅做主键，业务字段统一使用user_id
     *
     * 字段名: train_user.id
     *
     * @mbg.generated
     */
    private Integer id;

    /**
     * 字段描述: 用户中心-用户ID
     *
     * 字段名: train_user.user_id
     *
     * @mbg.generated
     */
    private Long userId;

    /**
     * 字段描述: 钉钉用户id
     *
     * 字段名: train_user.oa_user_id
     *
     * @mbg.generated
     */
    private String dingTalkUserId;

    /**
     * 字段描述: CRM用户id
     *
     * 字段名: train_user.crm_user_id
     *
     * @mbg.generated
     */
    private String crmUserId;

    /**
     * 字段描述: 所属部门id，使用;分隔
     *
     * 字段名: train_user.dept_ids
     *
     * @mbg.generated
     */
    private String deptIds;

    /**
     * 字段描述: 姓名
     *
     * 字段名: train_user.user_name
     *
     * @mbg.generated
     */
    private String userName;

    /**
     * 字段描述: 邮箱
     *
     * 字段名: train_user.email
     *
     * @mbg.generated
     */
    private String email;

    /**
     * 字段描述: 手机号
     *
     * 字段名: train_user.phone
     *
     * @mbg.generated
     */
    private String phone;

    /**
     * 字段描述: 钉钉职位
     *
     * 字段名: train_user.position
     *
     * @mbg.generated
     */
    private String userPosition;
    /**
     * 字段描述: 排班系统角色
     *
     * 字段名: train_user.role
     *
     * @mbg.generated
     */
    private String userRole;

    /**
     * 字段描述: 角色id
     *
     * 字段名: train_user.role_id
     *
     * @mbg.generated
     */
    private Integer roleId;

    /**
     * 字段描述: 工作地点
     *
     * 字段名: train_user.work_place
     *
     * @mbg.generated
     */
    private String workPlace;

    /**
     * 字段描述: 状态,0-在职，1-离职
     *
     * 字段名: train_user.user_status
     *
     * @mbg.generated
     */
    private Integer userStatus;

    /**
     * 字段描述: 成员类型，0-正式员工，1-兼职人员
     *
     * 字段名: train_user.user_type
     *
     * @mbg.generated
     */
    private Integer userType;

    /**
     * 字段描述: 是否已删除 0 否 1 是
     *
     * 字段名: train_user.invalid
     *
     * @mbg.generated
     */
    private Integer invalid;

    /**
     * 字段描述: 创建人
     *
     * 字段名: train_user.created_by
     *
     * @mbg.generated
     */
    private Long createdBy;

    /**
     * 字段描述: 更新人
     *
     * 字段名: train_user.updated_by
     *
     * @mbg.generated
     */
    private Long updatedBy;

    /**
     * 字段描述: 创建时间
     *
     * 字段名: train_user.gmt_create
     *
     * @mbg.generated
     */
    private Date gmtCreate;

    /**
     * 字段描述: 修改时间
     *
     * 字段名: train_user.gmt_modified
     *
     * @mbg.generated
     */
    private Date gmtModified;

    /**
     * 字段描述: 管理部门id，使用;分隔
     *
     * 字段名: train_user.manage_dept_ids
     *
     * @mbg.generated
     */
    private String manageDeptIds;
}
