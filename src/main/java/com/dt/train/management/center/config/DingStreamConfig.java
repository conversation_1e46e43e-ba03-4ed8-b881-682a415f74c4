package com.dt.train.management.center.config;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import com.dingtalk.open.app.api.OpenDingTalkClient;
import com.dingtalk.open.app.api.OpenDingTalkStreamClientBuilder;
import com.dingtalk.open.app.api.security.AuthClientCredential;
import com.dt.train.management.center.listener.DingAICallbackListener;

@Configuration
public class DingStreamConfig {

    @Autowired
    private BusinessConfig businessConfig;

    /**
     * 配置OpenDingTalkClient客户端并配置初始化方法(start)
     *
     * @param chatBotCallbackListener
     * @param aiGraphPluginCallbackListener
     * @return
     * @throws Exception
     */
    @Bean(initMethod = "start")
    public OpenDingTalkClient configureStreamClient(@Autowired DingAICallbackListener dingAICallbackListener) throws Exception {
        // init stream client
        return OpenDingTalkStreamClientBuilder.custom()
                //配置应用的身份信息, 企业内部应用分别为appKey和appSecret, 三方应用为suiteKey和suiteSecret
                .credential(new AuthClientCredential(businessConfig.getDingAiStreamAppClientId(), businessConfig.getDingAiStreamAppClientSecret()))
                //注册机器人回调
                .registerCallbackListener("/v1.0/aiAssistant/delivery/externalAccount/callback", dingAICallbackListener).build();
    }

}
