package com.dt.train.management.center.export.models;

import org.apache.poi.ss.usermodel.FillPatternType;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.HeadFontStyle;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import com.alibaba.excel.annotation.write.style.HeadStyle;

import lombok.Data;

@Data
@HeadRowHeight(30)
@HeadStyle(fillPatternType = FillPatternType.SOLID_FOREGROUND, fillForegroundColor = 30)
@HeadFontStyle(color = 9, fontHeightInPoints=11)
public class TrainProjectExcelDTO {

    @ExcelProperty("项目名称")
    @ColumnWidth(25)
    private String projectName;

    @ExcelProperty("项目编号")
    @ColumnWidth(20)
    private String projectCode;

    @ExcelProperty("项目起止时间")
    @ColumnWidth(25)
    private String projectDateRange;

    @ExcelProperty("项目状态")
    @ColumnWidth(15)
    private String projectStatus;

    @ExcelProperty("业务类型")
    @ColumnWidth(15)
    private String businessType;

    @ExcelProperty("所属区域")
    @ColumnWidth(15)
    private String projectArea;

    @ExcelProperty("所属省市区")
    @ColumnWidth(20)
    private String provinceCity;

    @ExcelProperty("项目级别")
    @ColumnWidth(15)
    private String projectLevel;

    @ExcelProperty("立项类型")
    @ColumnWidth(15)
    private String projectType;

    @ExcelProperty("研修模式")
    @ColumnWidth(15)
    private String projectMode;

    @ExcelProperty("服务对象")
    @ColumnWidth(20)
    private String serviceObject;

    @ExcelProperty("服务内容")
    @ColumnWidth(25)
    private String serviceContent;

    @ExcelProperty("项目总学时")
    @ColumnWidth(15)
    private String totalHours;

    @ExcelProperty("执行对接人姓名")
    @ColumnWidth(18)
    private String executeContactName;

    @ExcelProperty("执行对接人职务")
    @ColumnWidth(18)
    private String executeContactJob;

    @ExcelProperty("执行对接人手机号")
    @ColumnWidth(18)
    private String executeContactPhone;

    @ExcelProperty("面授方式")
    @ColumnWidth(15)
    private String courseForm;

    @ExcelProperty("是否异地")
    @ColumnWidth(12)
    private String isRemote;

    @ExcelProperty("责任编辑")
    @ColumnWidth(15)
    private String projectEditorName;

    @ExcelProperty("项目经理")
    @ColumnWidth(15)
    private String projectManagerName;

    @ExcelProperty("项目协助人\n（来自crm)")
    @ColumnWidth(20)
    private String projectHelperName;

    @ExcelProperty("学支编辑\n（来自crm)")
    @ColumnWidth(20)
    private String liveEditorName;

    @ExcelProperty("面授总天数\n（来自crm)")
    @ColumnWidth(18)
    private Float teachDays;

    @ExcelProperty("项目学员总人数\n（来自crm)")
    @ColumnWidth(20)
    private Integer totalUserCount;

    @ExcelProperty("项目排班天数")
    @ColumnWidth(15)
    private Float totalDays;

    @ExcelProperty("项目排班人数")
    @ColumnWidth(15)
    private Integer scheduledPersonCount;

    @ExcelProperty("项目排班人天")
    @ColumnWidth(15)
    private Float planUserDays;

    @ExcelProperty("预计班次数")
    @ColumnWidth(15)
    private Integer teachCourseCount;

    @ExcelProperty("已建班次数")
    @ColumnWidth(15)
    private Integer courseCount;

    @ExcelProperty("已完成班次数")
    @ColumnWidth(15)
    private Integer finishedCourseCount;

    @ExcelProperty("班次进度（完成/已建）")
    @ColumnWidth(20)
    private String courseProgress;
}
