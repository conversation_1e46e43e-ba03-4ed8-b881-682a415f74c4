package com.dt.train.management.center.enums;

import lombok.Getter;

@Getter
public enum UserStatusEnum {
    ON_JOB(0, "在职"),
    OFF_JOB(1, "离职");

    private final Integer code;
    private final String desc;

    UserStatusEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static String getDescByCode(Integer code) {
        if (code != null) {
            for (UserStatusEnum userStatusEnum : UserStatusEnum.values()) {
                if (userStatusEnum.getCode().equals(code)) {
                    return userStatusEnum.getDesc();
                }
            }
        }
        return ON_JOB.getDesc();
    }
}
