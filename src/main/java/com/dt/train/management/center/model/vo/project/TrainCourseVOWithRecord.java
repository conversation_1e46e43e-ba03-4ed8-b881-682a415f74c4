package com.dt.train.management.center.model.vo.project;

import java.util.List;

import com.dt.train.management.center.model.vo.check.AllocatedDayVO;
import com.dt.train.management.center.model.vo.check.TrainCheckRecordVO;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class TrainCourseVOWithRecord extends TrainCourseVO {

    @ApiModelProperty(value = "打卡状态,-1:待打卡,0:正常,1:异常")
    private Integer checkSummaryStatus;

    @ApiModelProperty(value = "打卡记录")
    private List<TrainCheckRecordVO> checkRecords;

    @ApiModelProperty(value = "排班信息")
    private AllocatedDayVO allocatedDay;

}
