package com.dt.train.management.center.export.service.impl;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import com.dt.train.management.center.enums.ProjectActionType;
import com.dt.train.management.center.enums.ProjectModeEnum;
import com.dt.train.management.center.export.enums.ReportType;
import com.dt.train.management.center.export.models.TrainProjectExcelDTO;
import com.dt.train.management.center.export.service.ReportDataFetcher;
import com.dt.train.management.center.model.vo.dict.DictMappingVO;
import com.dt.train.management.center.model.vo.project.TrainProjectWithCourseVO;
import com.dt.train.management.center.model.vo.user.TrainUserVO;
import com.dt.train.management.center.service.dict.DictMappingService;
import com.dt.train.management.center.service.project.TrainProjectService;

import lombok.extern.slf4j.Slf4j;

@Component
@Slf4j
public class TrainProjectDataFetcher implements ReportDataFetcher {

    @Autowired
    private TrainProjectService trainProjectService;

    @Autowired
    private DictMappingService dictMappingService;

    private static final SimpleDateFormat DATE_FORMAT = new SimpleDateFormat("yyyy/MM/dd");

    @Override
    public boolean supports(ReportType reportType) {
        return ReportType.TRAIN_PROJECT_REPORT.equals(reportType);
    }

    @Override
    public List<?> fetchData(Map<String, Object> parameters) {
        log.info("Fetching train project data with parameters: {}", parameters);
        
        if (!(parameters.get("ids") instanceof String)) {
            return new ArrayList<>();
        }
        
        String ids = (String) parameters.get("ids");
        List<Integer> idList = Arrays.asList(ids.split(",")).stream()
                .map(Integer::parseInt).collect(Collectors.toList());
        
        // 获取字典映射
        List<DictMappingVO> projectLevelDictList = dictMappingService.getDictListByDictType("project_level");
        Map<String, String> projectLevelDictMap = projectLevelDictList.stream()
                .collect(Collectors.toMap(DictMappingVO::getDictCode, DictMappingVO::getDictValue));
        
        List<DictMappingVO> projectTypeDictList = dictMappingService.getDictListByDictType("project_type");
        Map<String, String> projectTypeDictMap = projectTypeDictList.stream()
                .collect(Collectors.toMap(DictMappingVO::getDictCode, DictMappingVO::getDictValue));
        
        List<DictMappingVO> businessTypeDictList = dictMappingService.getDictListByDictType("business_type");
        Map<String, String> businessTypeDictMap = businessTypeDictList.stream()
                .collect(Collectors.toMap(DictMappingVO::getDictCode, DictMappingVO::getDictValue));

        List<TrainProjectExcelDTO> excelDTOs = new ArrayList<>();
        
        for (Integer projectId : idList) {
            try {
                TrainProjectWithCourseVO project = trainProjectService.getProjectWithCourse(projectId);
                if (project == null) {
                    continue;
                }
                
                TrainProjectExcelDTO dto = new TrainProjectExcelDTO();
                
                // 基本信息
                dto.setProjectName(project.getProjectName());
                dto.setProjectCode(project.getProjectCode());
                
                // 项目起止时间
                if (project.getStartDate() != null && project.getEndDate() != null) {
                    dto.setProjectDateRange(DATE_FORMAT.format(project.getStartDate()) + " - " + 
                                          DATE_FORMAT.format(project.getEndDate()));
                } else if (project.getStartDate() != null) {
                    dto.setProjectDateRange(DATE_FORMAT.format(project.getStartDate()) + " - ");
                } else if (project.getEndDate() != null) {
                    dto.setProjectDateRange(" - " + DATE_FORMAT.format(project.getEndDate()));
                }
                
                // 项目状态
                dto.setProjectStatus(getProjectStatusName(project.getProjectStatus()));
                
                // 业务类型
                dto.setBusinessType(businessTypeDictMap.get(String.valueOf(project.getBusinessType())));
                
                // 所属区域
                dto.setProjectArea(project.getProjectArea());
                
                // 所属省市区
                String provinceCity = project.getProvince();
                if (StringUtils.hasText(project.getCity())) {
                    provinceCity = provinceCity + "/" + project.getCity();
                }
                if (StringUtils.hasText(project.getArea())) {
                    provinceCity = provinceCity + "/" + project.getArea();
                }
                dto.setProvinceCity(provinceCity);
                
                // 项目级别
                dto.setProjectLevel(projectLevelDictMap.get(String.valueOf(project.getProjectLevel())));
                
                // 立项类型
                dto.setProjectType(projectTypeDictMap.get(String.valueOf(project.getProjectType())));
                
                // 研修模式
                dto.setProjectMode(getProjectModeName(project.getProjectMode()));
                
                // 服务对象和服务内容
                dto.setServiceObject(project.getServiceObject());
                dto.setServiceContent(project.getServiceContent());
                
                // 项目总学时
                dto.setTotalHours(project.getTotalHours());
                
                // 执行对接人信息
                dto.setExecuteContactName(project.getExecuteContactName());
                dto.setExecuteContactPhone(project.getExecuteContactPhone());
                dto.setExecuteContactJob(project.getExecuteContactJob());
                
                // 面授方式 - 从班次中获取主要的面授方式
                if(project.getActionType() != null && project.getActionType() != -1){
                    dto.setCourseForm(ProjectActionType.getByCode(project.getActionType()).getDesc());
                }

                // 是否异地
                if(project.getIsRemote() != null && project.getIsRemote() != -1){
                    dto.setIsRemote(project.getIsRemote() == 1 ? "异地面授" : "本地面授");
                }
                
                // 责任编辑和项目经理
                dto.setProjectEditorName(project.getProjectEditorName());
                dto.setProjectManagerName(project.getProjectManagerName());
                
                // CRM相关信息
                dto.setProjectHelperName(getHelperNames(project.getProjectHelpers()));
                dto.setLiveEditorName(getEditorNames(project.getLiveEditors()));
                dto.setTeachDays(project.getTeachDays());
                dto.setTotalUserCount(project.getTotalUserCount());
                
                // 排班统计信息
                dto.setTotalDays(project.getTotalDays());
                dto.setScheduledPersonCount(project.getScheduledPersonCount());
                dto.setPlanUserDays(project.getPlanUserDays());
                
                // 班次统计信息
                dto.setTeachCourseCount(project.getTeachCourseCount());
                dto.setCourseCount(project.getCourseCount());
                dto.setFinishedCourseCount(project.getFinishedCourseCount().intValue());
                
                // 班次进度
                if (project.getCourseCount() != null && project.getCourseCount() > 0) {
                    dto.setCourseProgress(project.getFinishedCourseCount() + " / " + project.getCourseCount());
                } else {
                    dto.setCourseProgress("0 / 0");
                }
                
                excelDTOs.add(dto);
                
            } catch (Exception e) {
                log.error("Error processing project with id: {}", projectId, e);
            }
        }
        
        return excelDTOs;
    }
    
    private String getProjectStatusName(Integer status) {
        if (status == null) return "";
        switch (status) {
            case 0: return "未开始";
            case 1: return "进行中";
            case 2: return "已完成";
            default: return "未知";
        }
    }
    
    private String getProjectModeName(String projectMode) {
        if (!StringUtils.hasText(projectMode)) return "";
        try {
            Integer code = Integer.parseInt(projectMode);
            ProjectModeEnum modeEnum = ProjectModeEnum.getByCode(code);
            return modeEnum != null ? modeEnum.getDesc() : "";
        } catch (NumberFormatException e) {
            return projectMode;
        }
    }
    
    private String getHelperNames(List<TrainUserVO> helpers) {
        if (helpers == null || helpers.isEmpty()) return "";
        return helpers.stream()
                .map(TrainUserVO::getUserName)
                .filter(name -> name != null && !name.trim().isEmpty())
                .collect(Collectors.joining(";"));
    }

    private String getEditorNames(List<TrainUserVO> editors) {
        if (editors == null || editors.isEmpty()) return "";
        return editors.stream()
                .map(TrainUserVO::getUserName)
                .filter(name -> name != null && !name.trim().isEmpty())
                .collect(Collectors.joining(";"));
    }
}
