package com.dt.train.management.center.enums;

import lombok.Getter;

@Getter
public enum ManagementExceptionEnum {

    /**
     * 项目不存在
     */
    PROJECT_NOT_EXIST("100000", "项目不存在"),
    HAS_NO_ACCESS_TO_PROJECT("100001", "无该项目访问权限"),
    PROJECT_NOT_SYNC_RULE("100002", "项目不符合同步规则"),
    PLAN_START_DATE_AFTER_END_DATE("100003", "计划开始时间不能大于计划结束时间"),
    REAL_START_DATE_AFTER_END_DATE("100004", "实际开始时间不能大于实际结束时间"),
    COURSE_NOT_EXIST("100005", "班次不存在"),
    COURSE_IN_PROGRESS_OR_COMPLETED_CANNOT_DELETE("100006", "进行中或已结束的班次不可删除"),
    TASK_NOT_FOUND("100007", "当前班次下不存在任务"),
    NO_SCHEDULE_FOR_USER("100008", "当前用户当前班次当天不存在排期"),
    CHECK_IN_TIME_AFTER_END("100009", "当前时间已超过上班打卡时间"),
    CHECK_IN_ALREADY_CHECKED("100010", "当前用户当前班次当天已打上班卡"),
    USER_NOT_EXIST("100011", "用户不存在"),
    CHECK_TASK_NOT_FOUND("100012", "打卡任务不存在"),
    DEPT_NOT_EXIST("100013", "部门不存在"),
    PROJECT_ALREADY_COMPLETED("100014", "项目已结束"),
    ROLE_NOT_EXIST("100015", "角色不存在"),
    ROLE_HAS_USERS("100016", "当前角色下仍有成员，请先前往成员管理中移除角色"),
    ROLE_NAME_EXISTS("100017", "角色名称已存在"),
    USER_NO_PERMISSION("100018", "暂无系统访问权限"),
    USER_DELETION_NOT_ALLOWED("100019", "当前成员已参与过班次排班，无法删除。\n请使用“停用”操作。"),

    PROJECT_SUFFIX_MHZ_CANNOT_DELETE("100020", "项目编号以MHZ结尾，不允许删除"),
    PROJECT_HAS_COURSES_CANNOT_DELETE("100021", "请先移除项目下班次，再删除项目"),

    PROJECT_NOT_IN_CURRENT_DEPT("100022", "你当前组织为%s，无法导入其他组织项目"),

    USER_CENTER_DATA_ERROR("500100", "用户中心数据获取失败"),
    USER_ID_CONFLICT("500101", "新用户ID已存在"),

    BAD_PARAMETER("110000", "参数错误："),
    ;

    private final String code;
    private final String message;

    ManagementExceptionEnum(String code, String message) {
        this.code = code;
        this.message = message;
    }

}
