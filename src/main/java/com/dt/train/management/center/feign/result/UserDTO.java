package com.dt.train.management.center.feign.result;

import com.dt.framework.business.constant.HeadTumbConstant;
import com.dt.framework.business.enums.RoleEnum;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.util.Date;

/**
 * 用户(老师，学生，家长，双师)
 * @author: zhangheng
 * @time: 2018-10-18  18:41
 */
@Data
public class UserDTO {

    /**
     * 头像url后缀,压缩图
     */
    private static final String ICON_SUFFIX = "?x-oss-process=image/resize,h_500";

    private static final String X_OSS_PROCESS = "&x-oss-process=image/resize,h_500";

    /**
     * 字段描述: 用户id
     */
    private Long id;

    /**
     * 字段描述: 用户编码
     */
    private Long userCode;

    /**
     * 字段描述: 真实姓名
     */
    private String realName;

    /**
     * 字段描述: 昵称
     */
    private String nickName;

    /**
     * 字段描述: 登录名
     */
    private String loginName;

    /**
     * 字段描述: 用户性别(0:女 1:男)
     */
    private Integer gender;

    /**
     * 字段描述: 角色
     */
    private Integer roleCode;

    /**
     * 字段描述: 手机号
     */
    private String mobile;

    /**
     * 字段描述: qq号
     */
    private String qq;

    /**
     * 字段描述: 邮箱
     */
    private String email;

    /**
     * 字段描述: 身份证号
     */
    private String idNo;

    /**
     * 字段描述: 个人简介
     */
    private String selfIntroduction;

    /**
     * 字段描述: 头像
     */
    private String icon;

    /**
     * 字段描述: 省份id
     */
    private Long provId;

    /**
     * 字段描述: 城市id
     */
    private Long cityId;

    /**
     * 字段描述: 区域id
     */
    private Long areaId;

    /**
     * 字段描述: 地址
     */
    private String address;

    /**
     * 字段描述: 生日
     */
    private Date birth;

    /**
     * 字段描述: 个性签名
     */
    private String selfSignature;

    /**
     * 字段描述: 备注
     */
    private String comments;

    /**
     * 字段描述: 用户标签
     */
    private String userTag;

    /**
     * 注册时间
     */
    private Date gmtCreate;

    /**
     * 修改时间
     */
    private Date gmtModified;

    /**
     * 管理者类型
     */
    private Integer managerType;

    private Long userId;

    private String phone;

    private Integer yn;

    private String dtTenant;

    private String dtBrand;


    /**
     * 给用户默认头像
     * @return
     */
    public String getIconWithDefault() {
        String iconUrl = this.icon;
        if (StringUtils.isEmpty(this.icon)) {
            iconUrl =  RoleEnum.STUDENT.getCode().equals(this.roleCode) ?
                    HeadTumbConstant.getStudentDefaultIcon(this.gender) : (RoleEnum.TEACHER.getCode().equals(this.roleCode) ?
                    HeadTumbConstant.DEFAULT_TEACHER_ICON: HeadTumbConstant.DEFAULT_PARENT_ICON);
        } else {
            if (iconUrl.endsWith(ICON_SUFFIX)
                    || iconUrl.contains("?x-oss-process=")
                    || iconUrl.contains("?imageMogr")) {
                return iconUrl;
            }
            if(iconUrl.contains("?")){
                return iconUrl.concat(X_OSS_PROCESS);
            }
        }
        return iconUrl.concat(ICON_SUFFIX);
    }

    /**
     * 删去icon后缀(裁剪缩略图后缀)
     * @param iconWithSuffix
     * @return
     */
    public static String cutSuffix(String iconWithSuffix){
        if(StringUtils.isNotEmpty(iconWithSuffix) && iconWithSuffix.endsWith(ICON_SUFFIX)){
            return iconWithSuffix.substring(0, iconWithSuffix.indexOf(ICON_SUFFIX));
        }
        return iconWithSuffix;
    }
}