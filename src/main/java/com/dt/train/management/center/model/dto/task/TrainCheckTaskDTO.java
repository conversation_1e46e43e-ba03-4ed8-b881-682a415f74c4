package com.dt.train.management.center.model.dto.task;

import java.util.Date;

/**
 * 
 * 表名: train_check_task
 *
 * @mbg.generated
 */
@lombok.Data
public class TrainCheckTaskDTO {
    /**
     * 字段描述: 主键ID
     *
     * 字段名: train_check_task.id
     *
     * @mbg.generated
     */
    private Integer id;

    /**
     * 字段描述: 项目ID
     *
     * 字段名: train_check_task.project_id
     *
     * @mbg.generated
     */
    private Integer projectId;

    /**
     * 字段描述: 班次ID
     *
     * 字段名: train_check_task.course_id
     *
     * @mbg.generated
     */
    private Integer courseId;

    /**
     * 字段描述: 任务ID
     *
     * 字段名: train_check_task.task_id
     *
     * @mbg.generated
     */
    private Integer taskId;

    /**
     * 字段描述: 用户ID
     *
     * 字段名: train_check_task.user_id
     *
     * @mbg.generated
     */
    private Long userId;

    private String userName;

    /**
     * 字段描述: 打卡维度，1-全天，2-上午，3-下午
     *
     * 字段名: train_check_task.check_dimension
     *
     * @mbg.generated
     */
    private Integer checkDimension;

    /**
     * 字段描述: 上班打卡状态,0-未打卡，1-正常打卡，2-迟到打卡,3-早退打卡
     *
     * 字段名: train_check_task.check_in_status
     *
     * @mbg.generated
     */
    private Integer checkInStatus;

    /**
     * 字段描述: 下班打卡状态,0-未打卡，1-正常打卡，2-迟到打卡,3-早退打卡
     *
     * 字段名: train_check_task.check_out_status
     *
     * @mbg.generated
     */
    private Integer checkOutStatus;

    /**
     * 字段描述: 现场签到状态,0-未签到，1-已签到
     *
     * 字段名: train_check_task.check_on_site_status
     *
     * @mbg.generated
     */
    private Integer checkOnSiteStatus;

    /**
     * 字段描述: 打卡状态,0-正常，1-异常
     *
     * 字段名: train_check_task.check_status
     *
     * @mbg.generated
     */
    private Integer checkStatus;

    /**
     * 字段描述: 打卡日期
     *
     * 字段名: train_check_task.check_date
     *
     * @mbg.generated
     */
    private Date checkDate;

    /**
     * 字段描述: 旧打卡状态
     *
     * 字段名: train_check_task.check_old_status
     *
     * @mbg.generated
     */
    private Integer checkOldStatus;

}