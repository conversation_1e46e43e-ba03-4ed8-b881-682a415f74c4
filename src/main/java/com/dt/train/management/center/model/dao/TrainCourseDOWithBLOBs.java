package com.dt.train.management.center.model.dao;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 
 * 表名: train_course
 *
 * @mbg.generated
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class TrainCourseDOWithBLOBs extends TrainCourseDO {
    /**
     * 字段描述: 计划说明
     *
     * 字段名: train_course.plan_desc
     *
     * @mbg.generated
     */
    private String planDesc;

    /**
     * 字段描述: 执行进展说明
     *
     * 字段名: train_course.execution_progress
     *
     * @mbg.generated
     */
    private String executionProgress;
}