package com.dt.train.management.center.enums;

import java.util.Arrays;

import lombok.Getter;

@Getter
public enum CheckSummaryStatusEnum {

    /**
     * 未打卡
     */
    NOT_CHECKED(-1, "待打卡"),
    /**
     * 正常
     */
    NORMAL(0, "正常"),
    /**
     * 异常
     */
    ABNORMAL(1, "异常");

    private final Integer code;
    private final String desc;

    CheckSummaryStatusEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static CheckSummaryStatusEnum fromCode(Integer code) {
        return Arrays.stream(CheckSummaryStatusEnum.values())
                .filter(status -> status.getCode().equals(code))
                .findFirst()
                .orElse(null);
    }

}
