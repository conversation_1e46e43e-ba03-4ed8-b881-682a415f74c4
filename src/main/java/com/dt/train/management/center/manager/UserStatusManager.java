package com.dt.train.management.center.manager;

import java.util.List;

import org.springframework.stereotype.Component;

import com.dt.train.management.center.constant.CacheConstant;
import com.dt.train.management.center.enums.ManagementExceptionEnum;
import com.dt.train.management.center.mapper.TrainUserDOMapper;
import com.dt.train.management.center.model.dao.TrainUserDO;
import com.dt.train.management.center.service.user.TrainUserService;
import com.dt.train.management.center.utils.ExceptionUtil;
import com.dt.train.management.center.utils.RedisUtil;

@Component
public class UserStatusManager {

    private final RedisUtil redisUtil;
    private final TrainUserService trainUserService;
    private final TrainUserDOMapper trainUserDOMapper;
    private static final String RESIGNED_USER_SET_KEY = CacheConstant.USER_STATUS_RESIGNED_SET;

    public UserStatusManager(RedisUtil redisUtil, TrainUserService trainUserService, TrainUserDOMapper trainUserDOMapper) {
        this.redisUtil = redisUtil;
        this.trainUserService = trainUserService;
        this.trainUserDOMapper = trainUserDOMapper;
    }

    /**
     * 获取所有离职用户id
     * @return 所有离职用户id set
     */
    public List<Long> getResignedUserIds() {
        return trainUserService.getResignedUserIds();
    }

    /**
     * 获取用户状态（带缓存）
     * @param userId 用户ID
     * @return 用户状态 (0-在职，1-离职)
     */
    public Integer getUserStatus(String userId) {
        // 检查缓存是否存在离职用户集合
        if (!redisUtil.hasKey(RESIGNED_USER_SET_KEY)) {
            loadResignedUsersToCache();
        }
        
        // 检查用户是否在离职集合中
        boolean isResigned = redisUtil.sismember(RESIGNED_USER_SET_KEY, userId);
        return isResigned ? 1 : 0;
    }
    
    /**
     * 加载离职用户ID到缓存
     */
    private void loadResignedUsersToCache() {
        // 查询所有离职用户ID（状态为1）
        List<Long> resignedUserIds = trainUserService.getResignedUserIds();
        
        if (resignedUserIds != null && !resignedUserIds.isEmpty()) {
            // 转换为字符串数组
            String[] userIds = resignedUserIds.stream()
                .map(String::valueOf)
                .toArray(String[]::new);
            
            // 添加到Set
            redisUtil.sadd(RESIGNED_USER_SET_KEY, userIds);
            
            // 设置缓存过期时间（1小时）
            redisUtil.expire(RESIGNED_USER_SET_KEY, 60 * 60);
        }
    }

    /**
     * 修改用户状态（启用/停用）
     *
     * @param userId    用户ID
     * @param userStatus 用户状态
     */
    public void changeUserStatus(String userId, Integer userStatus) {
        Long userIdLong = Long.parseLong(userId);
        TrainUserDO user = trainUserDOMapper.selectByUserId(userIdLong);
        if (user == null) {
            throw ExceptionUtil.businessException(ManagementExceptionEnum.USER_NOT_EXIST);
        }
        TrainUserDO update = new TrainUserDO();
        update.setId(user.getId());
        update.setUserStatus(userStatus);
        trainUserService.updateUserStatus(update);
        redisUtil.del(CacheConstant.getTrainUserInfoKey(userId));
        if (userStatus == 1) {
            // 如果用户状态为离职，则将其加入离职用户集合
            redisUtil.sadd(RESIGNED_USER_SET_KEY, userId);
        } else {
            // 否则从离职用户集合中移除
            redisUtil.srem(RESIGNED_USER_SET_KEY, userId);
        }
    }

}
