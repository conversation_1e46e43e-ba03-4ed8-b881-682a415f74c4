package com.dt.train.management.center.service.dept.impl;


import com.dt.train.management.center.config.BusinessConfig;
import com.dt.train.management.center.enums.ManagementExceptionEnum;
import com.dt.train.management.center.manager.UserStatusManager;
import com.dt.train.management.center.mapper.TrainDeptDOMapper;
import com.dt.train.management.center.mapper.TrainUserDOMapper;
import com.dt.train.management.center.model.dao.TrainDeptDO;
import com.dt.train.management.center.model.dao.TrainUserDO;
import com.dt.train.management.center.model.dept.UserDeptVO;
import com.dt.train.management.center.model.dto.user.TrainUserQueryDTO;
import com.dt.train.management.center.model.vo.config.CrmOrgProvinceDeptMappingConfigVO;
import com.dt.train.management.center.model.vo.dept.TrainDeptVO;
import com.dt.train.management.center.model.vo.user.TrainUserVO;
import com.dt.train.management.center.service.cache.DeptCacheService;
import com.dt.train.management.center.service.dept.TrainDeptService;
import com.dt.train.management.center.utils.ExceptionUtil;
import com.dt.train.management.center.utils.StringParseUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
public class TrainDeptServiceImpl implements TrainDeptService {

    @Resource
    private TrainUserDOMapper trainUserDOMapper;
    @Resource
    private TrainDeptDOMapper trainDeptDOMapper;
    @Resource
    private BusinessConfig businessConfig;

    @Resource
    private UserStatusManager userStatusManager;
    @Resource
    private DeptCacheService deptCacheService;
    /**
     * 根据用户id获取部门树
     *
     * @param userId 用户id
     */
    @Override
    public List<UserDeptVO> getByUserId(Long userId,Boolean isAll) {
        // 获取全量数据
        List<TrainDeptDO> allDepts = trainDeptDOMapper.selectAllDepts();
        if (CollectionUtils.isEmpty(allDepts)) {
            return Collections.emptyList();
        }
        List<TrainUserDO> allUsers = trainUserDOMapper.selectValidUsers();
        if (CollectionUtils.isEmpty(allUsers)) {
            return Collections.emptyList();
        }
        // 构建部门路径映射
        Map<Integer, String> deptPathMap = buildDeptPathMap(allDepts);
        // 构建部门用户数映射
        Map<Integer, Integer> directUserCountMap = buildDirectUserCountMap(allUsers, deptPathMap);
        // 计算重复统计的用户数（每个用户多出的部门数总和）
        int duplicateCount = getDuplicateCount(allUsers, deptPathMap);
        // 所有二级部门用户数单独查询
        List<Integer> subDeptIds = allDepts.stream().filter(d -> d.getDeptLevel() == 2).map(TrainDeptDO::getId).collect(Collectors.toList());
        Map<Integer, Integer> subDeptUserCountMap = buildSubDeptUserCountMap(subDeptIds);
        // 需要返回所有部门
        if (isAll != null && isAll) {
            List<Integer> topLevelDepts = getTopLevelDeptIds(allDepts);
            List<UserDeptVO> userDeptVOS = buildDeptTree(allDepts, topLevelDepts, directUserCountMap);
            for (UserDeptVO userDeptVO : userDeptVOS) {
                if (userDeptVO.getLevel() == 1) {
                    buildSubDeptUserCount(userDeptVO, duplicateCount, subDeptUserCountMap);
                }
        }
        return userDeptVOS;
    }
    // 参数校验
        if (userId == null) return Collections.emptyList();
        // 获取用户信息
        TrainUserDO user = trainUserDOMapper.selectByUserId(userId);
        if (user == null || StringUtils.isEmpty(user.getManageDeptIds())) {
            log.info("用户未配置部门, userId={}", userId);
            return Collections.emptyList();
        }

        // 5. 确定根节点部门
        List<Integer> rootIds = determineRootDepts(user, allDepts);
        // 6. 构建完整部门树
        List<UserDeptVO> userDeptVOS = buildDeptTree(allDepts, rootIds, directUserCountMap);
        // 7. 如果用户存在子部门，返回父子部门全称
        Map<Integer, TrainDeptDO> allDeptIdToDOMap = allDepts.stream().collect(Collectors.toMap(TrainDeptDO::getId, Function.identity()));
        for (UserDeptVO userDeptVO : userDeptVOS) {
            TrainDeptDO parentDept = allDeptIdToDOMap.get(userDeptVO.getParentDeptId());
            if (parentDept != null) {
                userDeptVO.setDeptName(parentDept.getDeptName() + "-" + userDeptVO.getDeptName());
            }
            if(userDeptVO.getLevel() == 1) {
                buildSubDeptUserCount(userDeptVO, duplicateCount, subDeptUserCountMap);
            }else if(userDeptVO.getLevel() == 2){
                Integer subDeptUserCount = subDeptUserCountMap.get(userDeptVO.getDeptId());
                if (subDeptUserCount != null) {
                    userDeptVO.setUserCount(subDeptUserCount);
                }
            }
        }
        return userDeptVOS;
    }

    // 构建二级部门用户数
    private static void buildSubDeptUserCount(UserDeptVO userDeptVO, int duplicateCount, Map<Integer, Integer> subDeptUserCountMap) {
        userDeptVO.setUserCount(userDeptVO.getUserCount() - duplicateCount);
        List<UserDeptVO> children = userDeptVO.getChildren();
        if (CollectionUtils.isNotEmpty(children)) {
            for (UserDeptVO child : children) {
                Integer subDeptUserCount = subDeptUserCountMap.get(child.getDeptId());
                if (subDeptUserCount != null) {
                    child.setUserCount(subDeptUserCount);
                }
            }
        }
    }

    private Map<Integer, Integer> buildSubDeptUserCountMap(List<Integer> subDeptIds) {
        Map<Integer, Integer> result = new HashMap<>();
        if (CollectionUtils.isEmpty(subDeptIds)) {
            return Collections.emptyMap();
        }
        for (Integer subDeptId : subDeptIds) {
            TrainUserQueryDTO query = new TrainUserQueryDTO();
            List<Integer> allChildrenDeptIds = deptCacheService.getAllChildrenDeptIds(subDeptId);
            query.setDeptIds(allChildrenDeptIds);
            query.setDeptId(null);
            long total = trainUserDOMapper.countByQuery(query);
            result.put(subDeptId, (int) total);
        }
        return result;
    }

    private int getDuplicateCount(List<TrainUserDO> allUsers, Map<Integer, String> deptPathMap) {
        return allUsers.stream()
                .mapToInt(user -> {
                    String deptIds = user.getDeptIds();
                    if (deptIds == null || deptIds.isEmpty()) {
                        return 0;
                    }

                    // 原始部门数量（未过滤父子部门）
                    int originalDeptCount = deptIds.split(";").length;
                    if (originalDeptCount <= 1) {
                        return 0;
                    }

                    // 过滤后的部门数量（已去除父子部门）
                    List<Integer> filteredDepts = parseDirectDeptIds(deptIds, deptPathMap);
                    int filteredDeptCount = filteredDepts.size();

                    // 如果过滤后仍有多个部门（平级部门），则计算重复统计数
                    return filteredDeptCount > 1 ? filteredDeptCount - 1 : 0;
                })
                .sum();
    }

    /**
     * 根据CRM区域组织架构id和省份id获取部门信息
     *
     * @param crmOrgId      CRM区域组织架构id
     * @param crmProvinceId 省份id(如果为空则只根据CRM区域组织架构id获取一级部门信息)
     */
    @Override
    public TrainDeptVO getDeptByCrmOrgIdAndCrmProvinceId(String crmOrgId, String crmProvinceId) {
        // 参数校验和日志记录
        if (StringUtils.isBlank(crmOrgId)) {
            return new TrainDeptVO();
        }

        // 获取配置映射
        Map<String, Integer> orgDeptMap = businessConfig.getCrmOrgOneLevelDeptMapping();
        List<CrmOrgProvinceDeptMappingConfigVO> provinceMappings = businessConfig.getCrmOrgMappingConfigVO();

        // 获取部门ID逻辑
        Integer deptId = resolveDeptId(crmOrgId, crmProvinceId, orgDeptMap, provinceMappings);

        // 数据库查询
        return getDeptVoFromDbById(deptId);
    }

    /**
     * 解析部门ID核心逻辑
     */
    private Integer resolveDeptId(String crmOrgId,
                                  String crmProvinceId,
                                  Map<String, Integer> orgDeptMap,
                                  List<CrmOrgProvinceDeptMappingConfigVO> provinceMappings) {
        // 情况1：省份ID不为空时，优先查找子部门映射
        if (StringUtils.isNotBlank(crmProvinceId)) {
            Optional<Integer> subDeptOpt = provinceMappings.stream()
                    .filter(m -> crmOrgId.equals(m.getCrmOrgRegionId()))
                    .filter(m -> crmProvinceId.equals(m.getCrmProvinceId()))
                    .map(CrmOrgProvinceDeptMappingConfigVO::getTargetSubDeptId)
                    .findFirst();

            if (subDeptOpt.isPresent()) {
                log.debug("命中子部门映射 crmOrgId:{}, provinceId:{} -> deptId:{}",
                        crmOrgId, crmProvinceId, subDeptOpt.get());
                return subDeptOpt.get();
            }
        }

        // 情况2：回退到一级部门映射
        Integer mainDeptId = orgDeptMap.get(crmOrgId);
        if (mainDeptId != null) {
            log.debug("使用一级部门映射 crmOrgId:{} -> deptId:{}", crmOrgId, mainDeptId);
            return mainDeptId;
        }

        // 情况3：无匹配配置
        log.info("未找到部门映射 crmOrgId:{}, provinceId:{}", crmOrgId, crmProvinceId);
        return null;
    }

    /**
     * 从数据库获取部门信息
     */
    @Override
    public TrainDeptVO getDeptVoFromDbById(Integer deptId) {
        if (deptId == null) {
            return new TrainDeptVO();
        }
        TrainDeptDO deptDO = trainDeptDOMapper.selectByPrimaryKey(deptId);
        if (deptDO == null) {
            log.warn("部门不存在 deptId:{}", deptId);
            return new TrainDeptVO();
        }
        TrainDeptDO parentDeptDO = trainDeptDOMapper.selectByPrimaryKey(deptDO.getParentId());
        TrainDeptVO vo = new TrainDeptVO();
        BeanUtils.copyProperties(deptDO, vo);
        if (parentDeptDO != null) {
            vo.setParentDeptName(parentDeptDO.getDeptName());
            vo.setParentDeptNameShort(parentDeptDO.getDeptNameShort());
        }
        return vo;
    }

    /**
     * 根据部门id获取当前部门及子部门的用户列表
     * @param deptId
     * @return
     */
    @Override
    public List<UserDeptVO> getUserById(Integer deptId, String userName) {
        if (deptId == null) {
            return Collections.emptyList();
        }

        // 验证部门存在性
        TrainDeptDO deptDO = trainDeptDOMapper.selectByPrimaryKey(deptId);
        if (deptDO == null || deptDO.getInvalid() == 1) {
            throw ExceptionUtil.businessException(ManagementExceptionEnum.DEPT_NOT_EXIST);
        }

        // 1. 获取所有有效部门（排除隐藏部门）
        List<TrainDeptDO> allDepts = trainDeptDOMapper.selectAllDepts()
                .stream()
                .filter(d -> d.getHidden() != 1)
                .collect(Collectors.toList());

        // 2. 获取所有有效用户
        List<TrainUserDO> allUsers = trainUserDOMapper.selectValidUsers();

        // 3. 构建部门路径映射
        Map<Integer, String> deptPathMap = buildDeptPathMap(allDepts);

        // 4. 按部门分组用户
        Map<Integer, List<TrainUserVO>> deptUsersMap = groupUsersByDept(allUsers, deptPathMap);

        // 准备相关用户ID集合
        Set<Long> relevantUserIds = allUsers.stream()
                .map(TrainUserDO::getUserId)
                .collect(Collectors.toSet());

        // 5. 处理用户名筛选
        Set<Integer> matchedDeptIds = new HashSet<>();
        if (StringUtils.isNotBlank(userName)) {
            // 应用用户名筛选（不区分大小写）
            String lowerUserName = userName.toLowerCase();
            Set<Long> nameMatchedUserIds = allUsers.stream()
                    .filter(user -> user.getUserName().toLowerCase().contains(lowerUserName))
                    .map(TrainUserDO::getUserId)
                    .collect(Collectors.toSet());

            // 更新相关用户ID为匹配的用户
            relevantUserIds = nameMatchedUserIds;

            // 收集匹配用户的部门ID
            for (Map.Entry<Integer, List<TrainUserVO>> entry : deptUsersMap.entrySet()) {
                boolean hasMatchedUser = entry.getValue().stream()
                        .anyMatch(user -> nameMatchedUserIds.contains(user.getUserId()));

                if (hasMatchedUser) {
                    matchedDeptIds.add(entry.getKey());
                }
            }
        }

        // 6. 构建部门父子关系映射
        Map<Integer, List<TrainDeptDO>> parentChildrenMap = buildParentChildrenMap(allDepts);
        Map<Integer, TrainDeptDO> deptMap = allDepts.stream()
                .collect(Collectors.toMap(TrainDeptDO::getId, dept -> dept));

        // 7. 处理超管部门逻辑
        if (deptDO.getDeptType() == 0) {
            // 超管部门 - 返回所有部门
            return buildFullDeptTree(parentChildrenMap, deptUsersMap, relevantUserIds,
                    matchedDeptIds, userName != null, deptMap);
        } else {
            // 非超管部门 - 返回当前部门及子部门
            Set<Integer> relevantDeptIds = new HashSet<>(Collections.singleton(deptId));
            collectChildDeptIds(parentChildrenMap, deptId, relevantDeptIds);
            return buildRelevantDeptTree(deptId, parentChildrenMap, deptUsersMap,
                    relevantUserIds, matchedDeptIds, relevantDeptIds, userName != null, deptMap);
        }
    }
    // 构建完整的部门树（用于超管部门）
    private List<UserDeptVO> buildFullDeptTree(
            Map<Integer, List<TrainDeptDO>> parentChildrenMap,
            Map<Integer, List<TrainUserVO>> deptUsersMap,
            Set<Long> relevantUserIds,
            Set<Integer> matchedDeptIds,
            boolean hasUserNameFilter,
            Map<Integer, TrainDeptDO> deptMap) {

        // 从根部门（0级）开始构建整棵树
        List<TrainDeptDO> rootDepts = parentChildrenMap.getOrDefault(-1, Collections.emptyList());

        // 准备展示的部门ID集合（如果有用户名筛选，则为匹配部门及其祖先）
        Set<Integer> deptIdsToShow = new HashSet<>();
        if (hasUserNameFilter) {
            for (Integer deptId : matchedDeptIds) {
                addDeptAndAncestors(deptId, deptMap, deptIdsToShow);
            }
        }

        List<UserDeptVO> result = new ArrayList<>();
        for (TrainDeptDO rootDept : rootDepts) {
            if (hasUserNameFilter && !deptIdsToShow.contains(rootDept.getId())) {
                continue; // 非匹配相关节点不展示
            }

            UserDeptVO rootNode = buildDeptNodeContainsUser(rootDept, parentChildrenMap, deptUsersMap,
                    relevantUserIds, null, matchedDeptIds, hasUserNameFilter, deptMap);
            if (rootNode != null) {
                result.add(rootNode);
            }
        }

        return result;
    }

    // 构建相关部门树（用于普通部门）
    private List<UserDeptVO> buildRelevantDeptTree(
            Integer rootDeptId,
            Map<Integer, List<TrainDeptDO>> parentChildrenMap,
            Map<Integer, List<TrainUserVO>> deptUsersMap,
            Set<Long> relevantUserIds,
            Set<Integer> matchedDeptIds,
            Set<Integer> relevantDeptIds,
            boolean hasUserNameFilter,
            Map<Integer, TrainDeptDO> deptMap) {

        // 准备展示的部门ID集合（如果有用户名筛选，则为匹配部门及其祖先）
        Set<Integer> deptIdsToShow = new HashSet<>();
        if (hasUserNameFilter) {
            for (Integer deptId : matchedDeptIds) {
                if (relevantDeptIds.contains(deptId)) {
                    addDeptAndAncestors(deptId, deptMap, deptIdsToShow);
                }
            }
        }

        // 查找根部门
        TrainDeptDO rootDeptDO = parentChildrenMap.values().stream()
                .flatMap(List::stream)
                .filter(d -> d.getId().equals(rootDeptId))
                .findFirst()
                .orElse(null);

        if (rootDeptDO == null) {
            return Collections.emptyList();
        }

        // 检查是否需要展示该根部门
        if (hasUserNameFilter && !deptIdsToShow.contains(rootDeptId)) {
            return Collections.emptyList();
        }

        // 构建以该部门为根的树
        UserDeptVO rootNode = buildDeptNodeContainsUser(rootDeptDO, parentChildrenMap, deptUsersMap,
                relevantUserIds, relevantDeptIds, matchedDeptIds, hasUserNameFilter, deptMap);
        if (rootNode == null) {
            return Collections.emptyList();
        }
        return Collections.singletonList(rootNode);
    }
    // 添加部门及其所有祖先部门
    private void addDeptAndAncestors(Integer deptId, Map<Integer, TrainDeptDO> deptMap, Set<Integer> collector) {
        Integer currentId = deptId;
        while (currentId != null && currentId != 0) {
            collector.add(currentId);
            TrainDeptDO current = deptMap.get(currentId);
            if (current == null || current.getParentId() == null) {
                break;
            }
            currentId = current.getParentId();
        }
    }
    // 构建部门节点
    private UserDeptVO buildDeptNodeContainsUser(
            TrainDeptDO dept,
            Map<Integer, List<TrainDeptDO>> parentChildrenMap,
            Map<Integer, List<TrainUserVO>> deptUsersMap,
            Set<Long> relevantUserIds,
            Set<Integer> relevantDeptIds,
            Set<Integer> matchedDeptIds,
            boolean hasUserNameFilter,
            Map<Integer, TrainDeptDO> deptMap) {

        // 1. 部门筛选检查（非超管部门需要）
        if (relevantDeptIds != null && !relevantDeptIds.contains(dept.getId())) {
            return null;
        }

        // 2. 用户名筛选时，检查是否为匹配部门或其祖先
        if (hasUserNameFilter && !matchedDeptIds.contains(dept.getId()) && !isAncestorOfMatched(dept, matchedDeptIds, deptMap)) {
            return null;
        }

        UserDeptVO node = new UserDeptVO();
        node.setDeptId(dept.getId());
        node.setDeptName(dept.getDeptName());
        node.setDeptNameShort(dept.getDeptNameShort());
        node.setLevel(dept.getDeptLevel());

        // 设置用户列表（过滤符合筛选条件的用户）
        List<TrainUserVO> users = deptUsersMap.getOrDefault(dept.getId(), Collections.emptyList())
                .stream()
                .filter(user -> relevantUserIds.contains(user.getUserId()))
                .collect(Collectors.toList());
        node.setUserInfoList(users);

        // 递归构建子部门
        List<UserDeptVO> children = new ArrayList<>();
        List<TrainDeptDO> childDepts = parentChildrenMap.getOrDefault(dept.getId(), Collections.emptyList());

        for (TrainDeptDO child : childDepts) {
            // 用户名筛选时，只展示匹配部门（不展示匹配部门的子部门）
            if (hasUserNameFilter && matchedDeptIds.contains(dept.getId())) {
                // 如果当前部门是匹配部门，不展示子部门
                continue;
            }

            UserDeptVO childNode = buildDeptNodeContainsUser(child, parentChildrenMap, deptUsersMap,
                    relevantUserIds, relevantDeptIds, matchedDeptIds, hasUserNameFilter, deptMap);
            if (childNode != null) {
                children.add(childNode);
            }
        }

        node.setChildren(children);

        return node;
    }
    // 检查部门是否是任何匹配部门的祖先
    private boolean isAncestorOfMatched(
            TrainDeptDO dept,
            Set<Integer> matchedDeptIds,
            Map<Integer, TrainDeptDO> deptMap) {

        for (Integer matchedDeptId : matchedDeptIds) {
            if (isAncestor(dept.getId(), matchedDeptId, deptMap)) {
                return true;
            }
        }
        return false;
    }

    // 检查部门A是否是部门B的祖先
    private boolean isAncestor(Integer ancestorId, Integer deptId, Map<Integer, TrainDeptDO> deptMap) {
        Integer currentId = deptId;
        while (currentId != null && currentId != 0) {
            if (ancestorId.equals(currentId)) {
                return true;
            }
            TrainDeptDO current = deptMap.get(currentId);
            if (current == null || current.getParentId() == null) {
                break;
            }
            currentId = current.getParentId();
        }
        return false;
    }
    // 检查部门是否应包含在结果中
    private boolean shouldInclude(Integer deptId, Set<Integer> relevantDeptIds) {
        return relevantDeptIds == null || relevantDeptIds.contains(deptId);
    }
    // 收集所有子部门ID
    private void collectChildDeptIds(
            Map<Integer, List<TrainDeptDO>> parentChildrenMap,
            Integer parentId,
            Set<Integer> collector) {

        List<TrainDeptDO> children = parentChildrenMap.getOrDefault(parentId, Collections.emptyList());
        for (TrainDeptDO child : children) {
            collector.add(child.getId());
            collectChildDeptIds(parentChildrenMap, child.getId(), collector);
        }
    }

    // 构建部门父子关系映射
    private Map<Integer, List<TrainDeptDO>> buildParentChildrenMap(List<TrainDeptDO> allDepts) {
        Map<Integer, List<TrainDeptDO>> map = new HashMap<>();
        for (TrainDeptDO dept : allDepts) {
            Integer parentId = dept.getParentId() != null ? dept.getParentId() : 0;
            map.computeIfAbsent(parentId, k -> new ArrayList<>()).add(dept);
        }
        return map;
    }

    // 按部门分组用户
    private Map<Integer, List<TrainUserVO>> groupUsersByDept(
            List<TrainUserDO> users,
            Map<Integer, String> deptPathMap) {

        Map<Integer, List<TrainUserVO>> result = new HashMap<>();
        List<Long> resignedUserIds = userStatusManager.getResignedUserIds();

        for (TrainUserDO user : users) {
            TrainUserVO userVO = convertToTrainUserVO(user, resignedUserIds);
            List<Integer> directDeptIds = parseDirectDeptIds(user.getDeptIds(), deptPathMap);

            for (Integer deptId : directDeptIds) {
                result.computeIfAbsent(deptId, k -> new ArrayList<>()).add(userVO);
            }
        }

        return result;
    }

    // 转换用户对象
    private TrainUserVO convertToTrainUserVO(TrainUserDO user, List<Long> resignedUserIds) {
        TrainUserVO vo = new TrainUserVO();
        vo.setUserId(user.getUserId());
        vo.setUserName(user.getUserName());
        vo.setUserStatus(resignedUserIds.contains(user.getUserId()) ? 1 : 0);
        // 其他属性不需要设置
        return vo;
    }


    /**
     * 构建直接用户数映射表（部门 -> 直接关联用户数）
     */
    private Map<Integer, Integer> buildDirectUserCountMap(List<TrainUserDO> users,  Map<Integer, String> deptPathMap) {
        // 并行流处理用户
        return users.parallelStream()
                .flatMap(user -> parseDirectDeptIds(user.getDeptIds(), deptPathMap).stream())
                .collect(Collectors.toMap(
                        deptId -> deptId,
                        deptId -> 1,
                        Integer::sum
                ));
    }

    /**
     * 确定根部门ID列表
     */
    private List<Integer> determineRootDepts(TrainUserDO user, List<TrainDeptDO> allDepts) {
        // 解析用户部门ID
        List<Integer> userDeptIds = parseDeptIds(user.getManageDeptIds());

        // 检查是否包含超管部门
        boolean hasSuperAdminDept = userDeptIds.stream()
                .map(id -> findDeptById(allDepts, id))
                .anyMatch(dept -> dept != null && dept.getDeptType() == 0);

        return hasSuperAdminDept ?
                getTopLevelDeptIds(allDepts) :   // 超管模式：顶级部门
                getValidUserDepts(userDeptIds, allDepts); // 普通模式：用户部门
    }

    /**
     * 获取所有顶级非隐藏部门
     */
    private List<Integer> getTopLevelDeptIds(List<TrainDeptDO> allDepts) {
        return allDepts.stream()
                .filter(dept -> (dept.getParentId() == null || dept.getParentId() == -1) && dept.getHidden() == 0)
                .map(TrainDeptDO::getId)
                .collect(Collectors.toList());
    }

    /**
     * 获取用户的有效部门（过滤隐藏部门）
     */
    private List<Integer> getValidUserDepts(List<Integer> userDeptIds, List<TrainDeptDO> allDepts) {
        return userDeptIds.stream()
                .map(id -> findDeptById(allDepts, id))
                .filter(dept -> dept != null && dept.getHidden() == 0)
                .map(TrainDeptDO::getId)
                .collect(Collectors.toList());
    }

    /**
     * 构建部门树结构
     */
    private List<UserDeptVO> buildDeptTree(List<TrainDeptDO> allDepts,
                                           List<Integer> rootIds,
                                           Map<Integer, Integer> userCountMap) {
        Map<Integer, TrainDeptDO> deptMap = allDepts.stream()
                .collect(Collectors.toMap(TrainDeptDO::getId, Function.identity()));

        return rootIds.stream()
                .map(id -> buildDeptNode(deptMap.get(id), deptMap, userCountMap))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    /**
     * 递归构建部门节点（包含用户数聚合）
     */
    private UserDeptVO buildDeptNode(TrainDeptDO dept,
                                     Map<Integer, TrainDeptDO> deptMap,
                                     Map<Integer, Integer> directUserCountMap) {
        // 过滤隐藏部门
        if (dept == null || dept.getHidden() == 1) return null;

        // 初始化节点基础信息
        UserDeptVO vo = new UserDeptVO();
        vo.setDeptId(dept.getId());
        vo.setDeptName(dept.getDeptName());
        vo.setParentDeptId(dept.getParentId());
        vo.setDeptNameShort(dept.getDeptNameShort());
        vo.setLevel(dept.getDeptLevel());
        vo.setUserCount(directUserCountMap.getOrDefault(dept.getId(), 0));

        // 递归构建子节点
        List<UserDeptVO> children = deptMap.values().stream()
                .filter(child ->
                        child.getParentId() != null &&
                                child.getParentId().equals(dept.getId())
                )
                .map(child -> buildDeptNode(child, deptMap, directUserCountMap))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

        // 聚合子节点用户数
        if (!children.isEmpty()) {
            int childrenTotal = children.stream()
                    .mapToInt(UserDeptVO::getUserCount)
                    .sum();
            vo.setUserCount(vo.getUserCount() + childrenTotal);
        }

        vo.setChildren(children.isEmpty() ? null : children);
        return vo;
    }

    /**
     * 工具方法：解析部门ID字符串
     */
    private List<Integer> parseDeptIds(String deptIdsStr) {
        return StringParseUtil.semicolonSeparated(deptIdsStr);
    }

    /**
     * 工具方法：解析直接关联部门ID列表（不包含被包含的部门）
     */
    private List<Integer> parseDirectDeptIds(String deptIds, Map<Integer, String> deptPathMap) {
        List<Integer> allDepts = Arrays.stream(deptIds.split(";"))
                .filter(StringUtils::isNotBlank)
                .map(Integer::parseInt)
                .collect(Collectors.toList());

        // 过滤掉被其他部门包含的部门
        return allDepts.stream()
                .filter(deptId ->
                        // 检查当前部门是否没有被同一个用户的其他部门包含
                        allDepts.stream().noneMatch(otherId ->
                                !deptId.equals(otherId) &&
                                        deptPathMap.getOrDefault(otherId, "").contains(";" + deptId + ";")
                        )
                )
                .collect(Collectors.toList());
    }

    /**
     * 工具方法：构建部门路径
     */
    private Map<Integer, String> buildDeptPathMap(List<TrainDeptDO> allDepts) {
        Map<Integer, String> deptPathMap = new HashMap<>();

        // 构建部门路径映射 (deptId -> 完整路径)
        Map<Integer, TrainDeptDO> deptMap = allDepts.stream().collect(Collectors.toMap(TrainDeptDO::getId, dept -> dept));

        for (TrainDeptDO dept : allDepts) {
            StringBuilder path = new StringBuilder();
            int currentId = dept.getId();

            while (currentId != 0) {
                path.insert(0, ";" + currentId + ";");
                TrainDeptDO current = deptMap.get(currentId);
                if (current == null || current.getParentId() == null) break;
                currentId = current.getParentId();
            }

            deptPathMap.put(dept.getId(), path.toString());
        }

        return deptPathMap;
    }
    /**
     * 工具方法：根据ID查找部门
     */
    private TrainDeptDO findDeptById(List<TrainDeptDO> depts, Integer id) {
        return depts.stream()
                .filter(d -> d.getId().equals(id))
                .findFirst()
                .orElse(null);
    }
}

