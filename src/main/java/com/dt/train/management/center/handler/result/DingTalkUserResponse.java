package com.dt.train.management.center.handler.result;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.Map;

@Data
public class DingTalkUserResponse {
    private String errcode;
    private String errmsg;
    private Result result;

    @Data
    public static class Result {
        // JSON字符串格式的扩展信息
        private String extension;
        private String unionid;

        @JsonProperty("boss")
        private Boolean isBoss;

        private Role role_list;
        private Boolean exclusive_account;
        private String manager_userid;

        @JsonProperty("admin")
        private Boolean isAdmin;

        private String remark;
        private String title;
        private Long hired_date;
        private String userid;
        private String work_place;
        private DeptOrder dept_order_list;

        @JsonProperty("real_authed")
        private Boolean isRealAuthed;
        // JSON数组字符串格式的部门ID列表
        private String dept_id_list;
        private String job_number;
        private String email;
        private LeaderInDept leader_in_dept;
        private String mobile;

        @JsonProperty("active")
        private Boolean isActive;

        private String org_email;
        private String telephone;
        private String avatar;

        @JsonProperty("hide_mobile")
        private Boolean isHideMobile;

        @JsonProperty("senior")
        private Boolean isSenior;

        private String name;
        private UnionEmpExt union_emp_ext;
        private String state_code;
    }

    @Data
    public static class Role {
        private String group_name;
        private String name;
        private String id;
    }

    @Data
    public static class DeptOrder {
        private String dept_id;
        private String order;
    }

    @Data
    public static class LeaderInDept {

        @JsonProperty("leader")
        private Boolean isLeader;

        private String dept_id;
    }

    @Data
    public static class UnionEmpExt {
        private Map<String, Object> union_emp_map_list;
        private String userid;
        private String corp_id;
    }
}