package com.dt.train.management.center.model.vo.project;

import lombok.Data;

@Data
public class ProjectStatusSummaryVO {

    /**
     * 总项目数
     */
    private int total;

    /**
     * 已完成项目数
     */
    private int completed;

    /**
     * 进行中项目数
     */
    private int inProgress;

    /**
     * 未开始项目数
     */
    private int notStarted;

    /**
     * 未开始-未排期项目数
     */
    private int totalNotScheduled;

    /**
     * 已完成-未排期项目数
     */
    private int completedNotScheduled;

    /**
     * 进行中-未排期项目数
     */
    private int inProgressNotScheduled;

    /**
     * 未开始-未排期项目数
     */
    private int notStartedNotScheduled;
}
