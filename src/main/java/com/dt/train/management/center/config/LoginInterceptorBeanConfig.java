package com.dt.train.management.center.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import com.dt.train.management.center.interceptor.CommonInterceptor;
import com.dt.train.management.center.mapper.TrainUserDOMapper;
import com.dt.train.management.center.utils.RedisUtil;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 6/2/21 3:46 PM
 */
@Configuration
public class LoginInterceptorBeanConfig {

    @Bean
    public InterceptorMvcConfig interceptorMvcConfig() {
        return new InterceptorMvcConfig();
    }

    @Bean
    public CommonInterceptor commonInterceptor(RedisUtil redisUtil, TrainUserDOMapper trainUserDOMapper) {
        return new CommonInterceptor(redisUtil, trainUserDOMapper);
    }

}
