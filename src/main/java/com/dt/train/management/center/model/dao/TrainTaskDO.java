package com.dt.train.management.center.model.dao;

import java.util.Date;

/**
 * 
 * 表名: train_task
 *
 * @mbg.generated
 */
@lombok.Data
public class TrainTaskDO {
    /**
     * 字段描述: 主键ID
     *
     * 字段名: train_task.id
     *
     * @mbg.generated
     */
    private Integer id;

    /**
     * 字段描述: 项目ID
     *
     * 字段名: train_task.project_id
     *
     * @mbg.generated
     */
    private Integer projectId;

    /**
     * 字段描述: 班次ID
     *
     * 字段名: train_task.course_id
     *
     * @mbg.generated
     */
    private Integer courseId;

    /**
     * 字段描述: 任务名称
     *
     * 字段名: train_task.task_name
     *
     * @mbg.generated
     */
    private String taskName;

    /**
     * 字段描述: 任务类型,1-带班考勤
     *
     * 字段名: train_task.task_type
     *
     * @mbg.generated
     */
    private Integer taskType;

    /**
     * 字段描述: 任务描述
     *
     * 字段名: train_task.task_desc
     *
     * @mbg.generated
     */
    private String taskDesc;

    /**
     * 字段描述: 是否已删除 0 否 1 是
     *
     * 字段名: train_task.invalid
     *
     * @mbg.generated
     */
    private Integer invalid;

    /**
     * 字段描述: 创建人
     *
     * 字段名: train_task.created_by
     *
     * @mbg.generated
     */
    private Long createdBy;

    /**
     * 字段描述: 更新人
     *
     * 字段名: train_task.updated_by
     *
     * @mbg.generated
     */
    private Long updatedBy;

    /**
     * 字段描述: 创建时间
     *
     * 字段名: train_task.gmt_create
     *
     * @mbg.generated
     */
    private Date gmtCreate;

    /**
     * 字段描述: 修改时间
     *
     * 字段名: train_task.gmt_modified
     *
     * @mbg.generated
     */
    private Date gmtModified;
}