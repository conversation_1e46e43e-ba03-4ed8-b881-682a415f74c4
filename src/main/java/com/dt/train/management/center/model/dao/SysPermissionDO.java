package com.dt.train.management.center.model.dao;

import java.util.Date;

/**
 * 
 * 表名: sys_permission
 *
 * @mbg.generated
 */
@lombok.Data
public class SysPermissionDO {
    /**
     * 字段描述: 权限ID
     *
     * 字段名: sys_permission.id
     *
     * @mbg.generated
     */
    private Integer id;

    /**
     * 字段描述: 父权限ID (0代表顶级)
     *
     * 字段名: sys_permission.parent_id
     *
     * @mbg.generated
     */
    private Integer parentId;

    /**
     * 字段描述: 权限名称
     *
     * 字段名: sys_permission.permission_name
     *
     * @mbg.generated
     */
    private String permissionName;

    /**
     * 字段描述: 菜单类型（MENU:菜单, PAGE:页面, FUNCTION:功能）
     *
     * 字段名: sys_permission.permission_type
     *
     * @mbg.generated
     */
    private String permissionType;

    /**
     * 字段描述: 权限标识 (例如: sys:user:list)
     *
     * 字段名: sys_permission.permission_key
     *
     * @mbg.generated
     */
    private String permissionKey;

    /**
     * 字段描述: 路由地址
     *
     * 字段名: sys_permission.p_path
     *
     * @mbg.generated
     */
    private String pPath;

    /**
     * 字段描述: 菜单图标
     *
     * 字段名: sys_permission.icon
     *
     * @mbg.generated
     */
    private String icon;

    /**
     * 字段描述: 显示排序
     *
     * 字段名: sys_permission.sort_order
     *
     * @mbg.generated
     */
    private Integer sortOrder;

    /**
     * 字段描述: 是否可选（0不可选 1可选）
     *
     * 字段名: sys_permission.can_select
     *
     * @mbg.generated
     */
    private Integer canSelect;

    /**
     * 字段描述: 备注信息
     *
     * 字段名: sys_permission.remark
     *
     * @mbg.generated
     */
    private String remark;

    /**
     * 字段描述: 创建时间
     *
     * 字段名: sys_permission.gmt_create
     *
     * @mbg.generated
     */
    private Date gmtCreate;

    /**
     * 字段描述: 修改时间
     *
     * 字段名: sys_permission.gmt_modified
     *
     * @mbg.generated
     */
    private Date gmtModified;
}