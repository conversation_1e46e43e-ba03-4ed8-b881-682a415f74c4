package com.dt.train.management.center.manager;

import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import com.alibaba.fastjson.JSON;
import com.dt.train.management.center.config.UserRequestContextHolder;
import com.dt.train.management.center.constant.CacheConstant;
import com.dt.train.management.center.enums.ManagementExceptionEnum;
import com.dt.train.management.center.enums.UserTypeEnum;
import com.dt.train.management.center.feign.client.UserCenterFeignClient;
import com.dt.train.management.center.feign.result.UserDTO;
import com.dt.train.management.center.mapper.TrainUserDOMapper;
import com.dt.train.management.center.model.dao.TrainUserDO;
import com.dt.train.management.center.model.dto.project.CourseUserRelationQueryDTO;
import com.dt.train.management.center.model.dto.user.TrainUserSaveDTO;
import com.dt.train.management.center.model.vo.user.TrainUserVO;
import com.dt.train.management.center.service.cache.DeptCacheService;
import com.dt.train.management.center.service.project.TrainCourseUserRelationService;
import com.dt.train.management.center.service.user.TrainUserService;
import com.dt.train.management.center.service.user.UserAndDepartmentMaintenanceService;
import com.dt.train.management.center.utils.ExceptionUtil;
import com.dt.train.management.center.utils.RedisUtil;

import lombok.extern.slf4j.Slf4j;

@Component
@Slf4j
public class TrainUserManager {

    private final TrainUserService trainUserService;

    private final TrainUserDOMapper trainUserDOMapper;

    private final TrainCourseUserRelationService trainCourseUserRelationService;

    private final RedisUtil redisUtil;

    private final UserAndDepartmentMaintenanceService userAndDepartmentMaintenanceService;
    private final UserCenterFeignClient userCenterFeignClient;
    private final DeptCacheService deptCacheService;

    public TrainUserManager(TrainUserService trainUserService, TrainUserDOMapper trainUserDOMapper,
                           TrainCourseUserRelationService trainCourseUserRelationService, 
                           RedisUtil redisUtil,
                           UserAndDepartmentMaintenanceService userAndDepartmentMaintenanceService,
                           UserCenterFeignClient userCenterFeignClient,
                           DeptCacheService deptCacheService) {
        this.trainUserService = trainUserService;
        this.trainUserDOMapper = trainUserDOMapper;
        this.trainCourseUserRelationService = trainCourseUserRelationService;
        this.redisUtil = redisUtil;
        this.userAndDepartmentMaintenanceService = userAndDepartmentMaintenanceService;
        this.userCenterFeignClient = userCenterFeignClient;
        this.deptCacheService = deptCacheService;
    }


    /**
     * 根据用户id获取用户信息
     *
     * @return TrainUserVO
     */
    public TrainUserVO getUserInfoByUserId(String userId) {
        if (StringUtils.isEmpty(userId)) {
            return new TrainUserVO();
        }
        Long userIdLong = Long.parseLong(userId);
        return trainUserService.getUserInfoByUserId(userIdLong);
    }

    /**
     * 逻辑删除用户账号
     * @param userId 用户ID
     */
    public void deleteUserLogical(String userId) {
        TrainUserDO user = precheck(userId);
        
        // 逻辑删除：设置 invalid=1
        TrainUserDO updateUser = new TrainUserDO();
        user.setUserId(updateUser.getUserId());
        user.setInvalid(1);
        trainUserDOMapper.updateByPrimaryKeySelective(user);
        redisUtil.del(CacheConstant.getTrainUserInfoKey(userId));
    }

    public TrainUserDO precheck(String userId) {
        // 类型转换
        Long userIdLong = Long.parseLong(userId);
        TrainUserDO user = trainUserDOMapper.selectByUserId(userIdLong);
        if (user == null) {
            throw ExceptionUtil.businessException(ManagementExceptionEnum.USER_NOT_EXIST);
        }
        
        // 检查排班记录
        CourseUserRelationQueryDTO queryDTO = new CourseUserRelationQueryDTO();
        queryDTO.setUserId(userIdLong);
        if (!trainCourseUserRelationService.listByQuery(queryDTO).isEmpty()) {
            throw ExceptionUtil.businessException(
                ManagementExceptionEnum.USER_DELETION_NOT_ALLOWED
            );
        }
        return user;
    }

    /**
     * 新增或编辑成员
     * @param saveDTO 保存DTO
     */
    public void saveOrUpdateUser(TrainUserSaveDTO saveDTO) {
        validateSaveDTO(saveDTO);

        TrainUserDO userDO = new TrainUserDO();

        if (saveDTO.getId() != null) {
            handleUpdateUser(saveDTO, userDO);
        } else {
            handleCreateUser(saveDTO, userDO);
        }

        setUserFields(saveDTO, userDO);
        saveUserToDB(userDO);
    }

    /**
     * 验证保存DTO
     */
    private void validateSaveDTO(TrainUserSaveDTO saveDTO) {
        if (saveDTO == null) {
            throw ExceptionUtil.businessException(ManagementExceptionEnum.BAD_PARAMETER);
        }
    }

    /**
     * 处理用户更新
     */
    private void handleUpdateUser(TrainUserSaveDTO saveDTO, TrainUserDO userDO) {
        TrainUserDO existUser = trainUserDOMapper.selectByPrimaryKey(saveDTO.getId());
        if (existUser == null) {
            throw ExceptionUtil.businessException(ManagementExceptionEnum.USER_NOT_EXIST);
        }

        validatePhoneForUpdate(saveDTO, existUser);

        userDO.setId(saveDTO.getId());
        userDO.setUserId(existUser.getUserId());
        userDO.setUpdatedBy(UserRequestContextHolder.getRequestUserId());

        clearUserCache(existUser.getUserId());
        updateUserDeptIfChanged(saveDTO, existUser);
    }

    /**
     * 处理用户创建
     */
    private void handleCreateUser(TrainUserSaveDTO saveDTO, TrainUserDO userDO) {
        Integer userType = getUserType(saveDTO);

        if (UserTypeEnum.PART_TIME.getCode().equals(userType)) {
            createPartTimeUser(userDO);
        } else {
            createFullTimeUser(saveDTO, userDO);
        }
    }

    /**
     * 验证更新时的手机号
     */
    private void validatePhoneForUpdate(TrainUserSaveDTO saveDTO, TrainUserDO existUser) {
        if (saveDTO.getPhone() != null) {
            List<TrainUserDO> phoneUsers = trainUserDOMapper.selectByPhones(
                java.util.Collections.singletonList(saveDTO.getPhone()));
            for (TrainUserDO phoneUser : phoneUsers) {
                if (!phoneUser.getId().equals(saveDTO.getId())) {
                    throw ExceptionUtil.businessException(ManagementExceptionEnum.BAD_PARAMETER, "手机号已被其他用户使用");
                }
            }
        }
    }

    /**
     * 清除用户缓存
     */
    private void clearUserCache(Long userId) {
        redisUtil.del(CacheConstant.getTrainUserInfoKey(String.valueOf(userId)));
    }

    /**
     * 如果部门发生变更，更新相关数据
     */
    private void updateUserDeptIfChanged(TrainUserSaveDTO saveDTO, TrainUserDO existUser) {
        if (!StringUtils.equals(saveDTO.getDeptId(), existUser.getDeptIds())) {
            String deptName = deptCacheService.getDeptName(saveDTO.getDeptId());
            trainCourseUserRelationService.updateUserDept(existUser.getUserId(), saveDTO.getDeptId(), deptName);
        }
    }

    /**
     * 获取用户类型
     */
    private Integer getUserType(TrainUserSaveDTO saveDTO) {
        return saveDTO.getUserType() != null ? saveDTO.getUserType() : UserTypeEnum.FULL_TIME.getCode();
    }

    /**
     * 创建兼职人员
     */
    private void createPartTimeUser(TrainUserDO userDO) {
        Long userId = userAndDepartmentMaintenanceService.queryUserIdByMobile(null);
        setNewUserDefaults(userDO, userId);
    }

    /**
     * 创建正式员工
     */
    private void createFullTimeUser(TrainUserSaveDTO saveDTO, TrainUserDO userDO) {
        if (saveDTO.getPhone() != null) {
            List<TrainUserDO> existUsers = trainUserDOMapper.selectAllByPhones(
                java.util.Collections.singletonList(saveDTO.getPhone()));

            if (!existUsers.isEmpty()) {
                handleExistingPhoneUser(saveDTO, userDO, existUsers.get(0));
            } else {
                createNewFullTimeUser(saveDTO, userDO);
            }
        } else {
            createNewFullTimeUser(saveDTO, userDO);
        }
    }

    /**
     * 处理已存在手机号的用户
     */
    private void handleExistingPhoneUser(TrainUserSaveDTO saveDTO, TrainUserDO userDO, TrainUserDO phoneUser) {
        if (phoneUser.getInvalid() != null && phoneUser.getInvalid() == 1) {
            // 恢复被删除的用户
            userDO.setId(phoneUser.getId());
            userDO.setUserId(phoneUser.getUserId());
            userDO.setInvalid(0);
            userDO.setUpdatedBy(UserRequestContextHolder.getRequestUserId());
            clearUserCache(phoneUser.getUserId());
            updateUserDeptIfChanged(saveDTO, phoneUser);
        } else {
            throw ExceptionUtil.businessException(ManagementExceptionEnum.BAD_PARAMETER, "手机号已存在");
        }
    }

    /**
     * 创建新的正式员工
     */
    private void createNewFullTimeUser(TrainUserSaveDTO saveDTO, TrainUserDO userDO) {
        Long userId = userAndDepartmentMaintenanceService.queryUserIdByMobile(saveDTO.getPhone());
        setNewUserDefaults(userDO, userId);
    }

    /**
     * 设置新用户的默认值
     */
    private void setNewUserDefaults(TrainUserDO userDO, Long userId) {
        userDO.setUserId(userId);
        userDO.setCreatedBy(UserRequestContextHolder.getRequestUserId());
        userDO.setUpdatedBy(UserRequestContextHolder.getRequestUserId());
        userDO.setCrmUserId("-1");
        userDO.setDingTalkUserId("-1");
        userDO.setUserStatus(0);
        userDO.setInvalid(0);
    }

    /**
     * 设置用户字段
     */
    private void setUserFields(TrainUserSaveDTO saveDTO, TrainUserDO userDO) {
        Integer userType = getUserType(saveDTO);
        userDO.setUserType(userType);

        // 设置用户名
        setUserName(saveDTO, userDO, userType);

        // 设置基本字段
        userDO.setPhone(saveDTO.getPhone());
        userDO.setRoleId(saveDTO.getRoleId());
        userDO.setDeptIds(saveDTO.getDeptId());
        userDO.setUserPosition(saveDTO.getUserPosition());
        userDO.setWorkPlace(saveDTO.getWorkPlace());

        // 设置管理区域
        setManageDeptIds(saveDTO, userDO, userType);
    }

    /**
     * 设置用户名
     */
    private void setUserName(TrainUserSaveDTO saveDTO, TrainUserDO userDO, Integer userType) {
        if (saveDTO.getId() == null && UserTypeEnum.PART_TIME.getCode().equals(userType) &&
            (saveDTO.getUserName() == null || saveDTO.getUserName().trim().isEmpty())) {
            // 兼职人员自动生成名字
            Integer partTimeCount = trainUserDOMapper.countPartTimeUsersByDeptId(saveDTO.getDeptId());
            String generatedName = "兼职人员" + (partTimeCount + 1);
            userDO.setUserName(generatedName);
        } else {
            userDO.setUserName(saveDTO.getUserName());
        }
    }

    /**
     * 设置管理区域ID
     */
    private void setManageDeptIds(TrainUserSaveDTO saveDTO, TrainUserDO userDO, Integer userType) {
        if (UserTypeEnum.PART_TIME.getCode().equals(userType)) {
            userDO.setManageDeptIds("");
        } else {
            userDO.setManageDeptIds(saveDTO.getManageDeptId());
        }
    }

    /**
     * 保存用户到数据库
     */
    private void saveUserToDB(TrainUserDO userDO) {
        if (userDO.getId() != null) {
            trainUserDOMapper.updateByPrimaryKeySelective(userDO);
        } else {
            trainUserDOMapper.insertSelective(userDO);
        }
    }

    /**
     * 根据userId去user-center查找用户，并修改对应user的userId字段和relation中的userId字段
     */
    @Transactional(rollbackFor = Exception.class)
    public TrainUserVO syncAndUpdateUserId(Long userId) {
        // 参数校验
        if (userId == null) {
            throw ExceptionUtil.businessException(ManagementExceptionEnum.BAD_PARAMETER, "用户ID不能为空");
        }
        // 1. 调用user-center获取最新用户数据
        UserDTO userDTO = userCenterFeignClient.getUserById(userId);
        log.info("从用户中心获取用户信息入参：userId:{},userCenterUserDTO:{}",userId, JSON.toJSONString(userDTO));
        if (userDTO == null) {
            throw ExceptionUtil.businessException(ManagementExceptionEnum.USER_CENTER_DATA_ERROR);
        }
        String phone = userDTO.getMobile();
        if(StringUtils.isEmpty(phone)){
            return null;
        }
        List<TrainUserDO> users = trainUserDOMapper.selectByPhones(java.util.Collections.singletonList(phone));
        if(users.isEmpty()){
            return null;
        }
        Long oldId = users.get(0).getUserId();
        // 2. 更新本地user表
        TrainUserVO userVO = updateLocalUser(users.get(0), userId);

        // 3. 更新relation表
        trainCourseUserRelationService.updateRelationUserId(oldId, userId);
        return userVO;
    }

    private TrainUserVO updateLocalUser(TrainUserDO oldUser, Long newUserId) {
        Long oldId = oldUser.getUserId();
        // 检查新ID是否已存在
        TrainUserDO existUser = trainUserDOMapper.selectByUserId(newUserId);
        if (existUser != null) {
            throw ExceptionUtil.businessException(ManagementExceptionEnum.USER_ID_CONFLICT);
        }

        // 执行更新
        oldUser.setUserId(newUserId);
        trainUserDOMapper.updateByPrimaryKeySelective(oldUser);
        redisUtil.del(CacheConstant.getTrainUserInfoKey(String.valueOf(oldId)));
        TrainUserVO trainUserVO = trainUserService.getUserInfoByUserId(newUserId);
        return trainUserVO;
    }

}
