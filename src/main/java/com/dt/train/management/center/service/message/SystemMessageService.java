package com.dt.train.management.center.service.message;

import java.time.LocalDate;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.dt.framework.business.dto.page.Pagination;
import com.dt.train.management.center.enums.MessageEnum;
import com.dt.train.management.center.mapper.SystemMessageDOMapper;
import com.dt.train.management.center.model.dao.SystemMessageDO;
import com.dt.train.management.center.model.dto.message.SystemMessageDTO;
import com.dt.train.management.center.model.dto.message.SystemMessageQueryDTO;
import com.dt.train.management.center.model.vo.message.SystemMessageVO;
import com.dt.train.management.center.utils.DateTimeUtils;

@Service
public class SystemMessageService {

    private final SystemMessageDOMapper systemMessageDOMapper;

    public SystemMessageService(SystemMessageDOMapper systemMessageDOMapper) {
        this.systemMessageDOMapper = systemMessageDOMapper;
    }

    /**
     * 根据用户ID查询系统消息
     * @param userId
     * @return
     */
    public Pagination<SystemMessageVO> queryPage(SystemMessageQueryDTO queryDTO) {
        long total = systemMessageDOMapper.count(queryDTO);
        Pagination<SystemMessageVO> pagination = new Pagination<>();
        pagination.setPageIndex(queryDTO.getPageIndex());
        pagination.setPageSize(queryDTO.getPageSize());
        pagination.setTotal(total);
        if (total == 0) {
            return pagination;
        }
        List<SystemMessageDO> systemMessageDOs = systemMessageDOMapper.query(queryDTO);
        List<SystemMessageVO> systemMessageVOs = systemMessageDOs.stream().map(SystemMessageVO::fromDO).collect(Collectors.toList());
        pagination.setRows(systemMessageVOs);
        return pagination;
    }

    /**
     * 根据用户ID查询未读系统消息数量
     * @param userId
     * @return
     */
    public long count(SystemMessageQueryDTO queryDTO) {
        return systemMessageDOMapper.count(queryDTO);
    }

    /**
     * 将系统消息标记为已读
     * @param messageId
     */
    public void markAsRead(Long messageId) {
        systemMessageDOMapper.markAsRead(messageId);
    }

    /**
     * 将某个用户的所有系统消息标记为已读
     * @param userId
     */
    public void markAllAsRead(Long userId) {
        systemMessageDOMapper.markAllAsReadByUserId(userId);
    }

    /**
     * 创建系统消息
     * @param userId
     * @param title
     * @param content
     * @param type
     * @param extInfo
     */
    public void createMessage(Long userId, String title, String content, MessageEnum type, JSONObject extInfo) {
        SystemMessageDO message = new SystemMessageDO();
        message.setUserId(userId);
        message.setMessageTitle(title);
        message.setMessageContent(content);
        message.setMessageType(type.getCode());
        message.setIsRead(0); // 0 表示未读
        message.setGmtCreate(new Date());
        message.setExtInfo(extInfo.toJSONString());
        systemMessageDOMapper.insert(message);
    }

    public void saveBatch(List<SystemMessageDTO> messages) {
        List<SystemMessageDO> systemMessageDOs = messages.stream().map(this::dto2do).collect(Collectors.toList());
        systemMessageDOMapper.insertBatch(systemMessageDOs);
    }

    private SystemMessageDO dto2do(SystemMessageDTO dto) {
        SystemMessageDO record = new SystemMessageDO();
        BeanUtils.copyProperties(dto, record);
        record.setExtInfo(JSON.toJSONString(dto.getExtInfo()));
        return record;
    }

    /**
     * 查询今日未读消息数量
     * @param userId
     * @return
     */
    public Integer unreadMessageCountToday(Long userId) {
        SystemMessageQueryDTO queryDTO = new SystemMessageQueryDTO();
        queryDTO.setUserId(userId);
        queryDTO.setIsRead(0);
        queryDTO.setStartDate(DateTimeUtils.getStartOfDay(LocalDate.now()));
        queryDTO.setEndDate(DateTimeUtils.getEndOfDay(LocalDate.now()));
        return (int) systemMessageDOMapper.count(queryDTO);
    }

    public Integer unreadMessageCountAll(Long userId) {
        SystemMessageQueryDTO queryDTO = new SystemMessageQueryDTO();
        queryDTO.setUserId(userId);
        queryDTO.setIsRead(0);
        return (int) systemMessageDOMapper.count(queryDTO);
    }

    public void deleteBatch(List<Long> messageIds) {
        systemMessageDOMapper.deleteBatch(messageIds);
    }

}
