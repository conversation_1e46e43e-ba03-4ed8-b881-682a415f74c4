package com.dt.train.management.center.web.dev;

import java.time.LocalDate;
import java.util.List;

import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.dt.framework.business.dto.web.common.WebResult;
import com.dt.train.management.center.manager.FXiaoKeManager;
import com.dt.train.management.center.manager.ProjectSyncManager;
import com.dt.train.management.center.manager.TrainCheckTaskManager;
import com.dt.train.management.center.model.request.fxiaoke.FxiaokeDataQueryOrder;
import com.dt.train.management.center.model.response.fxiaoke.FxiaokeDataObjectQueryResponse;

import io.swagger.annotations.Api;

@RestController
@RequestMapping("/dev/fxiaoke")
@Api(tags = "纷享销客开发接口", hidden = true)
public class FXiaoKeDevController {

    private final FXiaoKeManager fXiaoKeManager;

    private final ProjectSyncManager projectSyncManager;

    private final TrainCheckTaskManager trainCheckTaskManager;

    public FXiaoKeDevController(FXiaoKeManager fXiaoKeManager, ProjectSyncManager projectSyncManager, TrainCheckTaskManager trainCheckTaskManager) {
        this.fXiaoKeManager = fXiaoKeManager;
        this.projectSyncManager = projectSyncManager;
        this.trainCheckTaskManager = trainCheckTaskManager;
    }

    /**
     * 获取纷享销客token
     * 
     * @return String
     */
    @GetMapping("/token")
    public WebResult<String> getToken() {
        return WebResult.successData(fXiaoKeManager.getAccessToken());
    }
    
    /**
     * 刷新纷享销客token
     * 
     * @return String
     */
    @GetMapping("/refreshToken")
    public WebResult<String> refreshToken() {
        return WebResult.successData(fXiaoKeManager.refreshCorpAccessToken());
    }
    
    /**
     * 获取纷享销客项目列表
     * 
     * @param limit 每页记录数，默认20
     * @param offset 页码，从0开始，默认0
     * @param search 查询条件，可为空
     * @return 项目列表数据
     */
    @GetMapping("/projects")
    public WebResult<FxiaokeDataObjectQueryResponse> getProjectList(
            @RequestParam(required = false) Integer limit,
            @RequestParam(required = false) Integer offset,
            @RequestParam(required = false) List<FxiaokeDataQueryOrder> orders) {
        return WebResult.successData(fXiaoKeManager.getProjectList(limit, offset, null, orders));
    }

    /**
     * 全量同步项目数据
     * 
     * @return String
     */
    @GetMapping("/fullSyncProject")
    public WebResult<String> fullSyncProject() {
        return WebResult.successData(projectSyncManager.fullSyncProject(null, true, null));
    }

    /**
     * 前一天未打卡考勤处理
     * @param day
     */
    @PostMapping("/check/status")
    public void processCheckStatus(@RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate day) {
        if(day == null) {
            day = LocalDate.now().minusDays(1);
        }
        trainCheckTaskManager.processUnCheckStatus(day);
    }
}
