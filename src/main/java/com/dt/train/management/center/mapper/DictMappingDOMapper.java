package com.dt.train.management.center.mapper;

import com.dt.train.management.center.model.dao.DictMappingDO;
import org.apache.ibatis.annotations.Param;

import java.util.Set;

public interface DictMappingDOMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(DictMappingDO row);

    int insertSelective(DictMappingDO row);

    DictMappingDO selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(DictMappingDO row);

    int updateByPrimaryKey(DictMappingDO row);

    void deleteByDictTypeAndDictValueIn(@Param("dictType") String dictType, @Param("nameSet") Set<String> nameSet);
}