package com.dt.train.management.center.model.dto.project;

/**
 * 
 * 表名: train_course_area_relation
 *
 * @mbg.generated
 */
@lombok.Data
public class TrainCourseAreaRelationQueryDTO {

    /**
     * 字段描述: 项目ID
     *
     * 字段名: train_course_area_relation.project_id
     *
     * @mbg.generated
     */
    private Integer projectId;

    /**
     * 字段描述: 班次ID
     *
     * 字段名: train_course_area_relation.course_id
     *
     * @mbg.generated
     */
    private Integer courseId;

    /**
     * 字段描述: 省份id
     *
     * 字段名: train_course_area_relation.province_id
     *
     * @mbg.generated
     */
    private String provinceId;

    /**
     * 字段描述: 城市id
     *
     * 字段名: train_course_area_relation.city_id
     *
     * @mbg.generated
     */
    private String cityId;

    /**
     * 字段描述: 地区id
     *
     * 字段名: train_course_area_relation.area_id
     *
     * @mbg.generated
     */
    private String areaId;

}