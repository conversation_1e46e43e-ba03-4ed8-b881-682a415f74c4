package com.dt.train.management.center.web.dept;

import javax.annotation.Resource;

import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.dt.framework.business.dto.web.common.WebResult;
import com.dt.train.management.center.service.cache.DeptCacheService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

@RestController
@RequestMapping("/dept")
@Api(tags = "部门管理")
@Slf4j
public class TrainDeptCacheController {
    @Resource
    private DeptCacheService deptCacheService;

    @PostMapping("/refreshCache")
    @ApiOperation("刷新缓存")
    public WebResult<Boolean> refreshCache() {
        log.info("开始刷新缓存");
        deptCacheService.refreshDeptCache();
        return WebResult.successData(Boolean.TRUE);
    }

    @PostMapping("/clearCache")
    @ApiOperation("清空缓存")
    public WebResult<Boolean> clearCache() {
        log.info("开始清空缓存");
        deptCacheService.deleteDeptCache();
        return WebResult.successData(Boolean.TRUE);
    }


}
