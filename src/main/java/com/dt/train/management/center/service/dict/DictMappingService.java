package com.dt.train.management.center.service.dict;

import java.util.ArrayList;
import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.dt.train.management.center.constant.CacheConstant;
import com.dt.train.management.center.mapper.ex.DictMappingDOMapperEx;
import com.dt.train.management.center.model.dao.DictMappingDO;
import com.dt.train.management.center.model.vo.dict.DictMappingVO;
import com.dt.train.management.center.utils.RedisUtil;

@Service
public class DictMappingService {

    private final DictMappingDOMapperEx dictMappingDOMapperEx;

    private final RedisUtil redisUtil;

    public DictMappingService(DictMappingDOMapperEx dictMappingDOMapperEx, RedisUtil redisUtil) {
        this.dictMappingDOMapperEx = dictMappingDOMapperEx;
        this.redisUtil = redisUtil;
    }

    /**
     * 获取字典值
     * @param dictType 字典类型
     * @param thirdDictCode 第三方字典编码
     * @return 字典
     */
    public DictMappingVO getByTypeAndDictCode(String dictType, String dictCode) {
        if(StringUtils.isBlank(dictCode)){
            return null;
        }
        return getDictListByDictType(dictType).stream()
            .filter(dictMapping -> dictMapping.getDictCode().equals(dictCode))
            .findFirst()
            .orElse(null);
    }

    /**
     * 获取字典值
     * @param dictType 字典类型
     * @param thirdDictCode 第三方字典编码
     * @return 字典
     */
    public DictMappingVO getByTypeAndThirdDictCode(String dictType, String thirdDictCode) {
        if(StringUtils.isBlank(thirdDictCode)){
            return null;
        }
        return getDictListByDictType(dictType).stream()
            .filter(dictMapping -> dictMapping.getThirdDictCode().equals(thirdDictCode))
            .findFirst()
            .orElse(null);
    }

    /**
     * 获取字典列表
     * @param dictType 字典类型
     * @return 字典列表
     */
    public List<DictMappingVO> getDictListByDictType(String dictType) {
        String key = CacheConstant.getDictMappingKey(dictType);
        if (redisUtil.hasKey(key)) {
            return JSON.parseArray(redisUtil.get(key), DictMappingVO.class);
        }
        List<DictMappingDO> dictList = dictMappingDOMapperEx.selectByDictType(dictType);
        List<DictMappingVO> dictVOList = new ArrayList<>();
        for (DictMappingDO dictMappingDO : dictList) {
            DictMappingVO dictMappingVO = new DictMappingVO();
            BeanUtils.copyProperties(dictMappingDO, dictMappingVO);
            dictVOList.add(dictMappingVO);
        }
        redisUtil.set(key, JSON.toJSONString(dictVOList), 60 * 5);
        return dictVOList;
    }
}