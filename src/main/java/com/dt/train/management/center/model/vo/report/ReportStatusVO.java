package com.dt.train.management.center.model.vo.report;

import com.dt.train.management.center.export.enums.ReportStatus;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(description = "报表生成状态VO")
public class ReportStatusVO {

    @ApiModelProperty(value = "报表ID")
    private String reportId;

    @ApiModelProperty(value = "报表生成状态")
    private ReportStatus status;

    @ApiModelProperty(value = "生成进度")
    private Double progress;

    @ApiModelProperty(value = "提示信息")
    private String message;

    @ApiModelProperty(value = "下载链接")
    private String downloadUrl;
}
