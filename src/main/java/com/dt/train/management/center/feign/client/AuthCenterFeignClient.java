package com.dt.train.management.center.feign.client;

import java.util.List;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import com.dt.train.management.center.feign.param.SelectUserRequest;
import com.dt.train.management.center.feign.result.UserPassportDO;

@FeignClient(value = "auth-center")
public interface AuthCenterFeignClient {
    /**
     * 根据手机号批量取用户
     *
     * @param selectUserRequest
     */
    @PostMapping("/feign/user/selectUserByMobiles")
    List<UserPassportDO> selectUserByMobiles(@RequestBody @Validated SelectUserRequest selectUserRequest);


}
