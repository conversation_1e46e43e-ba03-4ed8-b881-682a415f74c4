package com.dt.train.management.center.model.dao;

import java.util.Date;

/**
 * 
 * 表名: train_course
 *
 * @mbg.generated
 */
@lombok.Data
public class TrainCourseDO {
    /**
     * 字段描述: 主键ID
     *
     * 字段名: train_course.id
     *
     * @mbg.generated
     */
    private Integer id;

    /**
     * 字段描述: 项目ID
     *
     * 字段名: train_course.project_id
     *
     * @mbg.generated
     */
    private Integer projectId;

    /**
     * 字段描述: 项目名称
     *
     * 字段名: train_course.project_name
     *
     * @mbg.generated
     */
    private String projectName;

    /**
     * 字段描述: 项目编码
     *
     * 字段名: train_course.project_code
     *
     * @mbg.generated
     */
    private String projectCode;

    /**
     * 字段描述: 班次名称
     *
     * 字段名: train_course.course_name
     *
     * @mbg.generated
     */
    private String courseName;

    /**
     * 字段描述: 班次状态,0-未开始，1-进行中，2-已完成
     *
     * 字段名: train_course.course_status
     *
     * @mbg.generated
     */
    private Integer courseStatus;

    /**
     * 字段描述: 班次形式，1-面授，2-远程直播，3-现场直播
     *
     * 字段名: train_course.course_form
     *
     * @mbg.generated
     */
    private Integer courseForm;

    /**
     * 字段描述: 班次类型
     *
     * 字段名: train_course.course_type
     *
     * @mbg.generated
     */
    private Integer courseType;

    /**
     * 字段描述: 班次人数
     *
     * 字段名: train_course.course_people_num
     *
     * @mbg.generated
     */
    private Integer coursePeopleNum;

    /**
     * 字段描述: 服务对象
     *
     * 字段名: train_course.service_object
     *
     * @mbg.generated
     */
    private String serviceObject;

    /**
     * 字段描述: 是否异地，0-否，1-是
     *
     * 字段名: train_course.is_remote
     *
     * @mbg.generated
     */
    private Integer isRemote;

    /**
     * 字段描述: 班次执行区域
     *
     * 字段名: train_course.course_area
     *
     * @mbg.generated
     */
    private String courseArea;

    /**
     * 字段描述: 班次执行区域id
     *
     * 字段名: train_course.course_area_id
     *
     * @mbg.generated
     */
    private Integer courseAreaId;

    /**
     * 字段描述: 是否填写计划说明，0-否，1-是
     *
     * 字段名: train_course.has_plan_desc
     *
     * @mbg.generated
     */
    private Boolean hasPlanDesc;

    /**
     * 字段描述: 是否填写执行进展说明，0-否，1-是
     *
     * 字段名: train_course.has_execution_progress
     *
     * @mbg.generated
     */
    private Boolean hasExecutionProgress;

    /**
     * 字段描述: 计划开始时间
     *
     * 字段名: train_course.plan_start_date
     *
     * @mbg.generated
     */
    private Date planStartDate;

    /**
     * 字段描述: 计划结束时间
     *
     * 字段名: train_course.plan_end_date
     *
     * @mbg.generated
     */
    private Date planEndDate;

    /**
     * 字段描述: 计划天数
     *
     * 字段名: train_course.plan_days
     *
     * @mbg.generated
     */
    private Float planDays;

    /**
     * 字段描述: 实际开始时间
     *
     * 字段名: train_course.real_start_date
     *
     * @mbg.generated
     */
    private Date realStartDate;

    /**
     * 字段描述: 实际结束时间
     *
     * 字段名: train_course.real_end_date
     *
     * @mbg.generated
     */
    private Date realEndDate;

    /**
     * 字段描述: 实际天数
     *
     * 字段名: train_course.real_days
     *
     * @mbg.generated
     */
    private Float realDays;

    /**
     * 字段描述: 总人天
     *
     * 字段名: train_course.total_person_days
     *
     * @mbg.generated
     */
    private Float totalPersonDays;

    /**
     * 字段描述: 是否已删除 0 否 1 是
     *
     * 字段名: train_course.invalid
     *
     * @mbg.generated
     */
    private Integer invalid;

    /**
     * 字段描述: 创建人
     *
     * 字段名: train_course.created_by
     *
     * @mbg.generated
     */
    private Long createdBy;

    /**
     * 字段描述: 更新人
     *
     * 字段名: train_course.updated_by
     *
     * @mbg.generated
     */
    private Long updatedBy;

    /**
     * 字段描述: 创建时间
     *
     * 字段名: train_course.gmt_create
     *
     * @mbg.generated
     */
    private Date gmtCreate;

    /**
     * 字段描述: 修改时间
     *
     * 字段名: train_course.gmt_modified
     *
     * @mbg.generated
     */
    private Date gmtModified;

    private String projectArea;
}