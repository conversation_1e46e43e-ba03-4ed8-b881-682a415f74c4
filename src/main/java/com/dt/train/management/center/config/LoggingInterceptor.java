package com.dt.train.management.center.config;

import java.io.IOException;
import java.nio.charset.StandardCharsets;

import org.springframework.http.HttpRequest;
import org.springframework.http.client.ClientHttpRequestExecution;
import org.springframework.http.client.ClientHttpRequestInterceptor;
import org.springframework.http.client.ClientHttpResponse;

import lombok.extern.slf4j.Slf4j;

@Slf4j
public class LoggingInterceptor implements ClientHttpRequestInterceptor {
    @Override
    public ClientHttpResponse intercept(
            HttpRequest request, byte[] body, ClientHttpRequestExecution execution) throws IOException {

        log.info("===== Request URI: {}", request.getURI());
        log.info("===== Request Method: {}", request.getMethod());
        log.info("===== Request Headers: {}", request.getHeaders());
        log.info("===== Request Body: {}", new String(body, StandardCharsets.UTF_8));

        return execution.execute(request, body);
    }
}