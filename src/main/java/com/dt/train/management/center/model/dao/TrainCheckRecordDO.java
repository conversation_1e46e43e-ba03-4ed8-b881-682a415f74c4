package com.dt.train.management.center.model.dao;

import java.util.Date;

/**
 * 
 * 表名: train_check_record
 *
 * @mbg.generated
 */
@lombok.Data
public class TrainCheckRecordDO {
    /**
     * 字段描述: 主键ID
     *
     * 字段名: train_check_record.id
     *
     * @mbg.generated
     */
    private Integer id;

    /**
     * 字段描述: 项目ID
     *
     * 字段名: train_check_record.project_id
     *
     * @mbg.generated
     */
    private Integer projectId;

    /**
     * 字段描述: 班次ID
     *
     * 字段名: train_check_record.course_id
     *
     * @mbg.generated
     */
    private Integer courseId;

    /**
     * 字段描述: 任务ID
     *
     * 字段名: train_check_record.task_id
     *
     * @mbg.generated
     */
    private Integer taskId;

    /**
     * 字段描述: 考勤任务ID
     *
     * 字段名: train_check_record.check_task_id
     *
     * @mbg.generated
     */
    private Integer checkTaskId;

    /**
     * 字段描述: 用户ID
     *
     * 字段名: train_check_record.user_id
     *
     * @mbg.generated
     */
    private Long userId;

    /**
     * 字段描述: 打卡类型,1-上班打卡，2-下班打卡，3-现场签到
     *
     * 字段名: train_check_record.check_type
     *
     * @mbg.generated
     */
    private Integer checkType;

    /**
     * 字段描述: 打卡时间
     *
     * 字段名: train_check_record.check_time
     *
     * @mbg.generated
     */
    private Date checkTime;

    /**
     * 字段描述: 打卡状态,1-正常打卡，2-迟到打卡,3-早退打卡
     *
     * 字段名: train_check_record.check_status
     *
     * @mbg.generated
     */
    private Integer checkStatus;

    /**
     * 字段描述: 打卡纬度
     *
     * 字段名: train_check_record.check_latitude
     *
     * @mbg.generated
     */
    private Double checkLatitude;

    /**
     * 字段描述: 打卡经度
     *
     * 字段名: train_check_record.check_longitude
     *
     * @mbg.generated
     */
    private Double checkLongitude;

    /**
     * 字段描述: 打卡地址
     *
     * 字段名: train_check_record.check_address
     *
     * @mbg.generated
     */
    private String checkAddress;

    /**
     * 字段描述: 打卡图片
     *
     * 字段名: train_check_record.check_image
     *
     * @mbg.generated
     */
    private String checkImage;

    /**
     * 字段描述: 是否已删除 0 否 1 是
     *
     * 字段名: train_check_record.invalid
     *
     * @mbg.generated
     */
    private Integer invalid;

    /**
     * 字段描述: 创建人
     *
     * 字段名: train_check_record.created_by
     *
     * @mbg.generated
     */
    private Long createdBy;

    /**
     * 字段描述: 更新人
     *
     * 字段名: train_check_record.updated_by
     *
     * @mbg.generated
     */
    private Long updatedBy;

    /**
     * 字段描述: 创建时间
     *
     * 字段名: train_check_record.gmt_create
     *
     * @mbg.generated
     */
    private Date gmtCreate;

    /**
     * 字段描述: 修改时间
     *
     * 字段名: train_check_record.gmt_modified
     *
     * @mbg.generated
     */
    private Date gmtModified;
}