package com.dt.train.management.center.export.models;

import com.dt.train.management.center.export.enums.ReportStatus;

import lombok.Data;

// 这个类在实际生产环境中通常会是一个JPA Entity，用于将报表元数据持久化到数据库
// 在这个示例中，它用于内存中存储报表状态和临时的内容，以便通过API查询和下载
@Data
public class ReportMetadata {
    private String reportId;
    private ReportStatus status;
    private String url; // 在真实应用中，这里会存储文件路径或OSS URL，而不是直接存储内容
    private String filename;
    private String contentType;
    private long creationTime; // 任务创建时间戳

    public ReportMetadata(String reportId, ReportStatus status, String url, String filename, String contentType, long creationTime) {
        this.reportId = reportId;
        this.status = status;
        this.url = url;
        this.filename = filename;
        this.contentType = contentType;
        this.creationTime = creationTime; // 设置创建时间
    }
}
