package com.dt.train.management.center.model.vo.check;

import java.util.List;

import com.dt.train.management.center.model.vo.project.TrainCourseVO;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class TrainCheckWorkbenchVO {

    @ApiModelProperty(value = "用户ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long userId;

    @ApiModelProperty(value = "总打卡天数")
    private Integer allCheckDay;

    @ApiModelProperty(value = "正常打卡天数")
    private Long normalCheckedDay;

    @ApiModelProperty(value = "异常打卡天数")
    private Long abnormalCheckedDay;

    @ApiModelProperty(value = "打卡任务列表")
    private List<TrainCheckTaskCalendarVO> checkTasks;

    @ApiModelProperty(value = "班次列表")
    private List<TrainCourseVO> courses;

    @ApiModelProperty(value = "总排班天数")
    private AllocatedDayVO allocatedDay;

}
