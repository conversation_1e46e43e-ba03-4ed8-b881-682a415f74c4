package com.dt.train.management.center.mapper;

import java.util.List;

import com.dt.train.management.center.model.dao.SysPermissionDO;

public interface SysPermissionDOMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(SysPermissionD<PERSON> row);

    int insertSelective(SysPermissionD<PERSON> row);

    SysPermissionDO selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(SysPermissionDO row);

    int updateByPrimaryKey(SysPermissionDO row);

    List<SysPermissionDO> selectAll();
}