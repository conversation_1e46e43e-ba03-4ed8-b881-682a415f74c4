package com.dt.train.management.center.utils;

import java.text.SimpleDateFormat;
import java.util.Date;

public class DateFormatUtils {

    private static final String DEFAULT_PATTERN = "yyyy-MM-dd";

    /**
     * 格式化日期
     * @param date
     * @param pattern
     * @return
     */
    public static String format(Date date, String pattern) {
        if (date == null) {
            return null;
        }
        if (pattern == null) {
            pattern = DEFAULT_PATTERN;
        }
        return new SimpleDateFormat(pattern).format(date);
    }

}
    
