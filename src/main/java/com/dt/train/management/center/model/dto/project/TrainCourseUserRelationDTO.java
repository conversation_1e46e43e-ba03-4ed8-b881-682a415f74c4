package com.dt.train.management.center.model.dto.project;

import com.dt.train.management.center.model.common.HalfDayTime;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 
 * 表名: train_course_user_relation
 *
 * @mbg.generated
 */
@Data
public class TrainCourseUserRelationDTO {
    /**
     * 字段描述: 主键ID
     *
     * 字段名: train_course_user_relation.id
     *
     * @mbg.generated
     */
    private Integer id;

    /**
     * 字段描述: 项目ID
     *
     * 字段名: train_course_user_relation.project_id
     *
     * @mbg.generated
     */
    private Integer projectId;

    /**
     * 字段描述: 班次ID
     *
     * 字段名: train_course_user_relation.course_id
     *
     * @mbg.generated
     */
    private Integer courseId;

    /**
     * 字段描述: 用户ID
     *
     * 字段名: train_course_user_relation.user_id
     *
     * @mbg.generated
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long userId;

    @ApiModelProperty(value = "用户名称")
    private String userName;

    @ApiModelProperty(value = "原始用户名称")
    private String originUserName;

    @ApiModelProperty(value = "成员类型 0-正式员工 1-兼职人员")
    private Integer userType;

    @ApiModelProperty(value = "成员类型名称")
    private String userTypeName;

    @ApiModelProperty(value = "用户部门ID")
    private String userDeptId;

    @ApiModelProperty(value = "用户部门名称")
    private String userDeptName;

    @ApiModelProperty(value = "用户状态，0-在职，1-离职")
    private Integer userStatus;

    /**
     * 字段描述: 关系类型,1-支援人，2-执行人
     *
     * 字段名: train_course_user_relation.relation_type
     *
     * @mbg.generated
     */
    private Integer relationType;

    /**
     * 字段描述: 开始时间
     *
     * 字段名: train_course_user_relation.start_date
     *
     * @mbg.generated
     */
    private HalfDayTime startDate;

    /**
     * 字段描述: 结束时间
     *
     * 字段名: train_course_user_relation.end_date
     *
     * @mbg.generated
     */
    private HalfDayTime endDate;
    
    @ApiModelProperty(value = "人天")
    private Float userDays;

    public Float getUserDays() {
        if(startDate == null || endDate == null){
            return 0f;
        }
        return startDate.daysBetweenWithHalf(endDate);
    }

    public Integer getFullDays() {
        if(startDate == null || endDate == null){
            return 0;
        }
        return (int)(endDate.getDate().toEpochDay() - startDate.getDate().toEpochDay()) + 1;
    }

    @ApiModelProperty(value = "是否超出实际执行日期")
    private Boolean isOverRealExecuteDate;

    @ApiModelProperty(value = "执行日期格式化（2025/3/10 上午-2025/3/10 下午;2025/3/11 上午-2025/3/11 下午）")
    private String executeDateStr;
}