package com.dt.train.management.center.mapper;

import com.dt.train.management.center.model.dao.TrainCourseDO;
import com.dt.train.management.center.model.dao.TrainCourseDOWithBLOBs;

public interface TrainCourseDOMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(TrainCourseDOWithBLOBs row);

    int insertSelective(TrainCourseDOWithBLOBs row);

    TrainCourseDOWithBLOBs selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(TrainCourseDOWithBLOBs row);

    int updateByPrimaryKeyWithBLOBs(TrainCourseDOWithBLOBs row);

    int updateByPrimaryKey(TrainCourseD<PERSON> row);
}