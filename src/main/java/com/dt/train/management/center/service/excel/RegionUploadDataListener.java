package com.dt.train.management.center.service.excel;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.fastjson.JSON;
import com.dt.framework.core.exception.BusinessException;
import com.dt.train.management.center.enums.ManagementExceptionEnum;
import com.dt.train.management.center.model.dto.tools.RegionUploadDataDTO;
import com.dt.train.management.center.model.exception.ManagementBusinessException;
import com.dt.train.management.center.utils.RedisUtil;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;

import java.util.List;


/**
 * @Author: ehan
 * @Date: 2022/3/3
 */
@Slf4j
public class RegionUploadDataListener extends AnalysisEventListener<RegionUploadDataDTO> {
    /**
     * 初始化队列默认50条
     */
    private static final int BATCH_COUNT = 100;
    private List<RegionUploadDataDTO> cachedDataList = Lists.newArrayListWithCapacity(BATCH_COUNT);


    /**
     * 这个每一条数据解析都会来调用
     *
     * @param uploadDataDTO
     * @param analysisContext
     */
    @Override
    public void invoke(RegionUploadDataDTO uploadDataDTO, AnalysisContext analysisContext) {
        long startTime = System.currentTimeMillis();
        log.info("批量区域组织导入解析到一条数据:{}", JSON.toJSONString(uploadDataDTO));
        int index = analysisContext.readRowHolder().getRowIndex();
        if(index == 1){
            Integer rowNumber = analysisContext.readSheetHolder().getApproximateTotalRowNumber();
            log.info("总行数：{}", rowNumber);
            if(rowNumber > 5000){
                log.info("区域组织批量导入超过5000行数据!");
                throw new ManagementBusinessException(ManagementExceptionEnum.BAD_PARAMETER, "区域组织批量导入超过5000行数据!");
            }
        }
        uploadDataDTO.setLineNum(index);
        long endTiem = System.currentTimeMillis();
        log.info("预约单批量导入解析到一条数据校验完成耗时:{}", endTiem - startTime);
        cachedDataList.add(uploadDataDTO);
    }


    /**
     * 所有数据解析完成了 都会来调用
     *
     * @param analysisContext
     */
    @Override
    public void doAfterAllAnalysed(AnalysisContext analysisContext) {

    }

}
