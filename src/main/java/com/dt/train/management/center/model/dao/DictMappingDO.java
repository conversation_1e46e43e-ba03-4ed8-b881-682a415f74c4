package com.dt.train.management.center.model.dao;

import java.util.Date;

/**
 * 
 * 表名: dict_mapping
 *
 * @mbg.generated
 */
@lombok.Data
public class DictMappingDO {
    /**
     * 字段描述: 主键ID
     *
     * 字段名: dict_mapping.id
     *
     * @mbg.generated
     */
    private Integer id;

    /**
     * 字段描述: 字典编码
     *
     * 字段名: dict_mapping.dict_code
     *
     * @mbg.generated
     */
    private String dictCode;

    /**
     * 字段描述: 第三方字典编码
     *
     * 字段名: dict_mapping.third_dict_code
     *
     * @mbg.generated
     */
    private String thirdDictCode;

    /**
     * 字段描述: 第三方系统
     *
     * 字段名: dict_mapping.third_system
     *
     * @mbg.generated
     */
    private String thirdSystem;

    /**
     * 字段描述: 字典类型
     *
     * 字段名: dict_mapping.dict_type
     *
     * @mbg.generated
     */
    private String dictType;

    /**
     * 字段描述: 字典值
     *
     * 字段名: dict_mapping.dict_value
     *
     * @mbg.generated
     */
    private String dictValue;

    /**
     * 字段描述: 字典描述
     *
     * 字段名: dict_mapping.dict_desc
     *
     * @mbg.generated
     */
    private String dictDesc;

    /**
     * 字段描述: 字典排序
     *
     * 字段名: dict_mapping.dict_order
     *
     * @mbg.generated
     */
    private Integer dictOrder;

    /**
     * 字段描述: 是否已删除 0 否 1 是
     *
     * 字段名: dict_mapping.invalid
     *
     * @mbg.generated
     */
    private Integer invalid;

    /**
     * 字段描述: 创建时间
     *
     * 字段名: dict_mapping.gmt_create
     *
     * @mbg.generated
     */
    private Date gmtCreate;

    /**
     * 字段描述: 修改时间
     *
     * 字段名: dict_mapping.gmt_modified
     *
     * @mbg.generated
     */
    private Date gmtModified;
}