package com.dt.train.management.center.model.dao;

import java.util.Date;

/**
 * 
 * 表名: sys_role_permission
 *
 * @mbg.generated
 */
@lombok.Data
public class SysRolePermissionDO {
    /**
     * 字段描述: 关联ID
     *
     * 字段名: sys_role_permission.id
     *
     * @mbg.generated
     */
    private Integer id;

    /**
     * 字段描述: 角色ID
     *
     * 字段名: sys_role_permission.role_id
     *
     * @mbg.generated
     */
    private Integer roleId;

    /**
     * 字段描述: 权限标识
     *
     * 字段名: sys_role_permission.permission_key
     *
     * @mbg.generated
     */
    private String permissionKey;

    /**
     * 字段描述: 是否失效（0正常 1失效）
     *
     * 字段名: sys_role_permission.invalid
     *
     * @mbg.generated
     */
    private Integer invalid;

    /**
     * 字段描述: 创建时间
     *
     * 字段名: sys_role_permission.gmt_create
     *
     * @mbg.generated
     */
    private Date gmtCreate;

    /**
     * 字段描述: 修改时间
     *
     * 字段名: sys_role_permission.gmt_modified
     *
     * @mbg.generated
     */
    private Date gmtModified;
}