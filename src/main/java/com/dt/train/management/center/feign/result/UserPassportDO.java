package com.dt.train.management.center.feign.result;

import java.util.Date;

/**
 * 表名: user_passport_a
 *
 * @Author: luyun<PERSON>i
 * @mbg.generated
 */
@lombok.Data
public class UserPassportDO {
    /**
     * 字段描述: 主键
     * <p>
     * 字段名: user_passport_a.id
     *
     * @mbg.generated
     */
    private Long id;

    /**
     * 字段描述: 登录名
     * <p>
     * 字段名: user_passport_a.login_name
     *
     * @mbg.generated
     */
    private String loginName;

    /**
     * 字段描述: 手机号
     * <p>
     * 字段名: user_passport_a.mobile
     *
     * @mbg.generated
     */
    private String mobile;

    /**
     * 字段描述: 邮箱
     * <p>
     * 字段名: user_passport_a.email
     *
     * @mbg.generated
     */
    private String email;

    /**
     * 字段描述: 微信ID
     * <p>
     * 字段名: user_passport_a.wechatid
     *
     * @mbg.generated
     */
    private String wechatid;

    /**
     * 微信APP OpenId
     */
    private String wechatOpenId;

    /**
     * 家长通服务号OpenId，用于H5 APP登录
     */
    private String parentFuwuhaoOpenId;

    /**
     * 字段描述: 微信小程序OpenID
     * <p>
     * 字段名: user_passport_a.wechatid
     *
     * @mbg.generated
     */
    private String openId;

    /**
     * 字段描述: 账号体系内角色
     * <p>
     * 字段名: user_passport_a.role
     *
     * @mbg.generated
     */
    private Integer role;
    /**
     * 字段描述: 密码
     * <p>
     * 字段名: user_passport_a.pwd
     *
     * @mbg.generated
     */
    private String pwd;

    /**
     * 字段描述: 密码加密方式: (md5)
     */
    private String pwdEncrypted;

    /**
     * 字段描述: 是否有效:(用于物理删除, 0:物理删除 1:有效)
     * <p>
     * 字段名: user_passport_a.yn
     *
     * @mbg.generated
     */
    private Boolean yn;

    /**
     * 字段描述: 创建时间
     * <p>
     * 字段名: user_passport_a.gmt_create
     *
     * @mbg.generated
     */
    private Date gmtCreate;

    /**
     * 字段描述: 更新时间
     * <p>
     * 字段名: user_passport_a.gmt_modified
     *
     * @mbg.generated
     */
    private Date gmtModified;

    /**
     * 集团用户ID
     */
    private String jtUserId;

    private String shjyUserId;

    private String zjfzUserId;

    private String eduyunUserId;

    private String realName;

    private Integer gender;

    private String icon;

    private String dtTenant;

    private String dtBrand;

    /**
     * 是否已(经过手机号)认证
     */
    private Boolean isAuth;

    /**
     * 昵称
     */
    private String nickName;


    public UserDetailsDTO initUserDetails() {
        UserDetailsDTO result = new UserDetailsDTO();
        result.setUserid(this.id == null ? null : String.valueOf(this.id));
        // OAuth用户名目前有两种情况: 1. 登录名/手机号登录 2. 手机号登录
        result.setUsername(this.loginName == null ? this.mobile : this.loginName);
        result.setMobile(this.mobile);
        result.setLoginName(this.loginName);
        result.setOpenId(this.openId);
        result.setWechatid(this.wechatid);
        result.setJtUserId(this.jtUserId);
        result.setShjyUserId(this.shjyUserId);
        result.setZjfzUserId(this.zjfzUserId);
        result.setEduyunUserId(this.eduyunUserId);
        result.setPwd(this.pwd);
        result.setYn(this.yn == null ? true : this.yn);
        result.setNickname(this.nickName);

        return result;
    }
}