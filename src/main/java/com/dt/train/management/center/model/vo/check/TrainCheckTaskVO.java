package com.dt.train.management.center.model.vo.check;

import java.util.Date;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;

import io.swagger.annotations.ApiModelProperty;

/**
 * 
 * 表名: train_check_task
 *
 * @mbg.generated
 */
@lombok.Data
public class TrainCheckTaskVO {
    
    @ApiModelProperty(value = "主键ID")
    private Integer id;

    /**
     * 字段描述: 项目ID
     *
     * 字段名: train_check_task.project_id
     *
     * @mbg.generated
     */
    @ApiModelProperty(value = "项目ID")
    private Integer projectId;

    /**
     * 字段描述: 班次ID
     *
     * 字段名: train_check_task.course_id
     *
     * @mbg.generated
     */
    @ApiModelProperty(value = "班次ID")
    private Integer courseId;

    /**
     * 字段描述: 任务ID
     *
     * 字段名: train_check_task.task_id
     *
     * @mbg.generated
     */
    @ApiModelProperty(value = "任务ID")
    private Integer taskId;

    /**
     * 字段描述: 用户ID
     *
     * 字段名: train_check_task.user_id
     *
     * @mbg.generated
     */
    @ApiModelProperty(value = "用户ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long userId;

    @ApiModelProperty(value = "用户姓名")
    private String userName;

    /**
     * 字段描述: 上班打卡状态,0-未打卡，1-正常打卡，2-迟到打卡,3-早退打卡
     *
     * 字段名: train_check_task.check_in_status
     *
     * @mbg.generated
     */
    @ApiModelProperty(value = "上班打卡状态,0-未打卡，1-正常打卡，2-迟到打卡,3-早退打卡")
    private Integer checkInStatus;

    /**
     * 字段描述: 下班打卡状态,0-未打卡，1-正常打卡，2-迟到打卡,3-早退打卡
     *
     * 字段名: train_check_task.check_out_status
     *
     * @mbg.generated
     */
    @ApiModelProperty(value = "下班打卡状态,0-未打卡，1-正常打卡，2-迟到打卡,3-早退打卡")
    private Integer checkOutStatus;

    /**
     * 字段描述: 现场签到状态,0-未签到，1-已签到
     *
     * 字段名: train_check_task.check_on_site_status
     *
     * @mbg.generated
     */
    @ApiModelProperty(value = "现场签到状态,0-未签到，1-已签到")
    private Integer checkOnSiteStatus;

    /**
     * 字段描述: 打卡状态,-1-待打卡,0-正常，1-异常
     *
     * 字段名: train_check_task.check_status
     *
     * @mbg.generated
     */
    @ApiModelProperty(value = "打卡状态,-1-待打卡,0-正常，1-异常")
    private Integer checkStatus;

    /**
     * 字段描述: 打卡日期
     *
     * 字段名: train_check_task.check_date
     *
     * @mbg.generated
     */
    private Date checkDate;

    @ApiModelProperty(value = "打卡维度,1-全天，2-上午，3-下午")
    private Integer checkDimension;

    @ApiModelProperty(value = "是否全天,0-否，1-是")
    private Integer isAllDay;
}