package com.dt.train.management.center.utils;

import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.util.Date;

/**
 * 日期时间工具类
 * 
 * <AUTHOR>
 */
public class DateTimeUtils {

    /**
     * 获取当天 00:00:00
     * 
     * @return
     */
    public static Date getStartOfDay(LocalDate date) {
        if (date == null) {
            date = LocalDate.now();
        }
        LocalDateTime startOfDay = date.atStartOfDay();
        return Date.from(startOfDay.atZone(ZoneId.systemDefault()).toInstant());
    }

    /**
     * 获取当天 23:59:59
     * 
     * @return
     */
    public static Date getEndOfDay(LocalDate date) {
        if (date == null) {
            date = LocalDate.now();
        }
        LocalDateTime endOfDay = date.atTime(LocalTime.MAX); // 等价于 23:59:59.999999999
        return Date.from(endOfDay.atZone(ZoneId.systemDefault()).toInstant());
    }

    /**
     * 获取当天 23:59:59
     * 
     * @return
     */
    public static Date getEndOfDay(Date date) {
        // Step 1: 将 Date 转为 Instant
        Instant instant = date.toInstant();
        
        // Step 2: 转为 LocalDate，保留系统默认时区
        ZoneId zone = ZoneId.systemDefault();
        LocalDate localDate = instant.atZone(zone).toLocalDate();
        
        // Step 3: 获取该日期的 23:59:59.999（纳秒精度）
        LocalDateTime endOfDay = localDate.atTime(LocalTime.MAX);
        
        // Step 4: 转为 Date 返回
        return Date.from(endOfDay.atZone(zone).toInstant());
    }

}
