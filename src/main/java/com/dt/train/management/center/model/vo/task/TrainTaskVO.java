package com.dt.train.management.center.model.vo.task;

import io.swagger.annotations.ApiModelProperty;

/**
 * 
 * 表名: train_task
 *
 * @mbg.generated
 */
@lombok.Data
public class TrainTaskVO {
    /**
     * 字段描述: 主键ID
     *
     * 字段名: train_task.id
     *
     * @mbg.generated
     */
    private Integer id;

    /**
     * 字段描述: 项目ID
     *
     * 字段名: train_task.project_id
     *
     * @mbg.generated
     */
    private Integer projectId;

    /**
     * 字段描述: 班次ID
     *
     * 字段名: train_task.course_id
     *
     * @mbg.generated
     */
    private Integer courseId;

    /**
     * 字段描述: 任务名称
     *
     * 字段名: train_task.task_name
     *
     * @mbg.generated
     */
    private String taskName;

    /**
     * 字段描述: 任务类型,1-带班考勤
     *
     * 字段名: train_task.task_type
     *
     * @mbg.generated
     */
    @ApiModelProperty(value = "任务类型,1-带班考勤")
    private Integer taskType;

    /**
     * 字段描述: 任务描述
     *
     * 字段名: train_task.task_desc
     *
     * @mbg.generated
     */
    private String taskDesc;

}