package com.dt.train.management.center.model.dto.project;

import lombok.Data;
import lombok.EqualsAndHashCode;
@Data
@EqualsAndHashCode(callSuper = true)
public class TrainProjectCRMDTO extends TrainProjectCRMStartDTO{

    /**
     * 字段描述: 主键ID
     *
     * 字段名: train_project.id
     *
     */
    private Integer id;

    /**
     * 字段描述: CRM项目ID
     *
     * 字段名: train_project.crm_project_id
     *
     */
    private String crmProjectId;

    /**
     * 字段描述: 项目名称
     *
     * 字段名: train_project.project_name
     *
     */
    private String projectName;

    /**
     * 字段描述: 项目编码
     *
     * 字段名: train_project.project_code
     *
     */
    private String projectCode;

    /**
     * 字段描述: 项目所属区域
     *
     * 字段名: train_project.project_area
     *
     */
    private String projectArea;

    /**
     * 字段描述: 项目所属区域id
     *
     *
     */
    private String projectAreaId;

    /**
     * 字段描述: 省份id
     *
     * 字段名: train_project.province_id
     *
     */
    private String provinceId;

    /**
     * 字段描述: 城市id
     *
     * 字段名: train_project.city_id
     *
     */
    private String cityId;

    /**
     * 字段描述: 地区id
     *
     * 字段名: train_project.area_id
     *
     */
    private String areaId;

    /**
     * 字段描述: 省份
     *
     * 字段名: train_project.province
     *
     */
    private String province;

    /**
     * 字段描述: 城市
     *
     * 字段名: train_project.city
     *
     */
    private String city;

    /**
     * 字段描述: 区县
     *
     * 字段名: train_project.area
     *
     */
    private String area;

    /**
     * 字段描述: 立项类型,0-正式立项，1-预立项
     *
     * 字段名: train_project.project_type
     *
     */
    private String projectType;

    /**
     * 字段描述: 项目级别,0-国家，1-省，2-市，3-区县，3-院校，4-学校，5-全国统招
     *
     * 字段名: train_project.project_level
     *
     */
    private String projectLevel;

    /**
     * 字段描述: 项目开始时间
     *
     * 字段名: train_project.start_date
     *
     */
    private Long startDate;

    /**
     * 字段描述: 项目结束时间
     *
     * 字段名: train_project.end_date
     *
     */
    private Long endDate;

    /**
     * 字段描述: 执行类型，0-一次集中，1-分段实施
     *
     * 字段名: train_project.action_type
     *
     */
    private String actionType;

    /**
     * 字段描述: 项目总学时
     *
     * 字段名: train_project.total_hours
     *
     */
    private String totalHours;

    /**
     * 字段描述: 项目经理id
     *
     * 字段名: train_project.project_manager_id
     *
     */
    private String projectManagerId;

    /**
     * 字段描述: 项目经理名称
     *
     * 字段名: train_project.project_manager_name
     *
     */
    private String projectManagerName;

    /**
     * 字段描述: 责任编辑
     *
     * 字段名: train_project.project_editor_id
     *
     */
    private String projectEditorId;

    /**
     * 字段描述: 责任编辑名称
     *
     * 字段名: train_project.project_editor_name
     *
     */
    private String projectEditorName;

    /**
     * 字段描述: 服务对象
     *
     * 字段名: train_project.service_object
     *
     */
    private String serviceObject;

    /**
     * 字段描述: 服务内容
     *
     * 字段名: train_project.service_content
     *
     */
    private String serviceContent;

    /**
     * 字段描述: 销售人员id
     *
     * 字段名: train_project.sales_user_id
     *
     */
    private Long salesUserId;

    /**
     * 字段描述: 销售人员名称
     *
     * 字段名: train_project.sales_user_name
     *
     */
    private String salesUserName;

    /**
     * 字段描述: 省区销售经理id
     *
     * 字段名: train_project.province_sales_manager_id
     *
     */
    private Long provinceSalesManagerId;

    /**
     * 字段描述: 省区销售经理名称
     *
     * 字段名: train_project.province_sales_manager_name
     *
     */
    private String provinceSalesManagerName;
    /**
     * 字段描述: 区域销售总监id
     *
     * 字段名: train_project.region_sales_director_id
     *
     */
    private Long regionSalesDirectorId;

    /**
     * 字段描述: 区域销售总监名称
     *
     * 字段名: train_project.region_sales_director_name
     *
     */
    private String regionSalesDirectorName;

    /**
     * 字段描述: 业务类型
     *
     * 字段名: train_project.business_type
     *
     */
    private String businessType;

    /**
     * 字段描述: 项目模式
     *
     * 字段名: train_project.project_mode
     *
     */
    private String projectMode;

    /**
     * 字段描述: 项目子模式
     *
     * 字段名: train_project.project_sub_mode
     *
     */
    private String projectSubMode;

    /**
     * 字段描述: 客户单位名称
     *
     * 字段名: train_project.customer_name
     *
     */
    private String customerName;

    /**
     * 字段描述: 同步类型
     *
     * 字段名: train_project.sync_type
     *
     */
    private Integer syncType;

    private String isRemote;

    /**
     * 字段描述: 执行总人数
     *
     * 字段名: train_project.total_user_count
     *
     */
    private Integer totalUserCount;


    /**
     * 字段描述: 执行对接人姓名
     *
     * 字段名: train_project.execute_contact_name
     *
     * @mbg.generated
     */
    private String executeContactName;

    /**
     * 字段描述: 执行对接人电话
     *
     * 字段名: train_project.execute_contact_phone
     *
     * @mbg.generated
     */
    private String executeContactPhone;

    /**
     * 字段描述: 执行对接人职务
     *
     * 字段名: train_project.execute_contact_job
     *
     * @mbg.generated
     */
    private String executeContactJob;
}
