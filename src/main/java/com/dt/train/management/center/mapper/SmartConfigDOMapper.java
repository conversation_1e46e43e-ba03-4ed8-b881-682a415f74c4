package com.dt.train.management.center.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.dt.train.management.center.model.dao.SmartConfigDO;

public interface SmartConfigDOMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(SmartConfigDO row);

    int insertSelective(SmartConfigDO row);

    SmartConfigDO selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(SmartConfigDO row);

    int updateByPrimaryKeyWithBLOBs(SmartConfigDO row);

    int updateByPrimaryKey(SmartConfigDO row);

    List<SmartConfigDO> listByKey(@Param("configKey") String configKey, @Param("secondKey") String secondKey);
}