package com.dt.train.management.center.model.request.fxiaoke;

import java.util.List;

import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.Data;

/**
 * 纷享销客自定义对象查询请求
 * 
 * <AUTHOR>
 * @since 2025-05-16
 */
@Data
public class FxiaokeDataObjectQueryRequest {

    /**
     * 企业访问令牌
     */
    private String corpAccessToken;

    /**
     * 企业ID
     */
    private String corpId;

    /**
     * 应用ID
     */
    private String currentOpenUserId;

    private QueryData data;

    @Data
    public static class QueryData {

        /**
         * 数据对象API名称
         */
        private String dataObjectApiName;

        /**
         * 查询条件
         */
        @JsonProperty("search_query_info")
        private SearchQueryInfo searchQueryInfo;


    }

    @Data
    public static class SearchQueryInfo {

         /**
         * 查询字段列表
         */
        // private List<String> fieldList;

        /**
         * 分页大小
         */
        private Integer limit;

        /**
         * 分页起始位置
         */
        private Integer offset;

        /**
         * 排序条件
         */
        private List<FxiaokeDataQueryOrder> orders;

        /**
         * 过滤条件
         */
        private List<FxiaokeDataQueryFilter> filters;
    }
    
}