package com.dt.train.management.center.serializer;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;

import java.io.IOException;

public class PhoneMaskSerializer extends JsonSerializer<String> {
    @Override
    public void serialize(String phone, JsonGenerator gen, SerializerProvider serializers) throws IOException {
        if (phone != null && phone.length() == 11) {
            String masked = phone.substring(0, 3) + "****" + phone.substring(7);
            gen.writeString(masked);
        } else {
            gen.writeString(phone);
        }
    }
}