package com.dt.train.management.center.web.dict;

import java.util.List;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.dt.framework.business.dto.web.common.WebResult;
import com.dt.train.management.center.model.vo.dict.DictMappingVO;
import com.dt.train.management.center.service.dict.DictMappingService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;

@RestController
@RequestMapping("/dict")
@Slf4j
@Api(tags = "字典映射")
public class DictMappingController {

    private final DictMappingService dictMappingService;

    public DictMappingController(DictMappingService dictMappingService) {
        this.dictMappingService = dictMappingService;
    }

    /**
     * 获取字典映射
     * @param dictType 字典类型
     * @return 字典映射
     */
    @ApiOperation("获取字典映射")
    @GetMapping("/mapping")
    public WebResult<List<DictMappingVO>> getMapping(
        @ApiParam("字典类型") @RequestParam String dictType) {
        return WebResult.successData(dictMappingService.getDictListByDictType(dictType));
    }

}