package com.dt.train.management.center.web.dev;

import java.time.LocalTime;

import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.dt.train.management.center.config.BusinessConfig;
import com.dt.train.management.center.enums.MessageEnum;
import com.dt.train.management.center.manager.TrainCheckTaskManager;

import io.swagger.annotations.Api;

@RestController
@RequestMapping("/dev/system/message")
@Api(tags = "系统消息开发接口", hidden = true)
public class SystemMessageDevController {

    private final BusinessConfig businessConfig;

    private final TrainCheckTaskManager trainCheckTaskManager;

    public SystemMessageDevController(BusinessConfig businessConfig, TrainCheckTaskManager trainCheckTaskManager) {
        this.businessConfig = businessConfig;
        this.trainCheckTaskManager = trainCheckTaskManager;
    }

    @PostMapping("")
    public void processMessage() {
        LocalTime now = LocalTime.now();
        if (now.isBefore(LocalTime.ofSecondOfDay(businessConfig.getCheckInTimeNormalSecond()))) {
            //上班签到提醒
            trainCheckTaskManager.processMessage(MessageEnum.SIGN_IN);
            return;
        }
        if(now.isBefore(LocalTime.ofSecondOfDay(businessConfig.getSignOnSiteAlertSecond()))) {
            //现场签到提醒
            trainCheckTaskManager.processMessage(MessageEnum.SIGN_ON_SITE);
            return;
        }
        if(now.isBefore(LocalTime.ofSecondOfDay(businessConfig.getCheckOutTimeNormalSecond()))) {
            //下班签到提醒
            trainCheckTaskManager.processMessage(MessageEnum.SIGN_OUT);
            return;
        }
    }

}
