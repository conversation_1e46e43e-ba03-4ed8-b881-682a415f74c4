package com.dt.train.management.center.model.vo.check;

import java.util.List;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class AllocatedDayVO {

    @ApiModelProperty(value = "总排班天数")
    private Float allocatedDay;

    @ApiModelProperty(value = "已带班天数")
    private Float pastDay;

    @ApiModelProperty(value = "待带班天数")
    private Float futureDay;

    @ApiModelProperty(value = "带班日期")
    private List<TrainCheckTaskCalendarVO> allocatedDates;
}
