package com.dt.train.management.center.job;

import com.dt.train.management.center.mapper.ex.TrainCourseDOMapperEx;
import com.dt.train.management.center.service.project.TrainCourseService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/feign/job/course")
@Slf4j
public class CourseSendMsgJob {

    private final TrainCourseService trainCourseService;

    public CourseSendMsgJob(TrainCourseService trainCourseService, TrainCourseDOMapperEx trainCourseDOMapperEx) {
        this.trainCourseService = trainCourseService;
    }


    /**
     * 班次开始提前一天提醒(每天9点执行一次)
     */
    @PostMapping("/start")
    public void sendMsgBeforeStart() {
        trainCourseService.sendMsgBeforeStart();
    }

}
