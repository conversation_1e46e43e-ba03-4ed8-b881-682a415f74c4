package com.dt.train.management.center.job;

import com.dt.train.management.center.service.user.UserAndDepartmentMaintenanceService;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@RestController
@RequestMapping("/feign/job/user")
@Slf4j
public class SynUserInfoJob {
    @Resource
    private UserAndDepartmentMaintenanceService userAndDepartmentMaintenanceService;

    @ApiOperation(value = "同步用户信息(每天凌晨1点执行一次)", notes = "同步用户信息")
    @PostMapping("/syncUserInfo")
    public void syncUserInfo() {
        userAndDepartmentMaintenanceService.syncUserStatusAndInfo();
    }
}
