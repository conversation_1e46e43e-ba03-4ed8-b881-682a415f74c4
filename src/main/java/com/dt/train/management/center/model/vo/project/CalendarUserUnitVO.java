package com.dt.train.management.center.model.vo.project;

import com.dt.train.management.center.model.common.HalfDayTime;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class CalendarUserUnitVO {


    /**
     * 字段描述: 项目ID
     *
     * 字段名: train_course_user_relation.project_id
     *
     * @mbg.generated
     */
    private Integer projectId;

    /**
     * 字段描述: 班次ID
     *
     * 字段名: train_course_user_relation.course_id
     *
     * @mbg.generated
     */
    private Integer courseId;

    /**
     * 字段描述: 用户ID
     *
     * 字段名: train_course_user_relation.user_id
     *
     * @mbg.generated
     */
    @ApiModelProperty(value = "用户ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long userId;

    @ApiModelProperty(value = "用户名称")
    private String userName;

    @ApiModelProperty(value = "原始用户名称")
    private String originUserName;

    @ApiModelProperty(value = "成员类型 0-正式员工 1-兼职人员")
    private Integer userType;

    @ApiModelProperty(value = "用户部门ID")
    private String userDeptId;

    @ApiModelProperty(value = "用户部门名称")
    private String userDeptName;

    @ApiModelProperty(value = "用户状态，0-在职，1-离职")
    private Integer userStatus;

    /**
     * 字段描述: 关系类型,1-支援人，2-执行人
     *
     * 字段名: train_course_user_relation.relation_type
     *
     * @mbg.generated
     */
    private Integer relationType;

    @ApiModelProperty(value = "日期")
    private HalfDayTime atDate;

}
