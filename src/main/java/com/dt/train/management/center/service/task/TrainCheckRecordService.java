package com.dt.train.management.center.service.task;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import com.dt.train.management.center.config.UserRequestContextHolder;
import com.dt.train.management.center.enums.CheckTypeEnum;
import com.dt.train.management.center.mapper.TrainCheckRecordDOMapper;
import com.dt.train.management.center.model.dao.TrainCheckRecordDO;
import com.dt.train.management.center.model.dto.task.TrainCheckRecordQueryDTO;
import com.dt.train.management.center.model.dto.task.TrainCheckRecordSaveDTO;
import com.dt.train.management.center.model.vo.check.TrainCheckRecordVO;

@Service
public class TrainCheckRecordService {

    private final TrainCheckRecordDOMapper trainCheckRecordDOMapper;

    public TrainCheckRecordService(TrainCheckRecordDOMapper trainCheckRecordDOMapper) {
        this.trainCheckRecordDOMapper = trainCheckRecordDOMapper;
    }

    /**
     * 保存打卡记录
     * 
     * @param trainCheckRecordSaveDTO 
     * @return
     */
    public TrainCheckRecordVO save(TrainCheckRecordSaveDTO trainCheckRecordSaveDTO) {
        TrainCheckRecordDO trainCheckRecordDO = saveDTO2DO(trainCheckRecordSaveDTO);
        trainCheckRecordDOMapper.insertSelective(trainCheckRecordDO);
        return do2VO(trainCheckRecordDO);
    }

    private TrainCheckRecordDO saveDTO2DO(TrainCheckRecordSaveDTO trainCheckRecordSaveDTO) {
        TrainCheckRecordDO trainCheckRecordDO = new TrainCheckRecordDO();
        BeanUtils.copyProperties(trainCheckRecordSaveDTO, trainCheckRecordDO);
        trainCheckRecordDO.setCreatedBy(UserRequestContextHolder.getRequestUserId());
        trainCheckRecordDO.setUpdatedBy(UserRequestContextHolder.getRequestUserId());
        return trainCheckRecordDO;
    }

    private TrainCheckRecordVO do2VO(TrainCheckRecordDO trainCheckRecordDO) {
        TrainCheckRecordVO trainCheckRecordVO = new TrainCheckRecordVO();
        BeanUtils.copyProperties(trainCheckRecordDO, trainCheckRecordVO);
        return trainCheckRecordVO;
    }

    /**
     * 根据查询条件查询打卡记录
     * 
     * @param queryDTO
     * @return
     */
    public List<TrainCheckRecordVO> listByQuery(TrainCheckRecordQueryDTO queryDTO) {
        List<TrainCheckRecordDO> trainCheckRecordDOs = trainCheckRecordDOMapper.selectByQuery(queryDTO);
        List<TrainCheckRecordVO> trainCheckRecordVOs = trainCheckRecordDOs.stream().map(this::do2VO).collect(Collectors.toList());
        //每个课程每天第一次上班打卡为有效，最后一次下班打卡为有效，所有的现场签到都是有效
        Map<Integer, List<TrainCheckRecordVO>> trainCheckRecordDOsMap = trainCheckRecordVOs.stream().collect(Collectors.groupingBy(TrainCheckRecordVO::getCourseId));
        trainCheckRecordDOsMap.forEach((courseId, vos) -> {
            Map<Integer, List<TrainCheckRecordVO>> checkTypeMap = vos.stream().collect(Collectors.groupingBy(TrainCheckRecordVO::getCheckType));
            if(checkTypeMap.containsKey(CheckTypeEnum.CHECK_IN.getCode())) {
                TrainCheckRecordVO checkInVO = checkTypeMap.get(CheckTypeEnum.CHECK_IN.getCode()).get(0);
                checkInVO.setIsValid(1);
            }
            if(checkTypeMap.containsKey(CheckTypeEnum.CHECK_OUT.getCode())) {
                TrainCheckRecordVO checkOutVO = checkTypeMap.get(CheckTypeEnum.CHECK_OUT.getCode()).get(checkTypeMap.get(CheckTypeEnum.CHECK_OUT.getCode()).size() - 1);
                checkOutVO.setIsValid(1);
            }
            if(checkTypeMap.containsKey(CheckTypeEnum.SIGN_IN.getCode())) {
                checkTypeMap.get(CheckTypeEnum.SIGN_IN.getCode()).forEach(vo -> vo.setIsValid(1));
            }
        });
        return trainCheckRecordVOs;
    }

}
