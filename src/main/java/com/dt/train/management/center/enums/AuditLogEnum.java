package com.dt.train.management.center.enums;

import lombok.Getter;

@Getter
public enum AuditLogEnum {

    MODIFY_CHECK_STATUS("MODIFY_CHECK_STATUS", "修改打卡状态", "#{#checkTaskId}", "#{#_result.userId}"
    , "将#{#_result.userName}#{#formatDate(#_result.checkDate,'yyyy-MM-dd')}的打卡状态由#{T(com.dt.train.management.center.enums.CheckSummaryStatusEnum).fromCode(#_result.checkStatus).desc}变更为#{T(com.dt.train.management.center.enums.CheckSummaryStatusEnum).fromCode(#checkStatus).desc}", BusinessModuleEnum.CHECK_TASK),
    MODIFY_CHECK_DETAIL_STATUS("MODIFY_CHECK_DETAIL_STATUS", "修改上下班打卡状态", "#{#checkTaskId}", "#{#_result.userId}"
    , "将#{#_result.userName}#{#formatDate(#_result.checkDate,'yyyy-MM-dd')}的#{#checkType.desc}状态由#{T(com.dt.train.management.center.enums.CheckStatusEnum).fromCode(#_result.checkOldStatus).desc}变更为#{T(com.dt.train.management.center.enums.CheckStatusEnum).fromCode(1).desc}", BusinessModuleEnum.CHECK_TASK);

    private String code;

    private String name;

    private String businessIdTemplate;

    private String targetUserIdTemplate;

    /**
     * 日志内容模板 (例如: "将用户 { {#userId} } 的状态修改为 { {#status} }")
     * 支持SpEL表达式从方法参数中取值
     * SpEL表达式格式: {#参数名} 或 {#参数对象.属性}
     */
    private String contentTemplate;

    private BusinessModuleEnum businessModule;

    AuditLogEnum(String code, String name, String businessIdTemplate, String targetUserIdTemplate, String contentTemplate, BusinessModuleEnum businessModule) {
        this.code = code;
        this.name = name;
        this.businessIdTemplate = businessIdTemplate;
        this.targetUserIdTemplate = targetUserIdTemplate;
        this.contentTemplate = contentTemplate;
        this.businessModule = businessModule;
    }
}
