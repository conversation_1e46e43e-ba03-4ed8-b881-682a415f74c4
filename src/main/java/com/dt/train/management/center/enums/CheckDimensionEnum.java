package com.dt.train.management.center.enums;

import java.util.Arrays;
import java.util.List;

import lombok.Getter;

@Getter
public enum CheckDimensionEnum {

    FULL_DAY(1, "全天", 1),
    MORNING(2, "上午", 0),
    AFTERNOON(3, "下午", 0);

    private final Integer code;
    private final String desc;

    /**
     * 1-全天，0-半天
     */
    private final Integer dimension;

    CheckDimensionEnum(Integer code, String desc, Integer dimension) {
        this.code = code;
        this.desc = desc;
        this.dimension = dimension;
    }

    /**
     * 根据半天列表获取打卡维度
     * @param halfDayList
     * @return
     */
    public static CheckDimensionEnum getByHalfDay(List<HalfDayEnum> halfDayList) {
        if (halfDayList.size() == 1) {
            HalfDayEnum halfDay = halfDayList.get(0);
            if (halfDay.equals(HalfDayEnum.AM)) {
                return CheckDimensionEnum.MORNING;
            } else {
                return CheckDimensionEnum.AFTERNOON;
            }
        } else {
            return CheckDimensionEnum.FULL_DAY;
        }
    }

    public static CheckDimensionEnum getByCode(Integer code) {
        return Arrays.stream(CheckDimensionEnum.values())
            .filter(checkDimensionEnum -> checkDimensionEnum.getCode().equals(code))
            .findFirst()
            .orElse(FULL_DAY);
    }

}
