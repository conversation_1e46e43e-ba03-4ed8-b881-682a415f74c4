package com.dt.train.management.center.service.config;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import org.springframework.beans.BeanUtils;
import org.springframework.cache.annotation.CacheConfig;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.cache.annotation.Caching;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import com.dt.train.management.center.constant.CacheConstant;
import com.dt.train.management.center.mapper.SmartConfigDOMapper;
import com.dt.train.management.center.model.dao.SmartConfigDO;
import com.dt.train.management.center.model.dto.config.SmartConfigDTO;

@Service
@CacheConfig(cacheNames = CacheConstant.CONFIG_KEY)
public class SmartConfigService {

    private final SmartConfigDOMapper smartConfigDOMapper;

    public SmartConfigService(SmartConfigDOMapper smartConfigDOMapper) {
        this.smartConfigDOMapper = smartConfigDOMapper;
    }


    @Cacheable(key = "#key")
    public List<SmartConfigDTO> getConfig(String key){
        List<SmartConfigDO> dos = smartConfigDOMapper.listByKey(key, null);
        if(CollectionUtils.isEmpty(dos)){
            return new ArrayList<>();
        }
        return dos.stream().map(configDo -> {
            SmartConfigDTO configDTO = new SmartConfigDTO();
            BeanUtils.copyProperties(configDo, configDTO);
            return configDTO;
        }).collect(Collectors.toList());
    }


    @Caching(evict = {
            @CacheEvict(key = "#configDTO.configKey"),
            @CacheEvict(key = "#configDTO.configKey + ':' + #configDTO.secondKey")
    })
    public void saveOrUpdate(SmartConfigDTO configDTO){
        SmartConfigDO configDO = new SmartConfigDO();
        BeanUtils.copyProperties(configDTO, configDO);
        if(configDO.getId() == null){
            smartConfigDOMapper.insertSelective(configDO);
        }else {
            smartConfigDOMapper.updateByPrimaryKeySelective(configDO);
        }
    }

    @Caching(evict = {
            @CacheEvict(key = "#configDTO.configKey"),
            @CacheEvict(key = "#configDTO.configKey + ':' + #configDTO.secondKey")
    })
    public void saveOrUpdateByKey(SmartConfigDTO configDTO){
        List<SmartConfigDO> dos = smartConfigDOMapper.listByKey(configDTO.getConfigKey(), configDTO.getSecondKey());
        if(CollectionUtils.isEmpty(dos)){
            SmartConfigDO configDO = new SmartConfigDO();
            BeanUtils.copyProperties(configDTO, configDO);
            smartConfigDOMapper.insertSelective(configDO);
        }else {
            SmartConfigDO oldConfigDO = dos.get(0);
            configDTO.setId(oldConfigDO.getId());
            BeanUtils.copyProperties(configDTO, oldConfigDO);
            smartConfigDOMapper.updateByPrimaryKeySelective(oldConfigDO);
        }
    }

}
