package com.dt.train.management.center.model.dao;

import java.util.Date;

/**
 * 
 * 表名: system_message
 *
 * @mbg.generated
 */
@lombok.Data
public class SystemMessageDO {
    /**
     * 字段描述: 主键ID，仅做主键，业务字段统一使用user_id
     *
     * 字段名: system_message.id
     *
     * @mbg.generated
     */
    private Integer id;

    /**
     * 字段描述: 用户中心-用户ID
     *
     * 字段名: system_message.user_id
     *
     * @mbg.generated
     */
    private Long userId;

    /**
     * 字段描述: 消息标题
     *
     * 字段名: system_message.message_title
     *
     * @mbg.generated
     */
    private String messageTitle;

    /**
     * 字段描述: 消息内容
     *
     * 字段名: system_message.message_content
     *
     * @mbg.generated
     */
    private String messageContent;

    /**
     * 字段描述: 消息类型
     *
     * 字段名: system_message.message_type
     *
     * @mbg.generated
     */
    private Integer messageType;

    /**
     * 字段描述: 是否已读，0-否，1-是
     *
     * 字段名: system_message.is_read
     *
     * @mbg.generated
     */
    private Integer isRead;

    /**
     * 字段描述: 业务ID
     *
     * 字段名: system_message.business_id
     *
     * @mbg.generated
     */
    private Long businessId;

    /**
     * 字段描述: 业务类型, 1-课程
     *
     * 字段名: system_message.business_type
     *
     * @mbg.generated
     */
    private Integer businessType;

    /**
     * 字段描述: 扩展信息
     *
     * 字段名: system_message.ext_info
     *
     * @mbg.generated
     */
    private String extInfo;

    /**
     * 字段描述: 是否无效，0-否，1-是
     *
     * 字段名: system_message.invalid
     *
     * @mbg.generated
     */
    private Integer invalid;

    /**
     * 字段描述: 创建时间
     *
     * 字段名: system_message.gmt_create
     *
     * @mbg.generated
     */
    private Date gmtCreate;
}