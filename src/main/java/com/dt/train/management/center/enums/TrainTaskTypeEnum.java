package com.dt.train.management.center.enums;

import lombok.Getter;

@Getter
public enum TrainTaskTypeEnum {

    CHECK_IN(1, "带班考勤", "班次执行人或支援人需在班次下进行打卡完成带班考勤");

    private final Integer code;
    private final String name;
    private final String desc;

    TrainTaskTypeEnum(Integer code, String name, String desc) {
        this.code = code;
        this.name = name;
        this.desc = desc;
    }
}
