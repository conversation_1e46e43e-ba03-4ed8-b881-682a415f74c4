package com.dt.train.management.center.service.project;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.alibaba.excel.util.DateUtils;
import com.dt.framework.business.dto.page.Pagination;
import com.dt.train.management.center.enums.CheckDimensionEnum;
import com.dt.train.management.center.enums.CheckSummaryStatusEnum;
import com.dt.train.management.center.enums.HalfDayEnum;
import com.dt.train.management.center.enums.ManagementExceptionEnum;
import com.dt.train.management.center.enums.MessageEnum;
import com.dt.train.management.center.enums.ProjectStatusEnum;
import com.dt.train.management.center.enums.TrainCourseAreaRelationTypeEnum;
import com.dt.train.management.center.enums.TrainCourseUserRelationTypeEnum;
import com.dt.train.management.center.manager.UserStatusManager;
import com.dt.train.management.center.mapper.ex.TrainProjectDOMapperEx;
import com.dt.train.management.center.model.common.HalfDayTime;
import com.dt.train.management.center.model.common.HalfDayTimeWithUser;
import com.dt.train.management.center.model.dao.TrainProjectDO;
import com.dt.train.management.center.model.dto.dingding.DingTalkSendDTO;
import com.dt.train.management.center.model.dto.message.SystemMessageDTO;
import com.dt.train.management.center.model.dto.project.TrainCourseAreaRelationDTO;
import com.dt.train.management.center.model.dto.project.TrainCourseUserRelationDTO;
import com.dt.train.management.center.model.dto.project.TrainProjectDTO;
import com.dt.train.management.center.model.dto.project.TrainProjectQueryDTO;
import com.dt.train.management.center.model.dto.task.TrainCheckTaskDTO;
import com.dt.train.management.center.model.dto.task.TrainCheckTaskQueryDTO;
import com.dt.train.management.center.model.dto.user.TrainUserQueryDTO;
import com.dt.train.management.center.model.vo.dict.DictMappingVO;
import com.dt.train.management.center.model.vo.project.ProjectStatusSummaryVO;
import com.dt.train.management.center.model.vo.project.TrainCourseVO;
import com.dt.train.management.center.model.vo.project.TrainProjectVO;
import com.dt.train.management.center.model.vo.project.TrainProjectWithCourseVO;
import com.dt.train.management.center.model.vo.user.TrainUserVO;
import com.dt.train.management.center.service.dict.DictMappingService;
import com.dt.train.management.center.service.dingding.DingTalkV1Service;
import com.dt.train.management.center.service.message.SystemMessageService;
import com.dt.train.management.center.service.task.TrainCheckTaskService;
import com.dt.train.management.center.service.user.TrainUserService;
import com.dt.train.management.center.utils.ExceptionUtil;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
public class TrainProjectService {

    private final DictMappingService dictMappingService;

    private final TrainCourseService trainCourseService;

    private final TrainProjectDOMapperEx trainProjectMapperEx;

    private final TrainCourseAreaRelationService trainCourseAreaRelationService;

    private final TrainCourseUserRelationService trainCourseUserRelationService;

    private final TrainCheckTaskService trainCheckTaskService;

    private final UserStatusManager userStatusManager;

    private final DingTalkV1Service dingTalkV1Service;

    private final TrainUserService trainUserService;

    private final SystemMessageService systemMessageService;


    public TrainProjectService(TrainProjectDOMapperEx trainProjectMapperEx, DictMappingService dictMappingService, TrainCourseService trainCourseService, TrainCourseAreaRelationService trainCourseAreaRelationService,
                               TrainCourseUserRelationService trainCourseUserRelationService, TrainCheckTaskService trainCheckTaskService, UserStatusManager userStatusManager, DingTalkV1Service dingTalkV1Service,
                               TrainUserService trainUserService, SystemMessageService systemMessageService) {
        this.trainProjectMapperEx = trainProjectMapperEx;
        this.dictMappingService = dictMappingService;
        this.trainCourseService = trainCourseService;
        this.trainCourseAreaRelationService = trainCourseAreaRelationService;
        this.trainCourseUserRelationService = trainCourseUserRelationService;
        this.trainCheckTaskService = trainCheckTaskService;
        this.userStatusManager = userStatusManager;
        this.dingTalkV1Service = dingTalkV1Service;
        this.trainUserService = trainUserService;
        this.systemMessageService = systemMessageService;
    }

    /**
     * 根据 crmId更新或保存项目
     * 
     * @param trainProjectDTO
     */
    @Transactional(rollbackFor = Exception.class)
    public void saveOrUpdateTrainProjectByCrmId(TrainProjectDTO trainProjectDTO) {
        // 更新项目状态
        trainProjectDTO.setProjectStatus(getProjectStatus(trainProjectDTO).getValue());
        TrainProjectDO trainProjectDO = trainProjectMapperEx.selectByCrmId(trainProjectDTO.getCrmProjectId());
        if (trainProjectDO == null) {
            trainProjectDO = new TrainProjectDO();
            BeanUtils.copyProperties(trainProjectDTO, trainProjectDO);
            trainProjectMapperEx.insertSelective(trainProjectDO);
        } else {
            // 检查areaId是否变更
            boolean areaIdChanged = trainProjectDTO.getProjectAreaId() != null 
                && !trainProjectDTO.getProjectAreaId().equals(trainProjectDO.getProjectAreaId());
            
            trainProjectDTO.setId(trainProjectDO.getId());
            trainProjectDTO.setSyncType(trainProjectDO.getSyncType());
            trainProjectDTO.setCreatedBy(trainProjectDO.getCreatedBy());
            trainProjectDTO.setUpdatedBy(trainProjectDO.getUpdatedBy());
            trainProjectDTO.setGmtCreate(trainProjectDO.getGmtCreate());
            trainProjectDTO.setGmtModified(new Date());
            trainProjectDTO.setInvalid(0);
            if(trainProjectDTO.getIsRemote() == null){
                trainProjectDTO.setIsRemote(-1);
            }
            if(trainProjectDTO.getActionType() == null){
                trainProjectDTO.setActionType(-1);
            }
            if(trainProjectDTO.getServiceObject() == null){
                trainProjectDTO.setServiceObject("");
            }
            if(trainProjectDTO.getServiceContent() == null){
                trainProjectDTO.setServiceContent("");
            }
            BeanUtils.copyProperties(trainProjectDTO, trainProjectDO);
            trainProjectMapperEx.updateByPrimaryKey(trainProjectDO);

            // 如果areaId变更，更新关联表
            if (areaIdChanged) {
                updateCourseUserRelationAreaId(trainProjectDO.getId(), trainProjectDTO.getProjectAreaId());
            }
        }
    }

    private void updateCourseUserRelationAreaId(Integer projectId, Integer newAreaId) {
        // 直接根据projectId更新关联记录的项目区域ID
        trainCourseUserRelationService.updateProjectAreaIdByProjectId(projectId, newAreaId);
    }

    private ProjectStatusEnum getProjectStatus(TrainProjectDTO trainProjectDTO) {
        Date now = new Date();
        if (trainProjectDTO.getStartDate() == null || trainProjectDTO.getStartDate().after(now)) {
            return ProjectStatusEnum.NOT_STARTED;
        }
        if (trainProjectDTO.getEndDate() == null || trainProjectDTO.getEndDate().after(now)) {
            return ProjectStatusEnum.IN_PROGRESS;
        }
        return ProjectStatusEnum.COMPLETED;
    }

    /**
     * 分页查询项目列表
     * 
     * @param query
     * @return
     */
    public Pagination<TrainProjectWithCourseVO> pageWithCourse(TrainProjectQueryDTO query) {
        Pagination<TrainProjectWithCourseVO> pagination = new Pagination<>();
        pagination.setPageIndex(query.getPageIndex());
        pagination.setPageSize(query.getPageSize());
        
        long total = trainProjectMapperEx.countByQuery(query);
        pagination.setTotal(total);
        if (total == 0) {
            pagination.setRows(new ArrayList<>());
            return pagination;
        }
        List<TrainProjectDO> list = trainProjectMapperEx.selectByQuery(query);
        List<TrainProjectWithCourseVO> result = new ArrayList<>();
        List<DictMappingVO> projectLevelDictList = dictMappingService.getDictListByDictType("project_level");
        Map<String, String> projectLevelDictMap = projectLevelDictList.stream().collect(Collectors.toMap(DictMappingVO::getDictCode, DictMappingVO::getDictValue));
        List<DictMappingVO> projectTypeDictList = dictMappingService.getDictListByDictType("project_type");
        Map<String, String> projectTypeDictMap = projectTypeDictList.stream().collect(Collectors.toMap(DictMappingVO::getDictCode, DictMappingVO::getDictValue));
        List<DictMappingVO> businessTypeDictList = dictMappingService.getDictListByDictType("business_type");
        Map<String, String> businessTypeDictMap = businessTypeDictList.stream().collect(Collectors.toMap(DictMappingVO::getDictCode, DictMappingVO::getDictValue));
        for (TrainProjectDO trainProjectDO : list) {
            TrainProjectWithCourseVO vo = do2vo(trainProjectDO, projectLevelDictMap, projectTypeDictMap, businessTypeDictMap);
            result.add(vo);
        }
        processCourse(result);
        pagination.setRows(result);
        return pagination;
    }

    private void processCourse(List<TrainProjectWithCourseVO> result) {
        List<Integer> projectIds = result.stream().map(TrainProjectWithCourseVO::getId).collect(Collectors.toList());
        List<TrainCourseVO> trainCourseList = trainCourseService.listByProjectIds(projectIds);
        processCheckTask(trainCourseList);
        List<TrainCourseAreaRelationDTO> trainCourseAreaRelationList = trainCourseAreaRelationService.listByProjectIds(projectIds);
        List<TrainCourseUserRelationDTO> trainCourseUserRelationList = trainCourseUserRelationService.listByProjectIds(projectIds);
        Map<Integer, List<TrainCourseVO>> trainCourseMap = trainCourseList.stream().collect(Collectors.groupingBy(TrainCourseVO::getProjectId));
        Map<Integer, List<TrainCourseAreaRelationDTO>> trainCourseAreaRelationMap = trainCourseAreaRelationList.stream().collect(Collectors.groupingBy(TrainCourseAreaRelationDTO::getCourseId));
        Map<Integer, List<TrainCourseUserRelationDTO>> trainCourseUserRelationMap = trainCourseUserRelationList.stream().collect(Collectors.groupingBy(TrainCourseUserRelationDTO::getCourseId));
        for (TrainProjectWithCourseVO vo : result) {
            List<TrainCourseVO> courses = trainCourseMap.getOrDefault(vo.getId(), new ArrayList<>());
            courses.forEach(course -> {
                List<TrainCourseAreaRelationDTO> trainCourseAreaRelationDTOList = trainCourseAreaRelationMap.getOrDefault(course.getId(), new ArrayList<>());
                course.setCourseAreaList(trainCourseAreaRelationDTOList.stream().filter(relation -> relation.getRelationType() == TrainCourseAreaRelationTypeEnum.EXECUTE.getCode()).collect(Collectors.toList()));
                course.setCourseStudentAreaList(trainCourseAreaRelationDTOList.stream().filter(relation -> relation.getRelationType() == TrainCourseAreaRelationTypeEnum.STUDENT.getCode()).collect(Collectors.toList()));
                List<TrainCourseUserRelationDTO> trainCourseUserRelationDTOList = trainCourseUserRelationMap.getOrDefault(course.getId(), new ArrayList<>());
                trainCourseUserRelationDTOList.forEach(relation -> processCourseRelationOver(relation, course.getRealStartDate(), course.getRealEndDate()));
                course.setExecuteUsers(trainCourseUserRelationDTOList.stream().filter(relation -> relation.getRelationType() == TrainCourseUserRelationTypeEnum.EXECUTE.getCode()).collect(Collectors.toList()));
                course.setSupportUsers(trainCourseUserRelationDTOList.stream().filter(relation -> relation.getRelationType() == TrainCourseUserRelationTypeEnum.SUPPORT.getCode()).collect(Collectors.toList()));
            });
            vo.setCourses(trainCourseMap.getOrDefault(vo.getId(), new ArrayList<>()));
            vo.setCourseCount(vo.getCourses().size());
            vo.setFinishedCourseCount(vo.getCourses().stream().filter(course -> course.getCourseStatus() == 2).count());
        }

    }

    private void processCheckTask(List<TrainCourseVO> trainCourseVOS) {
        List<Integer> courseIds = trainCourseVOS.stream().map(TrainCourseVO::getId).collect(Collectors.toList());
        TrainCheckTaskQueryDTO trainCheckTaskQueryDTO = new TrainCheckTaskQueryDTO();
        trainCheckTaskQueryDTO.setCheckStatus(CheckSummaryStatusEnum.NORMAL.getCode());
        trainCheckTaskQueryDTO.setCourseIds(courseIds);
        List<TrainCheckTaskDTO> trainCheckTaskDOS = trainCheckTaskService.listByQuery(trainCheckTaskQueryDTO);
        Map<Integer, List<HalfDayTimeWithUser>> halfDayTimeWithUserMap = trainCheckTaskDOS.stream().map(this::task2halfDayTimeWithUser).flatMap(List::stream).distinct().collect(Collectors.groupingBy(HalfDayTimeWithUser::getCourseId));
        trainCourseVOS.forEach(trainCourseVO -> {
            List<HalfDayTimeWithUser> halfDayTimeWithUserList = halfDayTimeWithUserMap.getOrDefault(trainCourseVO.getId(), new ArrayList<>());
            trainCourseVO.setActualUserDays(halfDayTimeWithUserList.size()/2f);
        });
    }

    private List<HalfDayTimeWithUser> task2halfDayTimeWithUser(TrainCheckTaskDTO trainCheckTaskDTO) {
        List<HalfDayTimeWithUser> halfDayTimeWithUserList = new ArrayList<>();
        if(trainCheckTaskDTO.getCheckDimension() != CheckDimensionEnum.AFTERNOON.getCode()){
            HalfDayTimeWithUser halfDayTimeWithUser = new HalfDayTimeWithUser();
            halfDayTimeWithUser.setHalfDayTime(new HalfDayTime(trainCheckTaskDTO.getCheckDate(), HalfDayEnum.AM));
            halfDayTimeWithUser.setUserId(trainCheckTaskDTO.getUserId());
            halfDayTimeWithUser.setCourseId(trainCheckTaskDTO.getCourseId());
            halfDayTimeWithUserList.add(halfDayTimeWithUser);
        }
        if(trainCheckTaskDTO.getCheckDimension() != CheckDimensionEnum.MORNING.getCode()){
            HalfDayTimeWithUser halfDayTimeWithUser = new HalfDayTimeWithUser();
            halfDayTimeWithUser.setUserId(trainCheckTaskDTO.getUserId());
            halfDayTimeWithUser.setHalfDayTime(new HalfDayTime(trainCheckTaskDTO.getCheckDate(), HalfDayEnum.PM));
            halfDayTimeWithUser.setCourseId(trainCheckTaskDTO.getCourseId());
            halfDayTimeWithUserList.add(halfDayTimeWithUser);
        }
        return halfDayTimeWithUserList;
    }
        
    private TrainProjectWithCourseVO do2vo(TrainProjectDO trainProjectDO, Map<String, String> projectLevelDictMap
        , Map<String, String> projectTypeDictMap, Map<String, String> businessTypeDictMap) {
        TrainProjectWithCourseVO vo = new TrainProjectWithCourseVO();
        BeanUtils.copyProperties(trainProjectDO, vo);
        vo.setProjectLevelDesc(projectLevelDictMap.getOrDefault(String.valueOf(trainProjectDO.getProjectLevel()), ""));
        vo.setProjectTypeDesc(projectTypeDictMap.getOrDefault(String.valueOf(trainProjectDO.getProjectType()), ""));
        vo.setBusinessTypeDesc(businessTypeDictMap.getOrDefault(String.valueOf(trainProjectDO.getBusinessType()), ""));
        if(StringUtils.isNotEmpty(trainProjectDO.getProjectManagerId())){
            vo.setProjectManagerDeptId(trainProjectDO.getProjectManagerId().substring(0, 5));
            vo.setProjectManagerId(trainProjectDO.getProjectManagerId().substring(5));
            vo.setProjectManagerStatus(userStatusManager.getUserStatus(vo.getProjectManagerId()));
        }
        if(StringUtils.isNotEmpty(trainProjectDO.getProjectEditorId())){
            vo.setProjectEditorDeptId(trainProjectDO.getProjectEditorId().substring(0, 5));
            vo.setProjectEditorId(trainProjectDO.getProjectEditorId().substring(5));
            vo.setProjectEditorStatus(userStatusManager.getUserStatus(vo.getProjectEditorId()));
        }
        if(StringUtils.isNotEmpty(trainProjectDO.getLiveEditorName())){
            List<String> liveEditorNames = Arrays.asList(trainProjectDO.getLiveEditorName().split(";"));
            List<String> liveEditorIds = new ArrayList<>();
            if(StringUtils.isNotBlank(trainProjectDO.getLiveEditorId())){
                liveEditorIds = Arrays.asList(trainProjectDO.getLiveEditorId().split(";"));
            }
            List<TrainUserVO> liveEditors = new ArrayList<>();
            for(int i=0; i<liveEditorNames.size(); i++){
                TrainUserVO liveEditor = new TrainUserVO();
                liveEditor.setUserName(liveEditorNames.get(i));
                if(i<liveEditorIds.size()){
                    liveEditor.setUserId(Long.valueOf(liveEditorIds.get(i).substring(5)));
                    liveEditor.setDeptId(liveEditorIds.get(i).substring(0, 5));
                    liveEditor.setUserStatus(userStatusManager.getUserStatus(liveEditorIds.get(i).substring(5)));
                }
                liveEditors.add(liveEditor);
            }
            vo.setLiveEditors(liveEditors);
        }

        if(StringUtils.isNotEmpty(trainProjectDO.getProjectHelperName())){
            List<String> projectHelperNames = Arrays.asList(trainProjectDO.getProjectHelperName().split(";"));
            List<String> projectHelperIds = new ArrayList<>();
            if(StringUtils.isNotBlank(trainProjectDO.getProjectHelperId())){
                projectHelperIds = Arrays.asList(trainProjectDO.getProjectHelperId().split(";"));
            }
            List<TrainUserVO> projectHelpers = new ArrayList<>();
            for(int i=0; i<projectHelperNames.size(); i++){
                TrainUserVO projectHelper = new TrainUserVO();
                projectHelper.setUserName(projectHelperNames.get(i));
                if(i<projectHelperIds.size()){
                    projectHelper.setUserId(Long.valueOf(projectHelperIds.get(i).substring(5)));
                    projectHelper.setDeptId(projectHelperIds.get(i).substring(0, 5));
                    projectHelper.setUserStatus(userStatusManager.getUserStatus(projectHelperIds.get(i).substring(5)));
                }
                projectHelpers.add(projectHelper);
            }
            vo.setProjectHelpers(projectHelpers);
        }
        return vo;
    }

    /**
     * 更新排期状态
     * 
     * @param projectId
     * @param scheduleStatus
     */
    public TrainProjectVO updateScheduleStatus(Integer projectId, int scheduleStatus) {
        if (projectId == null) {
            throw ExceptionUtil.businessException(ManagementExceptionEnum.PROJECT_NOT_EXIST);
        }
        TrainProjectDO trainProjectDO = trainProjectMapperEx.selectByPrimaryKey(projectId);
        if (trainProjectDO == null) {
            throw ExceptionUtil.businessException(ManagementExceptionEnum.PROJECT_NOT_EXIST);
        }
        TrainProjectDO updateTrainProjectDO = new TrainProjectDO();
        updateTrainProjectDO.setId(projectId);
        updateTrainProjectDO.setScheduleStatus(scheduleStatus);
        trainProjectMapperEx.updateByPrimaryKeySelective(updateTrainProjectDO);
        TrainProjectVO trainProjectVO = new TrainProjectVO();
        BeanUtils.copyProperties(trainProjectDO, trainProjectVO);
        return trainProjectVO;
    }

    /**
     * 更新排期状态
     * 
     * @param projectId
     * @param scheduleStatus
     * @return
     */
    @Async
    public TrainProjectVO updateScheduleStatus(Integer projectId) {
        try {
            // 模拟异步处理延时
            Thread.sleep(1000);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        if (projectId == null) {
            throw ExceptionUtil.businessException(ManagementExceptionEnum.PROJECT_NOT_EXIST);
        }
        TrainProjectDO trainProjectDO = trainProjectMapperEx.selectByPrimaryKey(projectId);
        if (trainProjectDO == null) {
            throw ExceptionUtil.businessException(ManagementExceptionEnum.PROJECT_NOT_EXIST);
        }
        List<TrainCourseVO> trainCourseList = trainCourseService.listByProjectIds(Collections.singletonList(projectId));
        TrainProjectDO updateTrainProjectDO = new TrainProjectDO();
        updateTrainProjectDO.setId(projectId);
        updateTrainProjectDO.setScheduleStatus(trainCourseList.isEmpty() ? 0 : 1);
        trainProjectMapperEx.updateByPrimaryKeySelective(updateTrainProjectDO);
        TrainProjectVO trainProjectVO = new TrainProjectVO();
        BeanUtils.copyProperties(trainProjectDO, trainProjectVO);
        return trainProjectVO;
    }

    public void updateIsRemote(Integer projectId){
        trainProjectMapperEx.updateIsRemote(projectId);
    }

    /**
     * 处理项目状态
     */
    public void processStatus() {
        trainProjectMapperEx.processCompleteStatus();
        trainProjectMapperEx.processStartStatus();
    }

    /**
     * 获取项目状态汇总
     * 
     * @return 项目状态汇总
     */
    public ProjectStatusSummaryVO getProjectStatusSummary(TrainProjectQueryDTO query) {
        return trainProjectMapperEx.getProjectStatusSummary(query);
    }

    /**
     * 获取手动同步的项目
     * 
     * @return 手动同步的项目
     */
    public List<TrainProjectDTO> getManualSyncProject() {
        TrainProjectQueryDTO query = new TrainProjectQueryDTO();
        query.setSyncType(1);
        query.setPageIndex(null);
        query.setPageSize(null);
        List<TrainProjectDO> list = trainProjectMapperEx.selectByQuery(query);
        return list.stream().map(this::do2dto).collect(Collectors.toList());
    }

    /**
     * 获取跨区支持项目涉及的区域ID列表
     * @param excludeProjectAreaIds 需要排除的项目区域ID列表
     * @return 区域ID列表
     */
    public List<String> getCrossAreaIds(List<String> excludeProjectAreaIds, String userId) {
        return trainProjectMapperEx.selectCrossAreaIds(excludeProjectAreaIds, userId);
    }

    private TrainProjectDTO do2dto(TrainProjectDO trainProjectDO) {
        TrainProjectDTO trainProjectDTO = new TrainProjectDTO();
        BeanUtils.copyProperties(trainProjectDO, trainProjectDTO);
        return trainProjectDTO;
    }

    /**
     * 获取项目详情
     * 
     * @param projectId
     * @return
     */
    public TrainProjectWithCourseVO getProjectWithCourse(Integer projectId) {
        TrainProjectDO trainProjectDO = trainProjectMapperEx.selectByPrimaryKey(projectId);
        if (trainProjectDO == null) {
            throw ExceptionUtil.businessException(ManagementExceptionEnum.PROJECT_NOT_EXIST);
        }
        List<DictMappingVO> projectLevelDictList = dictMappingService.getDictListByDictType("project_level");
        Map<String, String> projectLevelDictMap = projectLevelDictList.stream().collect(Collectors.toMap(DictMappingVO::getDictCode, DictMappingVO::getDictValue));
        List<DictMappingVO> projectTypeDictList = dictMappingService.getDictListByDictType("project_type");
        Map<String, String> projectTypeDictMap = projectTypeDictList.stream().collect(Collectors.toMap(DictMappingVO::getDictCode, DictMappingVO::getDictValue));
        List<DictMappingVO> businessTypeDictList = dictMappingService.getDictListByDictType("business_type");
        Map<String, String> businessTypeDictMap = businessTypeDictList.stream().collect(Collectors.toMap(DictMappingVO::getDictCode, DictMappingVO::getDictValue));
        TrainProjectWithCourseVO vo = do2vo(trainProjectDO, projectLevelDictMap, projectTypeDictMap, businessTypeDictMap);
        List<TrainCourseVO> trainCourseList = trainCourseService.listByProjectIds(Collections.singletonList(projectId));
        vo.setCourses(trainCourseList);
        vo.setCourseCount(trainCourseList.size());
        vo.setFinishedCourseCount(trainCourseList.stream().filter(course -> course.getCourseStatus() == 2).count());
        List<TrainCourseAreaRelationDTO> trainCourseAreaRelationList = trainCourseAreaRelationService.listByProjectIds(Collections.singletonList(projectId));
        List<TrainCourseUserRelationDTO> trainCourseUserRelationList = trainCourseUserRelationService.listByProjectIds(Collections.singletonList(projectId));
        Map<Integer, List<TrainCourseAreaRelationDTO>> trainCourseAreaRelationMap = trainCourseAreaRelationList.stream().collect(Collectors.groupingBy(TrainCourseAreaRelationDTO::getCourseId));
        Map<Integer, List<TrainCourseUserRelationDTO>> trainCourseUserRelationMap = trainCourseUserRelationList.stream().collect(Collectors.groupingBy(TrainCourseUserRelationDTO::getCourseId));
        trainCourseList.forEach(course -> {
            List<TrainCourseAreaRelationDTO> trainCourseAreaRelationDTOList = trainCourseAreaRelationMap.getOrDefault(course.getId(), new ArrayList<>());
            course.setCourseAreaList(trainCourseAreaRelationDTOList.stream().filter(relation -> relation.getRelationType() == TrainCourseAreaRelationTypeEnum.EXECUTE.getCode()).collect(Collectors.toList()));
            course.setCourseStudentAreaList(trainCourseAreaRelationDTOList.stream().filter(relation -> relation.getRelationType() == TrainCourseAreaRelationTypeEnum.STUDENT.getCode()).collect(Collectors.toList()));
            List<TrainCourseUserRelationDTO> trainCourseUserRelationDTOList = trainCourseUserRelationMap.getOrDefault(course.getId(), new ArrayList<>());
            trainCourseUserRelationDTOList.forEach(relation -> processCourseRelationOver(relation, course.getRealStartDate(), course.getRealEndDate()));
            course.setExecuteUsers(trainCourseUserRelationDTOList.stream().filter(relation -> relation.getRelationType() == TrainCourseUserRelationTypeEnum.EXECUTE.getCode()).collect(Collectors.toList()));
            course.setSupportUsers(trainCourseUserRelationDTOList.stream().filter(relation -> relation.getRelationType() == TrainCourseUserRelationTypeEnum.SUPPORT.getCode()).collect(Collectors.toList()));
        });
        processCheckTask(trainCourseList);
        return vo;
    }

    private void processCourseRelationOver(TrainCourseUserRelationDTO relationDTO, HalfDayTime realStartDate, HalfDayTime realEndDate) {
        boolean isOverRealExecuteDate = relationDTO.getStartDate().isBefore(realStartDate) || relationDTO.getEndDate().isAfter(realEndDate);
        relationDTO.setIsOverRealExecuteDate(isOverRealExecuteDate);
    }

    /**
     * 分页查询项目列表
     * 
     * @param query
     * @return
     */
    public Pagination<TrainProjectVO> page(TrainProjectQueryDTO query) {
        Pagination<TrainProjectVO> pagination = new Pagination<>();
        pagination.setPageIndex(query.getPageIndex());
        pagination.setPageSize(query.getPageSize());
        long total = trainProjectMapperEx.countByQuery(query);
        pagination.setTotal(total);
        if (total == 0) {
            pagination.setRows(new ArrayList<>());
            return pagination;
        }
        List<TrainProjectDO> list = trainProjectMapperEx.selectByQuery(query);
        List<TrainProjectVO> result = new ArrayList<>();
        for (TrainProjectDO trainProjectDO : list) {
            TrainProjectVO vo = new TrainProjectVO();
            BeanUtils.copyProperties(trainProjectDO, vo);
            result.add(vo);
        }
        pagination.setRows(result);
        return pagination;
    }
    /**
     * 项目下无任何班次提醒
     * （距离项目开始7天、3天、1天 的 9:00 提醒）
     */
    @Transactional(rollbackFor = Exception.class)
    public void withoutCourseReminder() {

        log.info("提前7天、3天、1天 发送 无班次项目 开始消息");
        List<TrainProjectDO> withoutCourseProject7 = queryWithoutCourseProjectsForDay(7);
        List<TrainProjectDO> withoutCourseProject3 = queryWithoutCourseProjectsForDay(3);
        List<TrainProjectDO> withoutCourseProject1 = queryWithoutCourseProjectsForDay(1);
        List<TrainProjectDO> allProjects = new ArrayList<>(withoutCourseProject7);
        allProjects.addAll(withoutCourseProject3);
        allProjects.addAll(withoutCourseProject1);

        if (CollectionUtils.isEmpty(allProjects)) {
            log.info("无需要发送提醒的项目");
            return;
        }
        List<DingTalkSendDTO> dingTalkSendDTOS = new ArrayList<>();
        List<SystemMessageDTO> systemMessageDTOs = new ArrayList<>();
        // 构建消息体
        for (TrainProjectDO trainProjectDO : allProjects) {
            String managerId = StringUtils.substring(trainProjectDO.getProjectManagerId(), 5);
            String addProjectManagerContent = "<font color=\"#FF0000\">【重要通知-项目排班提醒】</font>" + "\n\n" +
                        "项目即将启动，尚未配置班次，请及时排班" + "\n\n" +
                        "项目名称：" + trainProjectDO.getProjectCode() + " " + trainProjectDO.getProjectName() + "\n\n" +
                        "项目时间：" + DateUtils.format(trainProjectDO.getStartDate(), "yyyy/MM/dd") + " - " + DateUtils.format(trainProjectDO.getEndDate(), "yyyy/MM/dd") + "\n\n" +
                        "通知时间：" + DateUtils.format(new Date(), "yyyy/MM/dd HH:mm:ss") + "\n\n";
                DingTalkSendDTO dingTalkSendDTO = new DingTalkSendDTO();
                dingTalkSendDTO.setTitle("【重要通知-项目排班提醒】");
                dingTalkSendDTO.setUserIds(Collections.singletonList(managerId));
                dingTalkSendDTO.setMdContent(addProjectManagerContent);
                dingTalkSendDTOS.add(dingTalkSendDTO);
                // 构建系统消息体
                SystemMessageDTO systemMessageDTO = new SystemMessageDTO();
                if (StringUtils.isEmpty(managerId)) {
                    systemMessageDTO.setUserId(0L);
                } else {
                    systemMessageDTO.setUserId(Long.valueOf(managerId));
                }
                systemMessageDTO.setMessageType(MessageEnum.NONE_COURSE_PROJECT.getCode());
                systemMessageDTO.setMessageTitle("项目<span style=\"color: #007cff; cursor: pointer;\">《"+trainProjectDO.getProjectName()+"》</span>即将启动，尚未配置班次");
                systemMessageDTO.setMessageContent("你负责的项目【"+trainProjectDO.getProjectName()+"】即将启动，尚未配置班次，请及时排班");
                systemMessageDTO.setIsRead(0);
                systemMessageDTO.setBusinessId(Long.valueOf(trainProjectDO.getId()));
                systemMessageDTO.setBusinessType(1);
                systemMessageDTO.setGmtCreate(new Date());
                systemMessageDTOs.add(systemMessageDTO);
            }
            systemMessageService.saveBatch(systemMessageDTOs);
            dingTalkV1Service.sendDingTalkMessage(dingTalkSendDTOS);
    }


    // 查询指定天数后开始的无班次项目
    private List<TrainProjectDO> queryWithoutCourseProjectsForDay(int daysLater) {
        TrainProjectQueryDTO query = new TrainProjectQueryDTO();
        DateTime targetDate = DateUtil.offsetDay(DateUtil.date(), daysLater);

        query.setStartTime(DateUtil.beginOfDay(targetDate));
        query.setEndTime(DateUtil.endOfDay(targetDate));
        query.setProjectStatus(Collections.singletonList(ProjectStatusEnum.NOT_STARTED.getValue()));
        query.setPageIndex(null);
        query.setPageSize(null);
        List<TrainProjectDO> allProjects = trainProjectMapperEx.selectByQuery(query);
        if (CollectionUtils.isEmpty(allProjects)) {
            return new ArrayList<>();
        }
        List<Integer> projectIds = allProjects.stream().map(TrainProjectDO::getId).distinct().collect(Collectors.toList());
        // 查询项目下的班次信息
        List<TrainCourseVO> trainCourseList = trainCourseService.listByProjectIds(projectIds);
        if(CollectionUtils.isEmpty(trainCourseList)) {
            log.info("所有项目下均无班次");
            return allProjects;
        }
        // 提取有班次的项目ID
        Set<Integer> projectsWithCourses = trainCourseList.stream()
                .map(TrainCourseVO::getProjectId)
                .collect(Collectors.toSet());

        // 过滤出无班次的项目
        return allProjects.stream()
                .filter(p -> !projectsWithCourses.contains(p.getId()))
                .collect(Collectors.toList());
    }


    /**
     * 根据ID获取项目信息
     * @param id 项目ID
     * @return 项目信息
     */
    public TrainProjectVO getProjectById(Integer id) {
        TrainProjectDO trainProjectDO = trainProjectMapperEx.selectByPrimaryKey(id);
        if (trainProjectDO == null) {
            return null;
        }
        TrainProjectVO vo = new TrainProjectVO();
        BeanUtils.copyProperties(trainProjectDO, vo);
        return vo;
    }

    /**
     * 逻辑删除项目
     * @param id 项目ID
     */
    @Transactional(rollbackFor = Exception.class)
    public void logicDeleteProject(Integer id) {
        TrainProjectDO updateDO = new TrainProjectDO();
        updateDO.setId(id);
        updateDO.setInvalid(1);
        trainProjectMapperEx.updateByPrimaryKeySelective(updateDO);
    }
}
