package com.dt.train.management.center.enums;

import java.util.Arrays;

import lombok.Getter;

@Getter
public enum CourseFormEnum {

    /**
     * 面授、远程直播、现场直播
     */
    FACE_TO_FACE(1, "面授"),
    REMOTE_LIVE(2, "远程直播"),
    LIVE_ON_SITE(3, "现场直播");

    private final Integer code;
    private final String name;

    CourseFormEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    /**
     * 根据code获取枚举
     * @param code
     * @return
     */
    public static CourseFormEnum getByCode(Integer code) {
        return Arrays.stream(CourseFormEnum.values())
            .filter(item -> item.getCode().equals(code))
            .findFirst()
            .orElse(null);
    }
}
