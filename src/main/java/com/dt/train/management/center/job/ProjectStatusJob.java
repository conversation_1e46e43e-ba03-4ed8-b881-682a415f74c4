package com.dt.train.management.center.job;

import com.dt.train.management.center.service.project.TrainProjectService;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.dt.train.management.center.manager.ProjectSyncManager;
import com.dt.train.management.center.service.project.TrainCourseService;

import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

@RestController
@RequestMapping("/feign/job/project")
@Slf4j
public class ProjectStatusJob {

    private final ProjectSyncManager projectSyncManager;

    private final TrainCourseService trainCourseService;

    private final TrainProjectService trainProjectService;

    public ProjectStatusJob(ProjectSyncManager projectSyncManager, TrainCourseService trainCourseService,TrainProjectService trainProjectService) {
        this.projectSyncManager = projectSyncManager;
        this.trainCourseService = trainCourseService;
        this.trainProjectService = trainProjectService;
    }

    /**
     * 项目班次状态刷新
     * 
     */
    @PostMapping("/status")
    public void processStatus() {
        projectSyncManager.processStatus();
        trainCourseService.processStatus();
    }

    @GetMapping("/crm/sync/incremental")
    @ApiOperation("增量同步所有CRM项目")
    public String incrementalSyncAllProjects() {
        return projectSyncManager.incrementalSyncProject(null);
    }

    /**
     * 项目下无任何班次提醒（距离项目开始7天、3天、1天 的 9:00 提醒）
     *
     */
    @PostMapping("/withoutCourse/reminder")
    @ApiOperation("增量同步所有CRM项目")
    public void withoutCourseReminder() {
        trainProjectService.withoutCourseReminder();
    }
}
