package com.dt.train.management.center.service.task;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import com.dt.train.management.center.mapper.ex.TrainTaskDOMapperEx;
import com.dt.train.management.center.model.dao.TrainTaskDO;
import com.dt.train.management.center.model.dto.task.TrainTaskDTO;
import com.dt.train.management.center.model.dto.task.TrainTaskQueryDTO;
import com.dt.train.management.center.model.vo.task.TrainTaskVO;

@Service
public class TrainTaskService {

    private final TrainTaskDOMapperEx trainTaskMapperEx;

    public TrainTaskService(TrainTaskDOMapperEx trainTaskMapperEx) {
        this.trainTaskMapperEx = trainTaskMapperEx;
    }

    /**
     * 获取班次任务
     * 
     * @param queryDTO
     * @return
     */
    public TrainTaskVO getTask(TrainTaskQueryDTO queryDTO) {
        List<TrainTaskDO> tasks = trainTaskMapperEx.selectByQuery(queryDTO);
        if (tasks.isEmpty()) {
            return null;
        }
        return do2vo(tasks.get(0));
    }

    /**
     * 根据班次ID列表获取班次任务
     * 
     * @param courseIds
     * @return
     */
    public List<TrainTaskVO> getTasksByCourseIds(List<Integer> courseIds) {
        if (courseIds == null || courseIds.isEmpty()) {
            return Collections.emptyList();
        }
        List<TrainTaskDO> tasks = trainTaskMapperEx.selectByCourseIds(courseIds);
        return tasks.stream().map(this::do2vo).collect(Collectors.toList());
    }

    private TrainTaskVO do2vo(TrainTaskDO trainTaskDO) {
        TrainTaskVO trainTaskVO = new TrainTaskVO();
        BeanUtils.copyProperties(trainTaskDO, trainTaskVO);
        return trainTaskVO;
    }

    /**
     * 新建或更新班次任务
     * 
     * @param trainTaskDTO
     * @return
     */
    public void saveOrUpdateTrainTask(TrainTaskDTO trainTaskDTO) {
        if (trainTaskDTO == null || trainTaskDTO.getCourseId() == null || trainTaskDTO.getProjectId() == null
                || trainTaskDTO.getTaskType() == null) {
            return;
        }
        TrainTaskDO trainTaskDO = new TrainTaskDO();
        BeanUtils.copyProperties(trainTaskDTO, trainTaskDO);
        // 查询是否存在
        TrainTaskQueryDTO queryDTO = new TrainTaskQueryDTO();
        queryDTO.setCourseId(trainTaskDTO.getCourseId());
        queryDTO.setTaskType(trainTaskDTO.getTaskType());
        List<TrainTaskDO> existedTasks = trainTaskMapperEx.selectByQuery(queryDTO);
        if (existedTasks.isEmpty()) {
            trainTaskMapperEx.insertSelective(trainTaskDO);
        } else {
            trainTaskDO.setId(existedTasks.get(0).getId());
            trainTaskMapperEx.updateByPrimaryKeySelective(trainTaskDO);
        }
    }

    public void deleteByCourseId(Integer courseId) {
        trainTaskMapperEx.deleteByCourseId(courseId);
    }

}
