package com.dt.train.management.center.model.vo.check;

import java.util.Date;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;

import io.swagger.annotations.ApiModelProperty;

/**
 * 
 * 表名: train_check_record
 *
 * @mbg.generated
 */
@lombok.Data
public class TrainCheckRecordVO {
    /**
     * 字段描述: 主键ID
     *
     * 字段名: train_check_record.id
     *
     * @mbg.generated
     */
    private Integer id;

    /**
     * 字段描述: 项目ID
     *
     * 字段名: train_check_record.project_id
     *
     * @mbg.generated
     */
    @ApiModelProperty(value = "项目ID")
    private Integer projectId;

    /**
     * 字段描述: 班次ID
     *
     * 字段名: train_check_record.course_id
     *
     * @mbg.generated
     */
    @ApiModelProperty(value = "班次ID")
    private Integer courseId;

    /**
     * 字段描述: 任务ID
     *
     * 字段名: train_check_record.task_id
     *
     * @mbg.generated
     */
    @ApiModelProperty(value = "任务ID")
    private Integer taskId;

    /**
     * 字段描述: 考勤任务ID
     *
     * 字段名: train_check_record.check_task_id
     *
     * @mbg.generated
     */
    @ApiModelProperty(value = "考勤任务ID")
    private Integer checkTaskId;

    /**
     * 字段描述: 用户ID
     *
     * 字段名: train_check_record.user_id
     *
     * @mbg.generated
     */
    @ApiModelProperty(value = "用户ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long userId;

    /**
     * 字段描述: 打卡类型,1-上班打卡，2-下班打卡，3-现场签到
     *
     * 字段名: train_check_record.check_type
     *
     * @mbg.generated
     */
    @ApiModelProperty(value = "打卡类型,1-上班打卡，2-下班打卡，3-现场签到")
    private Integer checkType;

    /**
     * 字段描述: 打卡时间
     *
     * 字段名: train_check_record.check_time
     *
     * @mbg.generated
     */
    @ApiModelProperty(value = "打卡时间")
    private Date checkTime;

    /**
     * 字段描述: 打卡状态,1-正常打卡，2-迟到打卡,3-早退打卡
     *
     * 字段名: train_check_record.check_status
     *
     * @mbg.generated
     */
    @ApiModelProperty(value = "打卡状态,1-正常打卡，2-迟到打卡,3-早退打卡")
    private Integer checkStatus;

    /**
     * 字段描述: 打卡纬度
     *
     * 字段名: train_check_record.check_latitude
     *
     * @mbg.generated
     */
    @ApiModelProperty(value = "打卡纬度")
    private Double checkLatitude;

    /**
     * 字段描述: 打卡经度
     *
     * 字段名: train_check_record.check_longitude
     *
     * @mbg.generated
     */
    @ApiModelProperty(value = "打卡经度")
    private Double checkLongitude;

    /**
     * 字段描述: 打卡地址
     *
     * 字段名: train_check_record.check_address
     *
     * @mbg.generated
     */
    @ApiModelProperty(value = "打卡地址")
    private String checkAddress;

    /**
     * 字段描述: 打卡图片
     *
     * 字段名: train_check_record.check_image
     *
     * @mbg.generated
     */
    @ApiModelProperty(value = "打卡图片")
    private String checkImage;

    @ApiModelProperty("是否有效,0-无效，1-有效")
    private Integer isValid = 0;
}