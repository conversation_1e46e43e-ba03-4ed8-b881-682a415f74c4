package com.dt.train.management.center.interceptor;

import java.io.IOException;
import java.io.PrintWriter;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.commons.lang3.StringUtils;
import org.springframework.web.servlet.AsyncHandlerInterceptor;

import com.alibaba.fastjson.JSONObject;
import com.dt.framework.business.constant.BusinessCode;
import com.dt.framework.business.constant.DtHeaders;
import com.dt.train.management.center.config.UserRequestContextHolder;
import com.dt.train.management.center.constant.CacheConstant;
import com.dt.train.management.center.enums.ManagementExceptionEnum;
import com.dt.train.management.center.mapper.TrainUserDOMapper;
import com.dt.train.management.center.model.dao.TrainUserDO;
import com.dt.train.management.center.utils.RedisUtil;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 6/2/21 2:52 PM
 */
@Slf4j
public class CommonInterceptor implements AsyncHandlerInterceptor {

    private final RedisUtil redisUtil;

    private final TrainUserDOMapper trainUserDOMapper;

    public CommonInterceptor(RedisUtil redisUtil, TrainUserDOMapper trainUserDOMapper) {
        this.redisUtil = redisUtil;
        this.trainUserDOMapper = trainUserDOMapper;
    }

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response,
                             Object handler) throws Exception {
        String userIdStr = request.getHeader(DtHeaders.USER_ID);
        
        log.info("FrontInterceptor==前端拦截器==userId:{}", userIdStr);
        if(StringUtils.isEmpty(userIdStr)){
            log.info("FrontInterceptor==前端拦截器==当前为非登陆状态访问=======");
            fillResponse(response, BusinessCode.USER_NOT_LOGIN_IN_CODE, BusinessCode.USER_NOT_LOGIN_IN_MESSAGE);
            return false;
        }
        TrainUserDO trainUserDO;
        
        String key = CacheConstant.getTrainUserInfoKey(userIdStr);
        String user = redisUtil.get(key);
        if (!StringUtils.isEmpty(user)) {
            trainUserDO = JSONObject.parseObject(user, TrainUserDO.class);
        } else {
            trainUserDO = trainUserDOMapper.selectByUserId(Long.valueOf(userIdStr));
            if (trainUserDO != null) {
                redisUtil.set(key, JSONObject.toJSONString(trainUserDO), 60 * 10);
            }
        }
        if (trainUserDO == null) {
            log.info("未查询到userId:{}的用户信息,为非法请求！", userIdStr);
            fillResponse(response, BusinessCode.NO_PERMISSION_CODE, BusinessCode.NO_PERMISSION_MESSAGE);
            return false;
        }
        if (trainUserDO.getUserStatus() == 1) {
            fillResponse(response, BusinessCode.FORBIDDEN_ACCOUNT_CODE, ManagementExceptionEnum.USER_NO_PERMISSION.getMessage());
            return false;
        }
        UserRequestContextHolder.setRequestUserId(trainUserDO.getUserId());
        UserRequestContextHolder.setRequestUserName(trainUserDO.getUserName());
        UserRequestContextHolder.setRequestDeptId(trainUserDO.getDeptIds());
        UserRequestContextHolder.setRequestManageDeptId(trainUserDO.getManageDeptIds());
        return true;
    }

    private void fillResponse(HttpServletResponse response, Integer code, String desc) {
        response.setCharacterEncoding("UTF-8");
        response.setContentType("application/json; charset=utf-8");
        JSONObject status = new JSONObject();
        status.put("code", code);
        status.put("desc", desc);
        Long timestamp = System.currentTimeMillis();
        JSONObject result = new JSONObject();
        result.put("status", status);
        result.put("timestamp", timestamp);
        PrintWriter out = null;
        try {
            out = response.getWriter();
            out.append(result.toString());
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            if (out != null) {
                out.close();
            }
        }
    }
    
}
