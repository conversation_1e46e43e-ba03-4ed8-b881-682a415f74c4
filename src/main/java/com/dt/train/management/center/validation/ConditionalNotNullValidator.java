package com.dt.train.management.center.validation;

import org.springframework.util.StringUtils;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;
import java.lang.reflect.Field;
import java.util.Arrays;

/**
 * 条件非空验证器
 */
public class ConditionalNotNullValidator implements ConstraintValidator<ConditionalNotNull, Object> {
    
    private String field;
    private String conditionField;
    private String[] conditionValues;
    
    @Override
    public void initialize(ConditionalNotNull constraintAnnotation) {
        this.field = constraintAnnotation.field();
        this.conditionField = constraintAnnotation.conditionField();
        this.conditionValues = constraintAnnotation.conditionValues();
    }
    
    @Override
    public boolean isValid(Object value, ConstraintValidatorContext context) {
        try {
            // 获取条件字段的值
            Object conditionValue = getFieldValue(value, conditionField);
            
            // 如果条件字段的值在指定的条件值列表中，则验证目标字段
            if (conditionValue != null && Arrays.asList(conditionValues).contains(conditionValue.toString())) {
                Object fieldValue = getFieldValue(value, field);
                
                // 检查目标字段是否为空
                if (fieldValue == null || (fieldValue instanceof String && !StringUtils.hasText((String) fieldValue))) {
                    // 自定义错误消息
                    context.disableDefaultConstraintViolation();
                    context.buildConstraintViolationWithTemplate(context.getDefaultConstraintMessageTemplate())
                           .addPropertyNode(field)
                           .addConstraintViolation();
                    return false;
                }
            }
            
            return true;
        } catch (Exception e) {
            return false;
        }
    }
    
    private Object getFieldValue(Object object, String fieldName) throws Exception {
        Field field = object.getClass().getDeclaredField(fieldName);
        field.setAccessible(true);
        return field.get(object);
    }
}
