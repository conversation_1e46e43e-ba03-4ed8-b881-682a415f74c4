package com.dt.train.management.center.manager;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import com.dt.train.management.center.enums.TrainCourseUserRelationTypeEnum;
import com.dt.train.management.center.model.dto.project.TrainCourseUserRelationDTO;
import com.dt.train.management.center.model.dto.task.TrainTaskQueryDTO;
import com.dt.train.management.center.model.vo.task.TrainTaskVO;
import com.dt.train.management.center.model.vo.task.TrainTaskWithUserVO;
import com.dt.train.management.center.service.project.TrainCourseUserRelationService;
import com.dt.train.management.center.service.task.TrainTaskService;

@Component
public class TrainTaskManager {

    private final TrainTaskService trainTaskService;

    private final TrainCourseUserRelationService trainCourseUserRelationService;

    public TrainTaskManager(TrainTaskService trainTaskService, TrainCourseUserRelationService trainCourseUserRelationService) {
        this.trainTaskService = trainTaskService;
        this.trainCourseUserRelationService = trainCourseUserRelationService;
    }

    /**
     * 获取班次任务及支援人、执行人
     * 
     * @param queryDTO
     * @return
     */
    public TrainTaskWithUserVO getTaskWithUser(TrainTaskQueryDTO queryDTO) {
        TrainTaskVO task = trainTaskService.getTask(queryDTO);
        if(task == null) {
            return null;
        }
        TrainTaskWithUserVO trainTaskWithUserVO = new TrainTaskWithUserVO();
        BeanUtils.copyProperties(task, trainTaskWithUserVO);
        List<TrainCourseUserRelationDTO> relationUsers = trainCourseUserRelationService.listByCourseIds(Arrays.asList(queryDTO.getCourseId()));
        trainTaskWithUserVO.setSupportUsers(relationUsers.stream().filter(relation -> relation.getRelationType() == TrainCourseUserRelationTypeEnum.SUPPORT.getCode()).collect(Collectors.toList()));
        trainTaskWithUserVO.setExecuteUsers(relationUsers.stream().filter(relation -> relation.getRelationType() == TrainCourseUserRelationTypeEnum.EXECUTE.getCode()).collect(Collectors.toList()));
        return trainTaskWithUserVO;
    }
}
