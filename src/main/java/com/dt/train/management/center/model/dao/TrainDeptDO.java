package com.dt.train.management.center.model.dao;

import java.util.Date;

/**
 * 
 * 表名: train_dept
 *
 * @mbg.generated
 */
@lombok.Data
public class TrainDeptDO {
    /**
     * 字段描述: 主键ID
     *
     * 字段名: train_dept.id
     *
     * @mbg.generated
     */
    private Integer id;

    /**
     * 字段描述: 父级部门ID,-1为顶级部门
     *
     * 字段名: train_dept.parent_id
     *
     * @mbg.generated
     */
    private Integer parentId;


    /**
     * 字段描述: 部门名称
     *
     * 字段名: train_dept.dept_name
     *
     * @mbg.generated
     */
    private String deptName;
    /**
     * 字段描述: 部门名称简称
     *
     * 字段名: train_dept.dept_name_short
     *
     * @mbg.generated
     */
    private String deptNameShort;

    /**
     * 字段描述: 部门类型,0-超管部门，1-普通部门
     *
     * 字段名: train_dept.dept_type
     *
     * @mbg.generated
     */
    private Integer deptType;

    /**
     * 字段描述: 部门层级
     *
     * 字段名: train_dept.dept_level
     *
     * @mbg.generated
     */
    private Integer deptLevel;

    /**
     * 字段描述: 是否隐藏，0-不隐藏，1-隐藏
     *
     * 字段名: train_dept.hidden
     *
     * @mbg.generated
     */
    private Integer hidden;

    /**
     * 字段描述: 是否已删除 0 否 1 是
     *
     * 字段名: train_dept.invalid
     *
     * @mbg.generated
     */
    private Integer invalid;

    /**
     * 字段描述: 创建人
     *
     * 字段名: train_dept.created_by
     *
     * @mbg.generated
     */
    private Long createdBy;

    /**
     * 字段描述: 更新人
     *
     * 字段名: train_dept.updated_by
     *
     * @mbg.generated
     */
    private Long updatedBy;

    /**
     * 字段描述: 创建时间
     *
     * 字段名: train_dept.gmt_create
     *
     * @mbg.generated
     */
    private Date gmtCreate;

    /**
     * 字段描述: 修改时间
     *
     * 字段名: train_dept.gmt_modified
     *
     * @mbg.generated
     */
    private Date gmtModified;
}