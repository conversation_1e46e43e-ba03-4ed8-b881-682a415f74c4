package com.dt.train.management.center.model.dto.tools;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@EqualsAndHashCode(callSuper = false)
public class RegionUploadDataDTO {
    @ExcelProperty("区域名称")
    private String regionName;

    @ExcelProperty("区域类型")
    private String regionType;

    @ExcelProperty("crmID")
    private String crmID;

    @ExcelProperty("userCenterID")
    private String userCenterID;

    @ExcelProperty("行政编码")
    private String gaodeCode;


    private int lineNum;


}
