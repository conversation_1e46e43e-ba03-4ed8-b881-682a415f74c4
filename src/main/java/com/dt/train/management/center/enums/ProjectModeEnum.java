package com.dt.train.management.center.enums;

import lombok.Getter;

@Getter
public enum ProjectModeEnum {

    /**
     * 在线培训，集中研修，混合研修，教学教研
     */
    ONLINE_TRAIN(1, "在线培训"),
    CENTRAL_TRAIN(2, "集中研修"),
    MIXED_TRAIN(3, "混合研修"),
    SCHOOL_RESEARCH(4, "教学教研"),
    NO_MODE(5, "/");

    private Integer code;

    private String desc;

    ProjectModeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static ProjectModeEnum getByCode(Integer code) {
        for (ProjectModeEnum projectModeEnum : ProjectModeEnum.values()) {
            if (projectModeEnum.getCode().equals(code)) {
                return projectModeEnum;
            }
        }
        return NO_MODE;
    }

}
