package com.dt.train.management.center.service.project;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import com.dt.train.management.center.mapper.TrainCourseAreaRelationDOMapper;
import com.dt.train.management.center.model.dao.TrainCourseAreaRelationDO;
import com.dt.train.management.center.model.dto.project.TrainCourseAreaRelationDTO;
import com.dt.train.management.center.model.dto.project.TrainCourseAreaRelationQueryDTO;
import com.dt.train.management.center.model.dto.project.TrainCourseAreaRelationSaveOrUpdateDTO;

@Service
public class TrainCourseAreaRelationService {

    private final TrainCourseAreaRelationDOMapper trainCourseAreaRelationDOMapper;

    public TrainCourseAreaRelationService(TrainCourseAreaRelationDOMapper trainCourseAreaRelationDOMapper) {
        this.trainCourseAreaRelationDOMapper = trainCourseAreaRelationDOMapper;
    }

    /**
     * 根据项目id列表获取培训区域关系列表
     * @param projectIds 项目id列表
     */
    public List<TrainCourseAreaRelationDTO> listByProjectIds(List<Integer> projectIds) {
        if(CollectionUtils.isEmpty(projectIds)) {
            return new ArrayList<>();
        }
        List<TrainCourseAreaRelationDO> trainCourseAreaRelationDOList = trainCourseAreaRelationDOMapper.selectByProjectIds(projectIds);
        return trainCourseAreaRelationDOList.stream().map(this::convertToDTO).collect(Collectors.toList());
    }

    public List<TrainCourseAreaRelationDTO> listByCourseIds(List<Integer> courseIds) {
        if(CollectionUtils.isEmpty(courseIds)) {
            return new ArrayList<>();
        }
        List<TrainCourseAreaRelationDO> trainCourseAreaRelationDOList = trainCourseAreaRelationDOMapper.selectByCourseIds(courseIds);
        return trainCourseAreaRelationDOList.stream().map(this::convertToDTO).collect(Collectors.toList());
    }

    /**
     * 获取培训区域关系列表
     * @param queryDTO
     */
    public List<TrainCourseAreaRelationDTO> getTrainCourseAreaRelationList(TrainCourseAreaRelationQueryDTO queryDTO) {
        List<TrainCourseAreaRelationDO> trainCourseAreaRelationDOList = trainCourseAreaRelationDOMapper.selectByQuery(queryDTO);
        return trainCourseAreaRelationDOList.stream().map(this::convertToDTO).collect(Collectors.toList());
    }

    /**
     * 保存或更新培训区域关系
     * @param projectId 项目ID
     * @param courseId 课程ID
     * @param trainCourseAreaRelationSaveOrUpdateDTOList 培训区域关系保存或更新DTO列表
     */
    public void saveOrUpdateTrainAreaRelation(Integer projectId, Integer courseId
    , List<TrainCourseAreaRelationSaveOrUpdateDTO> trainCourseAreaRelationSaveOrUpdateDTOList) {
        trainCourseAreaRelationDOMapper.deleteByCourseId(courseId);
        for (TrainCourseAreaRelationSaveOrUpdateDTO trainCourseAreaRelationSaveOrUpdateDTO : trainCourseAreaRelationSaveOrUpdateDTOList) {
            TrainCourseAreaRelationDO trainCourseAreaRelationDO = saveDTOToDO(trainCourseAreaRelationSaveOrUpdateDTO);
            trainCourseAreaRelationDO.setProjectId(projectId);
            trainCourseAreaRelationDO.setCourseId(courseId);
            if (trainCourseAreaRelationSaveOrUpdateDTO.getId() == null) {
                trainCourseAreaRelationDOMapper.insertSelective(trainCourseAreaRelationDO);
            } else {
                trainCourseAreaRelationDO.setInvalid(0);
                trainCourseAreaRelationDOMapper.updateByPrimaryKeySelective(trainCourseAreaRelationDO);
            }
        }
    }

    private TrainCourseAreaRelationDO saveDTOToDO(TrainCourseAreaRelationSaveOrUpdateDTO trainCourseAreaRelationSaveOrUpdateDTO) {
        TrainCourseAreaRelationDO trainCourseAreaRelationDO = new TrainCourseAreaRelationDO();
        BeanUtils.copyProperties(trainCourseAreaRelationSaveOrUpdateDTO, trainCourseAreaRelationDO);
        return trainCourseAreaRelationDO;
    }

    private TrainCourseAreaRelationDTO convertToDTO(TrainCourseAreaRelationDO trainCourseAreaRelationDO) {
        TrainCourseAreaRelationDTO trainCourseAreaRelationDTO = new TrainCourseAreaRelationDTO();
        BeanUtils.copyProperties(trainCourseAreaRelationDO, trainCourseAreaRelationDTO);
        return trainCourseAreaRelationDTO;
    }

    /**
     * 根据班次ID删除培训区域关系
     * 
     * @param courseId 班次ID
     */
    public void deleteByCourseId(Integer courseId) {
        trainCourseAreaRelationDOMapper.deleteByCourseId(courseId);
    }

}
