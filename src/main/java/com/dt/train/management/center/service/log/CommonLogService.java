package com.dt.train.management.center.service.log;

import java.util.List;
import java.util.stream.Collectors;

import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSONObject;
import com.dt.train.management.center.enums.LogTypeEnum;
import com.dt.train.management.center.mapper.CommonLogDOMapper;
import com.dt.train.management.center.model.dao.CommonLogDO;
import com.dt.train.management.center.model.dto.log.CommonLogDTO;
import com.dt.train.management.center.model.dto.log.CommonLogQueryDTO;
import com.dt.train.management.center.model.vo.log.CommonLogVO;

@Service
public class CommonLogService {

    private final CommonLogDOMapper commonLogDOMapper;

    public CommonLogService(CommonLogDOMapper commonLogDOMapper) {
        this.commonLogDOMapper = commonLogDOMapper;
    }

    /**
     * 保存日志
     * 
     * @param commonLogDTO
     */
    public void save(CommonLogDTO commonLogDTO) {
        CommonLogDO commonLogDO = dto2do(commonLogDTO);
        commonLogDOMapper.insertSelective(commonLogDO);
    }

    private CommonLogDO dto2do(CommonLogDTO commonLogDTO) {
        CommonLogDO commonLogDO = new CommonLogDO();
        BeanUtils.copyProperties(commonLogDTO, commonLogDO);
        commonLogDO.setLogType(commonLogDTO.getLogType().getCode());
        return commonLogDO;
    }

    /**
     * 查询日志
     * 
     * @param commonLogQueryDTO
     * @return
     */
    public List<CommonLogVO> list(CommonLogQueryDTO commonLogQueryDTO) {
        List<CommonLogDO> commonLogDOS = commonLogDOMapper.selectByQuery(commonLogQueryDTO);
        return commonLogDOS.stream().map(this::do2vo).collect(Collectors.toList());
    }

    private CommonLogVO do2vo(CommonLogDO commonLogDO) {
        CommonLogVO commonLogVO = new CommonLogVO();
        BeanUtils.copyProperties(commonLogDO, commonLogVO);
        commonLogVO.setLogType(LogTypeEnum.getByCode(commonLogDO.getLogType()));
        commonLogVO.setDetail(JSONObject.parseObject(commonLogDO.getDetail()));
        return commonLogVO;
    }
}
