package com.dt.train.management.center.model.vo.user;

import com.dt.train.management.center.serializer.PhoneMaskSerializer;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class TrainUserVO {

    private Integer id;

    @ApiModelProperty(value = "（用户中心）用户id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long userId;

    @ApiModelProperty(value = "crm用户id")
    private String crmUserId;

    @ApiModelProperty(value = "姓名")
    private String userName;

    @ApiModelProperty(value = "所属区域ID（多个区域分号分隔）")
    private String deptId;

    @ApiModelProperty(value = "所属区域名称（多个区域分号分隔）")
    private String deptName;

    @ApiModelProperty(value = "管理区域ID（多个区域分号分隔）")
    private String manageDeptId;

    @ApiModelProperty(value = "管理区域名称（多个区域分号分隔）")
    private String manageDeptNames;

    @ApiModelProperty(value = "执行人员列表区域名称（二级区域显示为：xx区域-xx服务中心  多个区域分号分隔）")
    private String completeDeptName;

    @ApiModelProperty(value = "管理区域全名（多个区域分号分隔）")
    private String completeManageDeptNames;

    @ApiModelProperty(value = "职位")
    private String userPosition;

    @ApiModelProperty(value = "排班系统角色")
    private String userRole;

    @ApiModelProperty(value = "角色id")
    private Integer roleId;

    @ApiModelProperty(value = "角色名称")
    private String roleName;

    @ApiModelProperty(value = "在职状态 0-在职 1-离职")
    private Integer userStatus;

    @ApiModelProperty(value = "成员类型 0-正式员工 1-兼职人员")
    private Integer userType;

    @ApiModelProperty(value = "成员类型名称")
    private String userTypeName;

    @ApiModelProperty(value = "邮箱")
    private String email;

    @ApiModelProperty(value = "加密手机号码")
    @JsonSerialize(using = PhoneMaskSerializer.class)
    private String encPhone;

    @ApiModelProperty(value = "手机号码")
    private String phone;
    
    @ApiModelProperty(value = "办公地点")
    private String workPlace;

    @ApiModelProperty(value = "第一个所属区域ID")
    public String getFirstDeptId() {
        if (deptId != null && !deptId.isEmpty()) {
            String[] deptIds = deptId.split(";");
            return deptIds[0];
        }
        return null;
    }
}
