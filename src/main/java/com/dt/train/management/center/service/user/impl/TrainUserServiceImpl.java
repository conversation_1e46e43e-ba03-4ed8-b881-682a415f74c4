package com.dt.train.management.center.service.user.impl;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.dt.framework.business.dto.page.Pagination;
import com.dt.train.management.center.config.BusinessConfig;
import com.dt.train.management.center.enums.ManagementExceptionEnum;
import com.dt.train.management.center.enums.UserTypeEnum;
import com.dt.train.management.center.enums.RightLevelEnum;
import com.dt.train.management.center.mapper.SysRoleDOMapper;
import com.dt.train.management.center.mapper.TrainDeptDOMapper;
import com.dt.train.management.center.mapper.TrainUserDOMapper;
import com.dt.train.management.center.model.dao.SysRoleDO;
import com.dt.train.management.center.model.dao.TrainDeptDO;
import com.dt.train.management.center.model.dao.TrainUserDO;
import com.dt.train.management.center.model.dto.user.TrainUserQueryDTO;
import com.dt.train.management.center.model.vo.dept.DeptCacheVO;
import com.dt.train.management.center.model.vo.user.TrainUserVO;
import com.dt.train.management.center.service.cache.DeptCacheService;
import com.dt.train.management.center.service.user.TrainUserService;
import com.dt.train.management.center.utils.DeptUtils;
import com.dt.train.management.center.utils.ExceptionUtil;
import com.dt.train.management.center.utils.StringParseUtil;

import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
public class TrainUserServiceImpl implements TrainUserService {
    @Resource
    private TrainUserDOMapper trainUserDOMapper;
    @Resource
    private TrainDeptDOMapper trainDeptDOMapper;
    @Resource
    private DeptCacheService deptCacheService;
    @Autowired
    private SysRoleDOMapper sysRoleDOMapper;
    @Autowired
    private BusinessConfig businessConfig;

    /**
     * 查询角色用户数
     *
     * @param roleId 角色ID
     * @return 角色用户数
     */
    @Override
    public Integer countUsersByRoleId(Integer roleId) {
        return trainUserDOMapper.countUsersByRoleId(roleId);
    }


    /**
     * 校验用户是否有当前部门权限
     *
     * @param userId 用户id
     * @param userDeptIdsStr 用户部门id
     * @param deptId 部门id
     */
    @Override
    public RightLevelEnum getRightLevel(Long userId, String userDeptIdsStr, Integer deptId) {
        //  参数校验
        if (userId == null) {
            throw ExceptionUtil.businessException(ManagementExceptionEnum.HAS_NO_ACCESS_TO_PROJECT);
        }
        List<Integer> directDeptIds = StringParseUtil.semicolonSeparated(userDeptIdsStr);

        //  优先检查超管权限
        if (isSuperAdmin(directDeptIds)) {
            log.debug("用户[{}]具有超管权限，直接通过校验", userId);
            return RightLevelEnum.EDIT;
        }

        if (deptId == null) {
            return RightLevelEnum.VIEW;
        }

        //  检查当前部门是否包含在用户的直接部门中
        if (DeptUtils.isSubOf(String.valueOf(deptId), userDeptIdsStr)) {
            return RightLevelEnum.EDIT;
        }

        return RightLevelEnum.VIEW;
    }

    /**
     * 校验用户是否有当前部门权限
     *
     * @param userId 用户id
     * @param deptId 部门id
     */
    @Override
    public void checkUserAndDepartment(Long userId, String deptId) {
        //  参数校验
        if (userId == null) {
            throw ExceptionUtil.businessException(ManagementExceptionEnum.HAS_NO_ACCESS_TO_PROJECT);
        }
        TrainUserDO trainUserDO = trainUserDOMapper.selectByUserId(userId);
        if (trainUserDO == null) {
            throw ExceptionUtil.businessException(ManagementExceptionEnum.HAS_NO_ACCESS_TO_PROJECT);
        }
        String deptIdsStr = trainUserDO.getDeptIds();
        if (StringUtils.isBlank(deptIdsStr)) {
            throw ExceptionUtil.businessException(ManagementExceptionEnum.HAS_NO_ACCESS_TO_PROJECT);
        }
        List<Integer> directDeptIds =  StringParseUtil.semicolonSeparated(deptIdsStr);

        //  优先检查超管权限
        if (isSuperAdmin(directDeptIds)) {
            log.info("用户[{}]具有超管权限，直接通过校验", userId);
            return;
        }

        // 非超管还想查全部，拒绝访问
        if (StringUtils.isBlank(deptId)) {
            throw ExceptionUtil.businessException(ManagementExceptionEnum.HAS_NO_ACCESS_TO_PROJECT);
        }
        Integer deptIdInt = Integer.parseInt(deptId);

        //  检查当前部门是否包含在用户的直接部门中
        if (directDeptIds.contains(deptIdInt)) {
            return;
        }

        //  检查当前部门是否包含在用户的子部门中
        if (!checkNormalUserPermission(directDeptIds, deptIdInt)) {
            throw ExceptionUtil.businessException(ManagementExceptionEnum.HAS_NO_ACCESS_TO_PROJECT);
        }
    }

    /**
     * @param query
     * @return
     */
    @Override
    public Pagination<TrainUserVO> pageQuery(TrainUserQueryDTO query) {
        Pagination<TrainUserVO> pagination = new Pagination<>();

        pagination.setPageIndex(query.getPageIndex());
        pagination.setPageSize(query.getPageSize());

        // 处理部门ID筛选条件
        buildDeptIds(query);

        Long total = trainUserDOMapper.countByQuery(query);
        // 查询所有满足基本条件的用户
        List<TrainUserDO> userDOs = trainUserDOMapper.selectByQuery(query);

        // 构建部门名称映射
        List<DeptCacheVO>  cachedDeptList = deptCacheService.getCachedDeptList();
        Map<String, DeptCacheVO> deptNameConfigVOMap = cachedDeptList.stream()
                    .collect(Collectors.toMap(deptCacheVO -> String.valueOf(deptCacheVO.getDeptId()),
                            configVO -> configVO,
                            (existing, replacement) -> existing));

        Map<Integer, String> sysRoleMap = sysRoleDOMapper.selectAll().stream().collect(Collectors.toMap(
                SysRoleDO::getId, SysRoleDO::getRoleName,
                (existing, replacement) -> existing));
        pagination.setTotal(total);
        pagination.setRows(userDOs.stream().map(user -> do2vo(deptNameConfigVOMap, user, sysRoleMap))
                .collect(Collectors.toList()));

        return pagination;
    }

    /**
     * @param query
     * @return
     */
    @Override
    public List<TrainUserVO> getByQuery(TrainUserQueryDTO query) {
        List<TrainUserDO> userDOs = trainUserDOMapper.selectByQuery(query);
        if (userDOs == null || userDOs.isEmpty()) {
            return Collections.emptyList();
        }
        List<TrainUserVO> result = new ArrayList<>();
        for (TrainUserDO userDO : userDOs) {
            TrainUserVO trainUserVO = new TrainUserVO();
            BeanUtils.copyProperties(userDO, trainUserVO);
            // 设置成员类型名称
            trainUserVO.setUserTypeName(UserTypeEnum.getByCode(userDO.getUserType()).getDesc());
            result.add(trainUserVO);
        }
        return result;
    }

    private TrainUserVO do2vo(Map<String, DeptCacheVO> deptNameConfigVOMap, TrainUserDO user, Map<Integer, String> sysRoleMap) {
        TrainUserVO vo = new TrainUserVO();
        BeanUtils.copyProperties(user, vo);
        DeptCacheVO deptCacheVO = buildDeptNames(user.getDeptIds(), deptNameConfigVOMap);
        DeptCacheVO manageDeptCacheVO = buildDeptNames(user.getManageDeptIds(), deptNameConfigVOMap);
        vo.setDeptId(user.getDeptIds());
        vo.setManageDeptId(user.getManageDeptIds());
        vo.setDeptName(deptCacheVO.getTrainDeptName());
        vo.setCompleteDeptName(deptCacheVO.getCompleteDeptName());
        vo.setManageDeptNames(manageDeptCacheVO.getTrainDeptName());
        vo.setCompleteManageDeptNames(manageDeptCacheVO.getCompleteDeptName());
        vo.setRoleName(sysRoleMap.getOrDefault(user.getRoleId(), ""));
        vo.setEncPhone(user.getPhone());
        vo.setPhone(null); // 清除原始手机号码，避免泄露
        // 设置成员类型名称
        vo.setUserTypeName(UserTypeEnum.getByCode(user.getUserType()).getDesc());
        return vo;
    }

    private void buildDeptIds(TrainUserQueryDTO query) {
        if(query.getQueryScope() == 1){
            query.setDeptIds(Collections.singletonList(query.getDeptId()));
            return;
        }
        List<Integer> allChildrenDeptIds = deptCacheService.getAllChildrenDeptIds(query.getDeptId());
        query.setDeptIds(allChildrenDeptIds);
        query.setDeptId(null);
    }

    @Override
    public TrainUserVO getUserInfoByCrmUserId(String crmUserId) {
        if(crmUserId == null){
            return null;
        }
        TrainUserDO trainUserDO = trainUserDOMapper.selectByCrmUserId(crmUserId);
        if (trainUserDO == null) {
            return null;
        }
        TrainUserVO trainUserVO = new TrainUserVO();
        BeanUtils.copyProperties(trainUserDO, trainUserVO);
        trainUserVO.setDeptId(trainUserDO.getDeptIds());
        trainUserVO.setDeptName(buildCompleteDeptNames(trainUserDO.getDeptIds()));
        // 设置成员类型名称
        trainUserVO.setUserTypeName(UserTypeEnum.getByCode(trainUserDO.getUserType()).getDesc());
        return trainUserVO;
    }

    /**
     * 通过用户ID获取用户信息
     * @return TrainUserVO
     */
    @Override
    public TrainUserVO getUserInfoByUserId(Long userId) {
        TrainUserDO trainUserDO = trainUserDOMapper.selectByUserId(userId);
        if (trainUserDO == null) {
            return null;
        }
        List<DeptCacheVO>  cachedDeptList = deptCacheService.getCachedDeptList();
        Map<String, DeptCacheVO> deptNameConfigVOMap = cachedDeptList.stream()
                    .collect(Collectors.toMap(deptCacheVO -> String.valueOf(deptCacheVO.getDeptId()),
                            configVO -> configVO,
                            (existing, replacement) -> existing));
        TrainUserVO trainUserVO = do2vo(deptNameConfigVOMap, trainUserDO, new HashMap<>());
        trainUserVO.setPhone(trainUserVO.getEncPhone());
        return trainUserVO;
    }

    /**
     * 通过部门ID字符串构建部门名称（简单名称和完整路径名称）
     * @param userDeptIdsStr 部门ID字符串，如 "1001,1002,1003"
     * @param deptNameConfigVOMap 部门缓存数据Map
     * @return DeptCacheVO 包含两种格式的部门名称
     */
    public DeptCacheVO buildDeptNames(String userDeptIdsStr, Map<String, DeptCacheVO> deptNameConfigVOMap) {
        DeptCacheVO result = new DeptCacheVO();
        if (StringUtils.isBlank(userDeptIdsStr)) {
            return result;
        }

        // 解析部门ID列表
        List<Integer> deptIds = StringParseUtil.semicolonSeparated(userDeptIdsStr);
        if (deptIds.isEmpty()) {
            return result;
        }

        // 准备两个列表分别存储两种名称
        List<String> simpleNames = new ArrayList<>();
        List<String> fullNames = new ArrayList<>();

        // 遍历所有部门ID
        for (Integer deptId : deptIds) {
            DeptCacheVO deptCache = deptNameConfigVOMap.get(deptId.toString());
            if (deptCache != null) {
                // 添加简单部门名称
                if (StringUtils.isNotBlank(deptCache.getTrainDeptName())) {
                    simpleNames.add(deptCache.getTrainDeptName());
                }
                // 添加完整路径名称
                if (StringUtils.isNotBlank(deptCache.getCompleteDeptName())) {
                    fullNames.add(deptCache.getCompleteDeptName());
                }
            }
        }

        // 拼接两种名称
        if (!simpleNames.isEmpty()) {
            result.setTrainDeptName(String.join(";", simpleNames));
        }
        if (!fullNames.isEmpty()) {
            result.setCompleteDeptName(String.join(";", fullNames));
        }

        return result;
    }

    /**
     * 通过部门ID字符串构建指定部门名称字符串（二级区域显示为：xx区域-xx服务中心，分号分隔）
     */
    public String buildDeptNames2(String userDeptIdsStr) {
        if (StringUtils.isBlank(userDeptIdsStr)) {
            return "";
        }

        List<Integer> deptIds = StringParseUtil.semicolonSeparated(userDeptIdsStr);
        if (deptIds.isEmpty()) {
            return "";
        }

        // 查询所有部门数据（包括父部门和子部门）
        List<TrainDeptDO> allDepts = trainDeptDOMapper.selectAllDepts();
        if (allDepts == null || allDepts.isEmpty()) {
            return "";
        }

        // 构建部门ID到部门的映射（仅包含传入的部门ID且非隐藏的部门）
        Map<Integer, TrainDeptDO> deptMap = allDepts.stream()
                .filter(Objects::nonNull)
                .filter(dept -> deptIds.contains(dept.getId()))
                .filter(dept -> dept.getHidden() == 0)
                .collect(Collectors.toMap(
                        TrainDeptDO::getId,
                        dept -> dept,
                        (existing, replacement) -> existing
                ));

        // 构建父部门ID到父部门的映射（所有一级部门）
        Map<Integer, TrainDeptDO> parentDeptMap = allDepts.stream()
                .filter(dept -> dept != null && dept.getDeptLevel() == 1)
                .collect(Collectors.toMap(
                        TrainDeptDO::getId,
                        dept -> dept,
                        (existing, replacement) -> existing
                ));

        return deptIds.stream()
                .map(deptId -> {
                    TrainDeptDO dept = deptMap.get(deptId);
                    if (dept == null) {
                        return "";
                    }

                    // 如果是二级部门，尝试获取父部门简称
                    if (dept.getDeptLevel() == 2) {
                        TrainDeptDO parentDept = parentDeptMap.get(dept.getParentId());
                        if (parentDept != null && StringUtils.isNotBlank(parentDept.getDeptNameShort())) {
                            return parentDept.getDeptNameShort() + "-" + dept.getDeptName();
                        }
                    }
                    // 其他情况直接返回部门名称
                    return dept.getDeptName();
                })
                .filter(name -> !name.isEmpty())
                .collect(Collectors.joining(";"));
    }
    /**
     * 通过部门ID字符串构建指定部门名称字符串（二级区域显示为：一级全称-二级全称，多个部门分号分隔）
     */
    public String buildCompleteDeptNames(String userDeptIdsStr) {
        if (StringUtils.isBlank(userDeptIdsStr)) {
            return "";
        }

        List<Integer> deptIds = StringParseUtil.semicolonSeparated(userDeptIdsStr);
        if (deptIds.isEmpty()) {
            return "";
        }

        // 查询所有部门数据（包括父部门和子部门）
        List<TrainDeptDO> allDepts = trainDeptDOMapper.selectAllDepts();
        if (allDepts == null || allDepts.isEmpty()) {
            return "";
        }

        // 构建部门ID到部门的映射（仅包含传入的部门ID且非隐藏的部门）
        Map<Integer, TrainDeptDO> deptMap = allDepts.stream()
                .filter(Objects::nonNull)
                .filter(dept -> deptIds.contains(dept.getId()))
                .filter(dept -> dept.getHidden() == 0)
                .collect(Collectors.toMap(
                        TrainDeptDO::getId,
                        dept -> dept,
                        (existing, replacement) -> existing
                ));

        // 构建父部门ID到父部门的映射（所有一级部门）
        Map<Integer, TrainDeptDO> parentDeptMap = allDepts.stream()
                .filter(dept -> dept != null && dept.getDeptLevel() == 1)
                .collect(Collectors.toMap(
                        TrainDeptDO::getId,
                        dept -> dept,
                        (existing, replacement) -> existing
                ));

        return deptIds.stream()
                .map(deptId -> {
                    TrainDeptDO dept = deptMap.get(deptId);
                    if (dept == null) {
                        return "";
                    }

                    // 如果是二级部门，尝试获取父部门全称
                    if (dept.getDeptLevel() == 2) {
                        TrainDeptDO parentDept = parentDeptMap.get(dept.getParentId());
                        if (parentDept != null && StringUtils.isNotBlank(parentDept.getDeptNameShort())) {
                            return parentDept.getDeptName() + "-" + dept.getDeptName();
                        }
                    }
                    // 其他情况直接返回部门名称
                    return dept.getDeptName();
                })
                .filter(name -> !name.isEmpty())
                .collect(Collectors.joining(";"));
    }

    /**
     * 判断用户是否属于超管部门
     */
    private boolean isSuperAdmin(List<Integer> directDeptIds) {
        return CollectionUtils.containsAny(businessConfig.getDeptAdminList(), directDeptIds);
    }

    /**
     * 检查targetDeptId是否属于directDeptIds的子部门
     * @param directDeptIds 直接部门ID列表（一般为用户有权限的部门）
     * @param targetDeptId 目标部门ID（需要校验是否在用户权限内的部门ID）
     */
    private boolean checkNormalUserPermission(List<Integer> directDeptIds, Integer targetDeptId) {
        if (directDeptIds == null || directDeptIds.isEmpty()) {
            return false;
        }
        //  使用映射查找所有子部门
        Set<Integer> allSubDeptIds = new HashSet<>();
        for (Integer directDeptId : directDeptIds) {
            List<Integer> allChildrenDeptIds = deptCacheService.getAllChildrenDeptIds(directDeptId);
            allSubDeptIds.addAll(allChildrenDeptIds);
        }
        // 检查目标部门是否在子部门集合中
        return allSubDeptIds.contains(targetDeptId);
    }

    @Override
    public void updateUserStatus(TrainUserDO userDO) {
        if (userDO == null || userDO.getId() == null || userDO.getUserStatus() == null) {
            throw new IllegalArgumentException("用户id和状态不能为空");
        }
        trainUserDOMapper.updateByPrimaryKeySelective(userDO);
    }
    
    /**
     * 获取所有离职用户ID列表
     * @return 离职用户ID列表
     */
    @Override
    public List<Long> getResignedUserIds() {
        // 创建查询条件：userStatus = 1（离职）
        TrainUserQueryDTO query = new TrainUserQueryDTO();
        query.setUserStatus(1); // 离职状态
        query.setPageIndex(null);
        query.setPageSize(null);

        // 查询所有离职用户（不设分页参数，获取全部）
        List<TrainUserDO> resignedUsers = trainUserDOMapper.selectByQuery(query);
        
        // 提取用户ID列表
        return resignedUsers.stream()
            .map(TrainUserDO::getUserId)
            .collect(Collectors.toList());
    }

}
