package com.dt.train.management.center.mapper.ex;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.dt.train.management.center.mapper.TrainCourseDOMapper;
import com.dt.train.management.center.model.dao.TrainCourseDO;
import com.dt.train.management.center.model.dao.TrainCourseDOWithBLOBs;
import com.dt.train.management.center.model.dto.project.TrainCourseQueryDTO;
import com.dt.train.management.center.model.vo.project.ProjectStatusSummaryVO;

public interface TrainCourseDOMapperEx extends TrainCourseDOMapper{

    long countByQuery(TrainCourseQueryDTO query);

    List<TrainCourseDO> selectByProjectIds(@Param("projectIds") List<Integer> projectIds);

    List<TrainCourseDO> selectByQuery(TrainCourseQueryDTO query);

    List<TrainCourseDO> selectByPrimaryKeys(@Param("ids") List<Integer> ids);

    List<TrainCourseDOWithBLOBs> selectBlobByPrimaryKeys(@Param("ids") List<Integer> ids);

    void processCompleteStatus();

    void processStartStatus();

    ProjectStatusSummaryVO getCourseStatusSummary(TrainCourseQueryDTO query);

    List<String> selectCrossAreaIds(@Param("excludeCourseAreaIds") List<String> excludeCourseAreaIds, @Param("userId") String userId);

}
