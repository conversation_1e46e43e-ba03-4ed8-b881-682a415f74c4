package com.dt.train.management.center.web.fetch;

import java.time.LocalDate;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;
import java.util.UUID;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.MultiValueMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.dt.framework.business.dto.web.common.WebResult;
import com.dt.train.management.center.export.enums.ReportFormat;
import com.dt.train.management.center.export.enums.ReportStatus;
import com.dt.train.management.center.export.enums.ReportType;
import com.dt.train.management.center.export.exception.ReportGenerationException;
import com.dt.train.management.center.export.models.ReportMetadata;
import com.dt.train.management.center.export.models.ReportRequest;
import com.dt.train.management.center.export.service.ReportService;
import com.dt.train.management.center.model.vo.report.ReportStatusVO;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;

@Api(tags = "报表导出")
@RestController
@RequestMapping("/report")
@Slf4j
public class ReportController {

    @Autowired
    private ReportService reportService;

    /**
     * 同步报表导出接口。
     * 示例 GET 请求:
     * /api/reports/export?reportType=SALES_REPORT&format=EXCEL&startDate=2023-01-01&endDate=2023-01-31
     * 或者 /api/reports/export?reportType=TRAIN_COURSE_REPORT&format=EXCEL
     * 支持所有 URL query 参数作为报表参数。
     */
    @ApiOperation(value = "同步报表导出")
    @GetMapping("/export")
    public WebResult<String> exportReport(
            @RequestParam ReportType reportType,
            @RequestParam ReportFormat format,
            @RequestParam MultiValueMap<String, String> allRequestParams // 使用MultiValueMap捕获所有请求参数，包括多值参数
    ) {
        // 验证只允许 EXCEL 格式
        if (format != ReportFormat.EXCEL) {
            return WebResult.error("仅支持 EXCEL 格式的同步导出。");
        }

        ReportRequest request = buildReportRequest(reportType, format, allRequestParams);

        try {
            String url = reportService.generateReport(request);

            return WebResult.successData(url);
        } catch (IllegalArgumentException e) {
            // 参数错误，例如报表类型不支持
            log.error("报表导出请求参数错误: " + e.getMessage(), e);
            return WebResult.error(e.getMessage());
        } catch (ReportGenerationException e) {
            // 报表生成失败（例如Excel库内部错误）
            log.error("报表生成失败: " + e.getMessage(), e);
            return WebResult.error("报表生成失败: " + e.getMessage());
        } catch (Exception e) {
            // 其他未知错误
            log.error("服务器内部错误: " + e.getMessage(), e);
            return WebResult.error("服务器内部错误，请稍后重试。");
        }
    }

    /**
     * 构建报表请求对象
     * @param reportType 报表类型
     * @param format 报表格式
     * @param allRequestParams 所有请求参数
     * @return 组装好的ReportRequest对象
     */
    private ReportRequest buildReportRequest(ReportType reportType, ReportFormat format, 
                                           MultiValueMap<String, String> allRequestParams) {
        ReportRequest request = new ReportRequest();
        request.setReportType(reportType);
        request.setFormat(format);

        // 过滤掉 reportType 和 format，并将其他参数作为动态参数传递
        Map<String, Object> parameters = new HashMap<>();
        allRequestParams.forEach((key, values) -> {
            if (!"reportType".equals(key) && !"format".equals(key)) {
                if (!values.isEmpty()) {
                    String value = values.get(0);
                    // 简单示例：日期参数转换，实际应用中需更完善的类型转换逻辑
                    if (key.toLowerCase().endsWith("date") || key.toLowerCase().endsWith("time")) {
                        try {
                            parameters.put(key, LocalDate.parse(value)); // 假设日期格式为YYYY-MM-DD
                        } catch (Exception e) {
                            log.error("日期参数转换失败: " + key + "=" + value);
                            // 可以在这里抛出更具体的 Bad Request 异常
                        }
                    } else {
                        parameters.put(key, value);
                    }
                }
            }
        });
        // 示例：可以添加默认报表标题
        parameters.putIfAbsent("reportTitle", reportType.name() + " 报表");

        request.setParameters(parameters);
        return request;
    }

    /**
     * 异步报表导出请求接口。
     * 示例 POST 请求: /export/async
     * 请求体 Body (JSON): { "reportType": "SALES_REPORT", "format": "EXCEL",
     * "parameters": { "startDate": "2023-01-01", "endDate": "2023-01-31" } }
     * 或者 { "reportType": "TRAIN_COURSE_REPORT", "format": "EXCEL", "parameters": {
     * "projectId": 101 } }
     */
    @ApiOperation(value = "异步报表导出")
    @GetMapping("/export/async")
    public WebResult<ReportStatusVO> requestAsyncReport(@RequestParam ReportType reportType,
            @RequestParam ReportFormat format,
            @RequestParam MultiValueMap<String, String> allRequestParams) {
        ReportRequest request = buildReportRequest(reportType, format, allRequestParams);

        String reportId = UUID.randomUUID().toString(); // 生成唯一报表ID

        log.info("接收到异步报表请求，生成ID: " + reportId);
        log.info(
                "请求参数: " + reportType + ", " + format + ", " + request.getParameters());

        // 异步生成报表
        reportService.generateReportAsync(request, reportId);

        ReportStatusVO response = ReportStatusVO.builder()
                .reportId(reportId)
                .status(ReportStatus.PROCESSING)
                .progress(0.0)
                .message("报表正在异步生成中，请稍后使用报表ID查询或下载。")
                .build();
        return WebResult.successData(response);
    }

    /**
     * 查询异步报表生成进度接口。
     * 示例 GET 请求: /api/reports/status?reportId=xxx
     * 返回报表ID和当前状态。
     */
    @ApiOperation(value = "查询异步报表生成进度")
    @GetMapping("/status")
    public WebResult<ReportStatusVO> getReportStatus(@ApiParam("报表ID") @RequestParam String reportId) {
        Optional<ReportMetadata> metadata = reportService.getReportMetadata(reportId);

        if (metadata.isPresent()) {
            ReportMetadata report = metadata.get();
            ReportStatusVO.ReportStatusVOBuilder builder = ReportStatusVO.builder()
                    .reportId(report.getReportId())
                    .status(report.getStatus());

            if (report.getStatus() == ReportStatus.COMPLETED) {
                builder.progress(1.0)
                        .message("报表已生成完成。")
                        .downloadUrl(report.getUrl());
            } else if (report.getStatus() == ReportStatus.FAILED) {
                builder.message("报表生成失败。");
            } else { // PROCESSING
                long elapsedTime = System.currentTimeMillis() - report.getCreationTime();
                log.info("报表ID " + reportId + " 已经处理了 " + elapsedTime + " 毫秒");
                double progress = Math.min(0.9, elapsedTime / 30000.0); // 假设30秒完成
                builder.progress(progress)
                        .message("报表正在处理中，请稍候。");
            }
            return WebResult.successData(builder.build());
        } else {
            return WebResult.error("报表ID不存在。");
        }
    }

}
