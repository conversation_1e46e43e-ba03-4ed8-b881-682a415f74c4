package com.dt.train.management.center.export.enums;

import com.dt.train.management.center.export.models.TrainCourseExcelDTO;
import com.dt.train.management.center.export.models.TrainProjectExcelDTO;

import lombok.Getter;

@Getter
public enum ReportType {
    TRAIN_PROJECT_REPORT("项目导出", TrainProjectExcelDTO.class, "项目导出.xlsx"),
    TRAIN_COURSE_REPORT("班次导出", TrainCourseExcelDTO.class, "班次导出.xlsx");         // 班次报表
    // USER_ACTIVITY_REPORT, // 用户活跃度报表
    // INVENTORY_REPORT      // 库存报表

    private String sheetName;
    private Class<?> headClass;
    private String fileName;

    ReportType(String sheetName, Class<?> headClass, String fileName) {
        this.sheetName = sheetName;
        this.headClass = headClass;
        this.fileName = fileName;
    }
    
    
}