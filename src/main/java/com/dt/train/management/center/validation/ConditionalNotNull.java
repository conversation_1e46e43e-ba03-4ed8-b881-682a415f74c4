package com.dt.train.management.center.validation;

import javax.validation.Constraint;
import javax.validation.Payload;
import java.lang.annotation.*;

/**
 * 条件非空验证注解
 * 根据指定字段的值来决定当前字段是否必须非空
 */
@Target({ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
@Constraint(validatedBy = ConditionalNotNullValidator.class)
@Documented
@Repeatable(ConditionalNotNull.List.class)
public @interface ConditionalNotNull {
    
    String message() default "字段不能为空";
    
    Class<?>[] groups() default {};
    
    Class<? extends Payload>[] payload() default {};
    
    /**
     * 要验证的字段名
     */
    String field();
    
    /**
     * 条件字段名
     */
    String conditionField();
    
    /**
     * 条件字段的值，当条件字段等于这些值时，目标字段必须非空
     */
    String[] conditionValues();

    /**
     * 用于支持重复注解
     */
    @Target({ElementType.TYPE})
    @Retention(RetentionPolicy.RUNTIME)
    @Documented
    @interface List {
        ConditionalNotNull[] value();
    }
}
