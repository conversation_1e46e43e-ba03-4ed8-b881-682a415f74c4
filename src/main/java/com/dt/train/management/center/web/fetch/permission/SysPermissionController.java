package com.dt.train.management.center.web.fetch.permission;

import java.util.List;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.dt.framework.business.dto.web.common.WebResult;
import com.dt.train.management.center.model.vo.permission.SysPermissionTreeVO;
import com.dt.train.management.center.service.permission.SysPermissionService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

@RestController
@RequestMapping("/fetch/permission")
@Api(tags = "权限相关接口")
public class SysPermissionController {

    private final SysPermissionService sysPermissionService;

    public SysPermissionController(SysPermissionService sysPermissionService) {
        this.sysPermissionService = sysPermissionService;
    }

    /**
     * 获取权限树
     *
     * @return 权限树
     */
    @ApiOperation(value = "获取权限树", notes = "获取系统权限的树形结构")
    @GetMapping("/tree")
    public WebResult<List<SysPermissionTreeVO>> getPermissionTree() {
        return WebResult.successData(sysPermissionService.getPermissionTree());
    }

}
