package com.dt.train.management.center.mapper.ex;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.dt.train.management.center.mapper.TrainTaskDOMapper;
import com.dt.train.management.center.model.dao.TrainTaskDO;
import com.dt.train.management.center.model.dto.task.TrainTaskQueryDTO;

@Mapper
public interface TrainTaskDOMapperEx extends TrainTaskDOMapper{

    List<TrainTaskDO> selectByQuery(TrainTaskQueryDTO queryDTO);

    List<TrainTaskDO> selectByCourseIds(@Param("courseIds") List<Integer> courseIds);

    void deleteByCourseId(Integer courseId);
}
