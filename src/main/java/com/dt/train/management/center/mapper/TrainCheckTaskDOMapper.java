package com.dt.train.management.center.mapper;

import com.dt.train.management.center.model.dao.TrainCheckTaskDO;

public interface TrainCheckTaskDOMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(TrainCheckTaskDO row);

    int insertSelective(TrainCheckTaskDO row);

    TrainCheckTaskDO selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(TrainCheckTaskDO row);

    int updateByPrimaryKey(TrainCheckTaskDO row);
}