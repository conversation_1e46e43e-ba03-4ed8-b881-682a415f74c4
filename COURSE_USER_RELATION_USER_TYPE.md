# 班次用户关联表用户类型字段功能文档

## 功能概述

在人员班次关联表（train_course_user_relation）中添加了用户类型字段（user_type），用于标识关联用户的类型（正式员工或兼职人员），便于筛选和处理不同类型的用户。

## 数据库变更

### 1. 表结构变更
```sql
-- 为train_course_user_relation表添加成员类型字段
ALTER TABLE train_management_center.train_course_user_relation 
ADD COLUMN `user_type` tinyint(1) NOT NULL DEFAULT 0 COMMENT '成员类型，0-正式员工，1-兼职人员' 
AFTER `user_name`;
```

### 2. 数据迁移
```sql
-- 更新现有数据的用户类型
UPDATE train_management_center.train_course_user_relation tcur
INNER JOIN train_management_center.train_user tu ON tcur.user_id = tu.user_id
SET tcur.user_type = tu.user_type
WHERE tcur.invalid = 0 AND tu.invalid = 0;
```

## 字段说明

| 字段名 | 类型 | 说明 | 默认值 |
|--------|------|------|--------|
| user_type | tinyint(1) | 成员类型，0-正式员工，1-兼职人员 | 0 |

## 功能特性

### 1. 自动同步用户类型
- 在创建班次用户关联时，系统会自动从用户表（train_user）中获取用户类型并设置到关联表中
- 确保关联表中的用户类型与用户表保持一致

### 2. 用户类型名称显示
- 在查询班次用户关联信息时，会自动设置用户类型名称（userTypeName）
- 正式员工显示为"正式员工"
- 兼职人员显示为"兼职人员"

### 3. 部门变更时同步更新
- 当用户部门发生变更时，系统会同时更新关联表中的用户类型
- 确保数据的一致性

## API 响应示例

### 班次用户关联查询响应
```json
{
  "success": true,
  "data": [
    {
      "id": 1,
      "userId": 1001,
      "userName": "兼职人员1",
      "userType": 1,
      "userTypeName": "兼职人员",
      "userDeptId": "10000",
      "userDeptName": "技术部",
      "courseId": 100,
      "relationType": 1,
      "startDate": "2024-01-01 09:00",
      "endDate": "2024-01-01 18:00"
    },
    {
      "id": 2,
      "userId": 1002,
      "userName": "张三",
      "userType": 0,
      "userTypeName": "正式员工",
      "userDeptId": "10000",
      "userDeptName": "技术部",
      "courseId": 100,
      "relationType": 1,
      "startDate": "2024-01-01 09:00",
      "endDate": "2024-01-01 18:00"
    }
  ]
}
```

## 筛选功能

### 1. 按用户类型筛选
可以通过用户类型字段对班次用户关联进行筛选：

```sql
-- 查询兼职人员的班次关联
SELECT * FROM train_course_user_relation 
WHERE user_type = 1 AND invalid = 0;

-- 查询正式员工的班次关联
SELECT * FROM train_course_user_relation 
WHERE user_type = 0 AND invalid = 0;
```

### 2. 统计功能
可以按用户类型进行统计分析：

```sql
-- 统计各类型用户的班次参与情况
SELECT 
    user_type,
    CASE user_type 
        WHEN 0 THEN '正式员工' 
        WHEN 1 THEN '兼职人员' 
    END as user_type_name,
    COUNT(*) as relation_count
FROM train_course_user_relation 
WHERE invalid = 0 
GROUP BY user_type;
```

## 业务价值

### 1. 便于管理
- 可以快速识别和筛选不同类型的用户
- 便于对兼职人员和正式员工进行分类管理

### 2. 数据分析
- 支持按用户类型进行统计分析
- 便于了解不同类型用户的培训参与情况

### 3. 业务决策
- 为人力资源管理提供数据支持
- 便于制定针对性的培训策略

## 注意事项

1. **数据一致性**：系统会自动维护用户类型的一致性，无需手动设置
2. **历史数据**：已通过数据迁移脚本更新了历史数据
3. **性能考虑**：用户类型字段已建立索引，查询性能良好
4. **扩展性**：如需新增用户类型，只需修改枚举类即可
