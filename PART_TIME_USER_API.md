# 兼职人员创建接口文档

## 功能概述

本文档描述了兼职人员创建功能的特殊处理逻辑。

## 接口信息

- **接口路径**: `POST /manager/user`
- **接口描述**: 新增或编辑成员（支持兼职人员特殊逻辑）

## 兼职人员特殊功能

### 1. 自动生成名字
当创建兼职人员时，如果没有提供姓名（userName为空），系统会自动生成名字：
- 格式：`兼职人员 + 当前组织下兼职人员的次序数字`
- 例如：`兼职人员1`、`兼职人员2`、`兼职人员3`

### 2. 字段验证规则
- **正式员工（userType = 0）**：
  - 手机号（phone）：必填
  - 角色（roleId）：必填
  - 所属区域（deptId）：必填
  - 管理区域（manageDeptId）：可选

- **兼职人员（userType = 1）**：
  - 手机号（phone）：可选
  - 角色（roleId）：可选
  - 所属区域（deptId）：必填
  - 管理区域（manageDeptId）：自动设置为与所属区域相同

### 3. 管理区域自动设置
兼职人员的管理区域ID会自动设置为与所属区域ID相同，无需手动指定。

## 请求示例

### 创建兼职人员（最简参数）
```json
{
  "userType": 1,
  "deptId": "10000"
}
```

### 创建兼职人员（完整参数）
```json
{
  "userName": "张三",
  "userType": 1,
  "deptId": "10000",
  "phone": "13800138000",
  "roleId": 2,
  "userPosition": "临时工作",
  "workPlace": "北京办公室"
}
```

### 创建正式员工
```json
{
  "userName": "李四",
  "userType": 0,
  "deptId": "10000",
  "manageDeptId": "10000;10001",
  "phone": "13800138001",
  "roleId": 1,
  "userPosition": "高级工程师",
  "workPlace": "上海办公室"
}
```

## 响应格式

成功响应：
```json
{
  "success": true,
  "code": "200",
  "message": "操作成功"
}
```

## 注意事项

1. 兼职人员的次序数字是基于当前部门下已存在的兼职人员数量计算的
2. 如果兼职人员提供了自定义姓名，则不会自动生成
3. 兼职人员的管理区域会自动设置，即使前端传递了manageDeptId参数也会被覆盖
4. 兼职人员可以没有手机号，但如果提供了手机号，仍需要保证唯一性
