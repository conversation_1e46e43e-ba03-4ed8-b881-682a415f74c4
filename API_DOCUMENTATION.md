# 新增或编辑成员接口文档

## 接口概述

本接口用于新增或编辑系统中的成员信息，支持创建新用户和更新现有用户的信息。

## 接口详情

### 请求信息

- **接口路径**: `POST /manager/user`
- **接口描述**: 新增或编辑成员
- **Content-Type**: `application/json`

### 请求参数

| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| id | Integer | 否 | 用户ID，新增时不传，编辑时必传 |
| userName | String | 是 | 用户姓名 |
| deptId | String | 是 | 所属区域ID（多个区域分号分隔） |
| manageDeptId | String | 否 | 管理区域ID（多个区域分号分隔） |
| userPosition | String | 否 | 职位 |
| roleId | Integer | 是 | 角色ID |
| phone | String | 是 | 手机号码 |
| workPlace | String | 否 | 办公地点 |

### 请求示例

#### 新增用户
```json
{
  "userName": "张三",
  "deptId": "1001;1002",
  "manageDeptId": "1001",
  "userPosition": "高级工程师",
  "roleId": 1,
  "phone": "13800138000",
  "workPlace": "北京总部"
}
```

#### 编辑用户
```json
{
  "id": 123,
  "userName": "张三",
  "deptId": "1001;1003",
  "manageDeptId": "1001;1003",
  "userPosition": "资深工程师",
  "roleId": 2,
  "phone": "13800138000",
  "workPlace": "上海分部"
}
```

### 响应信息

#### 成功响应
```json
{
  "success": true,
  "code": "200",
  "message": "操作成功",
  "data": null
}
```

#### 失败响应
```json
{
  "success": false,
  "code": "400",
  "message": "具体错误信息",
  "data": null
}
```

## 业务逻辑

### 新增用户流程
1. 参数校验（姓名、手机号、所属区域、角色不能为空）
2. 检查手机号是否已存在
3. 生成用户ID（通过用户中心或生成规则）
4. 设置默认值（在职状态、有效标识等）
5. 插入数据库记录

### 编辑用户流程
1. 参数校验
2. 检查用户是否存在
3. 检查手机号是否被其他用户使用
4. 更新用户信息
5. 清除相关缓存

## 错误码说明

| 错误码 | 错误信息 | 说明 |
|--------|----------|------|
| 400 | 用户姓名不能为空 | 必填参数校验失败 |
| 400 | 手机号不能为空 | 必填参数校验失败 |
| 400 | 所属区域不能为空 | 必填参数校验失败 |
| 400 | 角色不能为空 | 必填参数校验失败 |
| 400 | 手机号已存在 | 新增用户时手机号重复 |
| 400 | 手机号已被其他用户使用 | 编辑用户时手机号冲突 |
| 404 | 用户不存在 | 编辑时找不到对应用户 |

## 注意事项

1. **手机号唯一性**: 系统中每个手机号只能对应一个用户
2. **部门ID格式**: 多个部门ID使用分号(;)分隔
3. **用户状态**: 新增用户默认为在职状态(0)
4. **缓存处理**: 编辑用户后会自动清除相关缓存
5. **权限控制**: 需要管理员权限才能调用此接口

## 相关接口

- `GET /manager/user/getByUserId` - 获取指定用户信息
- `POST /manager/user/changeStatus` - 修改用户状态
- `DELETE /manager/user/_delete` - 删除用户

## 技术实现

### 核心类文件
- **Controller**: `TrainUserManagerController.java`
- **Manager**: `TrainUserManager.java`
- **DTO**: `TrainUserSaveDTO.java`
- **Entity**: `TrainUserDO.java`

### 数据库表
- **表名**: `train_user`
- **主要字段**: id, user_id, user_name, phone, dept_ids, manage_dept_ids, role_id, user_position, work_place

### 依赖服务
- **用户中心服务**: 用于获取或生成用户ID
- **Redis缓存**: 用于缓存用户信息
- **部门服务**: 用于验证部门信息
