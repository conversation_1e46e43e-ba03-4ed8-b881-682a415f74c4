<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>

	<groupId>com.dt</groupId>
	<artifactId>train-management-center</artifactId>
	<version>1.0-SNAPSHOT</version>
	<packaging>jar</packaging>

	<name>train-management-center</name>

	<parent>
		<groupId>com.dt</groupId>
		<artifactId>project-parent</artifactId>
		<version>2.4.0.2-SNAPSHOT</version>
		<relativePath/>
	</parent>

	<dependencies>
		<!-- dt framework-core ：spring boot + spring cloud  + extension-->
		<dependency>
			<groupId>com.dt</groupId>
			<artifactId>framework-core</artifactId>
			<version>*******-SNAPSHOT</version>
		</dependency>

		<!-- dt framework-config ：apollo + env_meta_data-->
		<dependency>
			<groupId>com.dt</groupId>
			<artifactId>framework-config</artifactId>
			<version>*******-SNAPSHOT</version>
		</dependency>

		<!-- dt framework-config ：sleuth + zipkin + kafka-->
		<dependency>
			<groupId>com.dt</groupId>
			<artifactId>framework-trace</artifactId>
			<version>*******-SNAPSHOT</version>
		</dependency>

		<!-- dt framework-log ：logstash + config xml-->
		<dependency>
			<groupId>com.dt</groupId>
			<artifactId>framework-log</artifactId>
			<version>*******-SNAPSHOT</version>
		</dependency>

		<!-- dt framework-bussiness : common business utils-->
		<dependency>
			<groupId>com.dt</groupId>
			<artifactId>framework-business</artifactId>
			<version>*******-SNAPSHOT</version>
		</dependency>

		<!-- Spring Cloud Stream, 用于MQ消息发送-->
		<dependency>
			<groupId>org.springframework.cloud</groupId>
			<artifactId>spring-cloud-stream</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework.cloud</groupId>
			<artifactId>spring-cloud-starter-stream-rabbit</artifactId>
		</dependency>

		<!-- dt framework-jdbc : jdbc + druid + mybatis -->
		<dependency>
			<groupId>com.dt</groupId>
			<artifactId>framework-jdbc</artifactId>
			<version>*******-SNAPSHOT</version>
		</dependency>

		<!-- Test -->
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-test</artifactId>
			<scope>test</scope>
		</dependency>
		<!-- dingding -->
		<dependency>
			<groupId>com.aliyun</groupId>
			<artifactId>alibaba-dingtalk-service-sdk</artifactId>
			<version>2.0.0</version>
		</dependency>
		<dependency>
			<groupId>org.projectlombok</groupId>
			<artifactId>lombok</artifactId>
			<optional>true</optional>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-devtools</artifactId>
			<optional>true</optional>
		</dependency>
		<dependency>
			<groupId>cn.hutool</groupId>
			<artifactId>hutool-all</artifactId>
			<version>5.6.5</version>
		</dependency>
		<dependency>
			<groupId>com.dingtalk.open</groupId>
			<artifactId>dingtalk-stream</artifactId>
			<version>1.3.7</version>
		</dependency>

	</dependencies>

	<build>
		<finalName>train-management-center</finalName>
		<plugins>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-compiler-plugin</artifactId>
				<version>3.8.1</version>
				<configuration>
					<source>1.8</source>
					<target>1.8</target>
					<annotationProcessorPaths>
						<path>
							<groupId>org.projectlombok</groupId>
							<artifactId>lombok</artifactId>
							<version>1.18.32</version>
						</path>
					</annotationProcessorPaths>
				</configuration>
			</plugin>
			<plugin>
				<groupId>org.springframework.boot</groupId>
				<artifactId>spring-boot-maven-plugin</artifactId>
				<executions>
					<execution>
						<goals>
							<goal>build-info</goal>
						</goals>
					</execution>
				</executions>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-jar-plugin</artifactId>
				<configuration>
					<excludes>
						<exclude>business.properties</exclude>
						<exclude>common.properties</exclude>
					</excludes>
				</configuration>
			</plugin>
			<plugin>
				<groupId>pl.project13.maven</groupId>
				<artifactId>git-commit-id-plugin</artifactId>
				<configuration>
					<failOnNoGitDirectory>false</failOnNoGitDirectory>
				</configuration>
			</plugin>
			<plugin>
				<groupId>org.mybatis.generator</groupId>
				<artifactId>mybatis-generator-maven-plugin</artifactId>
				<version>1.4.1</version>
				<configuration>
					<verbose>true</verbose>
					<overwrite>true</overwrite>
				</configuration>
				<dependencies>
					<dependency>
						<groupId>mysql</groupId>
						<artifactId>mysql-connector-java</artifactId>
						<version>5.1.46</version>
					</dependency>
					<dependency>
						<groupId>com.dt</groupId>
						<artifactId>mybatis-generator-config</artifactId>
						<version>2.0.0-SNAPSHOT</version>
					</dependency>
				</dependencies>
			</plugin>
		</plugins>
	</build>

	<repositories>
		<repository>
			<id>nexus-releases</id>
			<url>http://nexus.3ren.cn:8081/repository/maven-releases/</url>
		</repository>
		<repository>
			<id>nexus-snapshots</id>
			<url>http://nexus.3ren.cn:8081/repository/maven-snapshots/</url>
			<snapshots>
				<enabled>true</enabled>
				<updatePolicy>always</updatePolicy>
				<checksumPolicy>warn</checksumPolicy>
			</snapshots>
		</repository>
	</repositories>

	<pluginRepositories>
		<pluginRepository>
			<id>nexus-releases</id>
			<url>http://nexus.3ren.cn:8081/repository/maven-releases/</url>
		</pluginRepository>
		<pluginRepository>
			<id>nexus-snapshots</id>
			<url>http://nexus.3ren.cn:8081/repository/maven-snapshots/</url>
		</pluginRepository>
	</pluginRepositories>
</project>
